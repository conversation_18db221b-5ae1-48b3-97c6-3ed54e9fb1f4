package com.cet.pqscheduleservice.mapper.matterhorn;

import com.cet.pqscheduleservice.annotation.Batch;
import com.cet.pqscheduleservice.model.enums.BatchType;
import com.cet.pqscheduleservice.model.line.LineVO;
import com.cet.pqscheduleservice.model.status.PqHistoryStatus;
import com.cet.pqscheduleservice.model.status.PqMonitorStatus;
import com.cet.pqscheduleservice.model.status.PqStatus;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/11/29 13:26
 * @description
 */
public interface PqHistoryStatusMapper {

    List<PqStatus> queryPqStatus(Long dataId);

    Long queryHistoryStatusCount();

    List<PqStatus> queryAllPqStatus(Long dataId);

    @Batch(type = BatchType.INSERT, size = 1000)
    void insertPqHistoryStatus(List<PqHistoryStatus> pqHistoryStatuses);

    @Batch(type = BatchType.INSERT, size = 1000)
    void updateLineStatus(List<PqHistoryStatus> pqHistoryStatuses);

    @Batch(type = BatchType.QUERY, size = 1000)
    List<PqMonitorStatus> queryMonitorStatus();

    @Batch(type = BatchType.QUERY, size = 1000)
    List<PqHistoryStatus> queryHistoryStatus(List<PqHistoryStatus> pqHistoryStatuses);

    @Batch(type = BatchType.QUERY, size = 1000)
    List<Long> selectOfflineMonitorsLast5Days(List<Long> pqmonitorIds);

    @Batch(type = BatchType.INSERT, size = 1000)
    void updatePqHistoryStatus(@Param("idList")List<Long> idList, @Param("status")Integer status);

    @Batch(type = BatchType.QUERY, size = 1000)
    List<LineVO> queryMeterInfo();
}
