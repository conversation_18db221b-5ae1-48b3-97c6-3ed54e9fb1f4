package com.cet.pqscheduleservice.mapper.matterhorn;

import com.cet.pqscheduleservice.annotation.Batch;
import com.cet.pqscheduleservice.model.enums.BatchType;
import com.cet.pqscheduleservice.model.event.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PqEventMapper {

    Long queryEventStartId(Integer year);

    List<Measuredby> queryMeasureby();

    @Batch(type = BatchType.INSERT, size = 1000)
    void insertPqEventVariation(@Param("pqEventTransients") List<PqEventVariation> pqEventTransients);

    @Batch(type = BatchType.INSERT, size = 1000)
    void insertDeviceEventList(@Param("deviceEventList") List<DeviceEvent> deviceEventList);

    void updatePqEventPoi(@Param("year") Integer year,@Param("count") Long count);

    List<PqEventVariation> queryToleranceEvent();

    List<SemiScheme> querySemiScheme();

    List<IticScheme> queryIticScheme();

    @Batch(type = BatchType.INSERT, size = 1000)
    void insertSemiScheme(List<PqVariationEventSemiInfo> pqVariationEventSemiInfos);

    @Batch(type = BatchType.INSERT, size = 1000)
    void insertIticScheme(List<PqVariationEventItic> pqVariationEventItics);

    void updateSchemePoi(Long count);

    List<PqEventVariation> queryEvent(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    void updateEventCauseAnalysis(List<EventAnalysisDTO> pqEventVariations);
}
