package com.cet.pqscheduleservice.mapper.matterhorn;


import com.cet.pqscheduleservice.annotation.Batch;
import com.cet.pqscheduleservice.model.enums.BatchType;
import com.cet.pqscheduleservice.model.event.Measuredby;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ：MatterhornMapper
 * @date ：Created in 2021/2/23 14:09
 * @description： matterhorn数据查询
 */
public interface MatterhornMapper {

    /**
     * pqoverlimitdata表扩展
     * @return
     */
    @Batch(type = BatchType.QUERY, size = 1000)
    List<Measuredby> queryLineDevice();

    /**
     * pqoverlimitdata表扩展
     * @return
     */
     void callCreat_pqoverlimitdata();

    /**
     * pqquantityaggdata表扩展
     * @return
     */
    void callCreat_pqquantityaggdata();
    /**
     * call S_PQSTABLE1_1()
     * @return
     */
    void callS_PQSTABLE1_1();

    /**
     * call S_PQSTABLE1_2()
     * @return
     */
    void callS_PQSTABLE1_2();

    /**
     * call S_PQSTABLE1_3()
     * @return
     */
    void callS_PQSTABLE1_3();

    /**
     * call S_PQSTABLE1_4()
     * @return
     */
    void callS_PQSTABLE1_4();

    /**
     * call S_PQSTABLE2()
     * @return
     */
    void callS_PQSTABLE2();

    /**
     * call S_PQRMS()
     * @return
     */
    void callS_PQRMS();

    /**
     * call S_PQSTABLE_LASTMONTH()
     * @return
     */
    void callS_PQSTABLE_LASTMONTH();

    /**
     * call S_PQSTABLE1_1_TIME()
     * @return
     */
    void callS_PQSTABLE1_1_TIME();

    /**
     * call S_PQSTABLE1_2_TIME()
     * @return
     */
    void callS_PQSTABLE1_2_TIME();

    /**
     * call S_PQSTABLE1_3_TIME()
     * @return
     */
    void callS_PQSTABLE1_3_TIME();

    /**
     * call S_PQSTABLE1_4_TIME()
     * @return
     */
    void callS_PQSTABLE1_4_TIME();

    /**
     * call S_PQSTABLE2_1_TIME()
     * @return
     */
    void callS_PQSTABLE2_1_TIME();

    /**
     * call S_PQSTABLE2_2_TIME()
     * @return
     */
    void callS_PQSTABLE2_2_TIME();

    /**
     * call S_PQSTABLE2_3_TIME()
     * @return
     */
    void callS_PQSTABLE2_3_TIME();

    /**
     * call S_PQSTABLE2_4_TIME()
     * @return
     */
    void callS_PQSTABLE2_4_TIME();

    /**
     * call S_PQSTABLE1_LASTMONTH()
     * @return
     */
    void callADDPQPARTITIONS();

    /**
     * deletePqStatLog
     * @return
     */
    void deletePqStatLog(String date);
}
