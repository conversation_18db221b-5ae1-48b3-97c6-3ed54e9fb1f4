package com.cet.pqscheduleservice.mapper.data;

import com.cet.pqscheduleservice.annotation.Batch;
import com.cet.pqscheduleservice.model.enums.BatchType;
import com.cet.pqscheduleservice.model.event.EventVo;
import com.cet.pqscheduleservice.model.event.WaveData;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface DataMapper {

    @Batch(type = BatchType.QUERY, size = 1000)
    List<WaveData> queryWave(@Param("eventLogVos") List<EventVo> eventLogVos,@Param("tableName") String tableName);

    Long queryDeviceEventStartId(@Param("year") Integer year ,@Param("starttimestamp") LocalDateTime starttimestamp, @Param("endtimestamp") LocalDateTime endtimestamp);

    Integer queryDeviceEventCount(@Param("year") Integer year ,@Param("starttimestamp") LocalDateTime starttimestamp, @Param("endtimestamp") LocalDateTime endtimestamp);
}
