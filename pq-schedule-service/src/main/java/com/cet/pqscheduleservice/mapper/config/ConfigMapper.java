package com.cet.pqscheduleservice.mapper.config;

import com.cet.pqscheduleservice.model.config.PecConfig;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/27 9:50
 * @description
 */
public interface ConfigMapper {
    /**
     * 根据所有系统网络
     * @return
     */
    List<PecConfig> queryAllSystemNetwork();

    /**
     * 查询所有场站id
     * @return
     */
    List<Long> queryStationId();

    /**
     * 查询所有场站id对应的通道
     * @return
     */
    List<PecConfig> queryChannelId(List<Long> stationIds);
}
