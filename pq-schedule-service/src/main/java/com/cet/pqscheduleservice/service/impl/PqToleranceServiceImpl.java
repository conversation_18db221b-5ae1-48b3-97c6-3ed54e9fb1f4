package com.cet.pqscheduleservice.service.impl;

import com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper;
import com.cet.pqscheduleservice.model.enums.ToleranceBandEnum;
import com.cet.pqscheduleservice.model.event.*;
import com.cet.pqscheduleservice.service.PqToleranceService;
import com.cet.pqscheduleservice.utils.DateUtil;
import com.cet.pqscheduleservice.utils.ParseDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/6 9:19
 * @description
 */
@Service
@Slf4j
public class PqToleranceServiceImpl implements PqToleranceService {

    @Autowired
    private PqEventMapper pqEventMapper;

    @Override
    public Integer dealToleranceEvent() {
        //读取暂态事件
        List<PqEventVariation> pqEventVariations = pqEventMapper.queryToleranceEvent();
        if (CollectionUtils.isEmpty(pqEventVariations)) {
            log.info("no tolerance event update.");
            return 0;
        }
        List<SemiEvent> semiEvents = transformSemi(pqEventVariations);
        //处理semi暂态事件
        List<PqVariationEventSemiInfo> pqVariationEventSemiInfos = dealSemiToleranceBand(semiEvents);
        List<PqVariationEventItic> pqVariationEventItics = dealIticToleranceBand(semiEvents);
        //添加semi和itic事件
        if (!CollectionUtils.isEmpty(pqVariationEventSemiInfos)) {
            pqEventMapper.insertSemiScheme(pqVariationEventSemiInfos);
            log.info("semi info add {}.", pqVariationEventSemiInfos.size());
        }
        if (!CollectionUtils.isEmpty(pqVariationEventItics)) {
            pqEventMapper.insertIticScheme(pqVariationEventItics);
            log.info("itic info add {}.", pqVariationEventItics.size());
        }
        /*//更新poi
        Long maxId = pqEventVariations.stream().map(PqEventVariation::getId).max(Long::compareTo).orElse(0L);
        if (maxId != 0L) {
            pqEventMapper.updateSchemePoi(maxId);
        }*/
        return pqEventVariations.size();
    }

    private List<PqVariationEventItic> dealIticToleranceBand(List<SemiEvent> semiEvents) {
        List<PqVariationEventItic> pqVariationEventItics = new ArrayList<>();
        semiEvents.forEach(event -> {
            List<IticScheme> iticSchemeList = pqEventMapper.queryIticScheme();
            Map<Integer, List<IticScheme>> iticSchemeMap = iticSchemeList.stream().collect(Collectors.groupingBy(IticScheme::getIticScheme_id));
            Long duration = ParseDataUtil.parseLong(event.getDuration());
            Double magnitude = ParseDataUtil.parseDouble(event.getMag());
            iticSchemeMap.forEach((scheme, list) -> {
                //过滤上限曲线
                List<IticScheme> upList = list.stream().filter(itic -> itic.getIsup()).collect(Collectors.toList());
                //计算上限曲线duration最大左值
                IticScheme upMaxItem = upList.stream().filter(itic -> duration >= itic.getDuration())
                        .sorted(Comparator.comparing(IticScheme::getDuration, Comparator.reverseOrder()).thenComparing(IticScheme::getItemId, Comparator.reverseOrder()))
                        .findFirst().orElse(new IticScheme());
                //计算上限曲线duration最小右值
                IticScheme upMinItem = upList.stream().filter(itic -> duration <= itic.getDuration())
                        .sorted(Comparator.comparing(IticScheme::getDuration).thenComparing(IticScheme::getItemId))
                        .findFirst().orElse(new IticScheme());
                //过滤下限曲线
                List<IticScheme> downList = list.stream().filter(itic -> !itic.getIsup()).collect(Collectors.toList());
                //计算下限曲线duration最大左值
                IticScheme downMinItem = downList.stream().filter(itic -> duration >= itic.getDuration())
                        .sorted(Comparator.comparing(IticScheme::getDuration, Comparator.reverseOrder()).thenComparing(IticScheme::getItemId, Comparator.reverseOrder()))
                        .findFirst().orElse(new IticScheme());
                //计算下限曲线duration最小右值
                IticScheme  downMaxItem= downList.stream().filter(itic -> duration <= itic.getDuration())
                        .sorted(Comparator.comparing(IticScheme::getDuration).thenComparing(IticScheme::getItemId))
                        .findFirst().orElse(new IticScheme());
                //计算上限
                Double highlimit = 0D;
                if (upMinItem.getDuration() == upMaxItem.getDuration()) {
                    highlimit = upMinItem.getMagnitude();
                } else {
                    highlimit = upMaxItem.getMagnitude() + (upMinItem.getMagnitude() - upMaxItem.getMagnitude()) * (duration - upMaxItem.getDuration()) / (upMinItem.getDuration() - upMaxItem.getDuration());
                }
                //计算下限
                Double lowlimit = 0D;
                if (downMinItem.getDuration() == downMaxItem.getDuration()) {
                    lowlimit = downMinItem.getMagnitude();
                } else {
                    lowlimit = downMaxItem.getMagnitude() + (downMinItem.getMagnitude() - downMaxItem.getMagnitude()) * (duration - downMaxItem.getDuration()) / (downMinItem.getDuration() - downMaxItem.getDuration());
                }
                //容忍度判断
                Integer toleranceband = null;
                if (null == event.getDuration() || null == event.getMag() || event.getDuration() < 0 ||
                        event.getDuration() > 100000000 || event.getMag() < 0 || event.getMag() > 500) {
                    toleranceband = ToleranceBandEnum.NONE.getValue();
                } else {
                    if (event.getMag() > highlimit) {
                        //损坏区
                        toleranceband = ToleranceBandEnum.PROHIBITED.getValue();
                    } else if (event.getMag() <= lowlimit) {
                        //无损区
                        toleranceband = ToleranceBandEnum.NODAMAGE.getValue();
                    } else {
                        //容忍区
                        toleranceband = ToleranceBandEnum.TOLERANCE.getValue();
                    }
                }
                PqVariationEventItic iticInfo = new PqVariationEventItic();
                iticInfo.setPqvariationeventId(event.getEventId());
                iticInfo.setIticschemeId(scheme.longValue());
                iticInfo.setToleranceband(toleranceband);
                pqVariationEventItics.add(iticInfo);
            });
        });
        return pqVariationEventItics;
    }

    /**
     * @Description: 处理semi数据
     **/
    private List<PqVariationEventSemiInfo> dealSemiToleranceBand(List<SemiEvent> semiEvents) {
        List<PqVariationEventSemiInfo> pqVariationEventSemiInfos = new ArrayList<>();
        try {
            semiEvents.forEach(event -> {
                List<SemiScheme> semiSchemeList = pqEventMapper.querySemiScheme();
                Map<Integer, List<SemiScheme>> semiSchemeMap = semiSchemeList.stream().collect(Collectors.groupingBy(SemiScheme::getSemiScheme_id));
                Long duration = ParseDataUtil.parseLong(event.getDuration());
                Double magnitude = ParseDataUtil.parseDouble(event.getMag());
                semiSchemeMap.forEach((scheme, list) -> {
                    //计算duration最小右值
                    SemiScheme minItem = list.stream().filter(semi -> duration <= semi.getDuration())
                            .sorted(Comparator.comparing(SemiScheme::getDuration).thenComparing(SemiScheme::getItemId))
                            .findFirst().orElse(new SemiScheme());
                    //计算duration最大左值
                    SemiScheme maxItem = list.stream().filter(semi -> duration >= semi.getDuration())
                            .sorted(Comparator.comparing(SemiScheme::getDuration, Comparator.reverseOrder()).thenComparing(SemiScheme::getItemId, Comparator.reverseOrder()))
                            .findFirst().orElse(new SemiScheme());
                    //根据duration最大左值和最小右值及对应的幅值，计算出上限幅值
                    Double highlimit = 0D;
                    if (maxItem.getDuration() == minItem.getDuration()) {
                        highlimit = minItem.getMagnitude();
                    } else {
                        highlimit = maxItem.getMagnitude() + (minItem.getMagnitude() - maxItem.getMagnitude()) * (duration - maxItem.getDuration()) / (minItem.getDuration() - maxItem.getDuration());
                    }
                    //上限幅值和暂态事件的幅值进行比较
                    Integer toleranceband = null;
                    if (null == maxItem.getDuration() || null == minItem.getDuration() || null == event.getDuration() || null == event.getMag()
                            || event.getMag() > 100 || event.getDuration() > 1000000 || event.getDuration() < 50000) {
                        toleranceband = ToleranceBandEnum.NONE.getValue();
                    } else {
                        if (event.getMag() >= highlimit) {
                            //容忍区
                            toleranceband = ToleranceBandEnum.TOLERANCE.getValue();
                        } else {
                            //损坏区
                            toleranceband = ToleranceBandEnum.PROHIBITED.getValue();
                        }
                    }
                    PqVariationEventSemiInfo semiInfo = new PqVariationEventSemiInfo();
                    semiInfo.setPqvariationeventId(event.getEventId());
                    semiInfo.setSemischemeId(scheme.longValue());
                    semiInfo.setToleranceband(toleranceband);
                    pqVariationEventSemiInfos.add(semiInfo);
                });
            });
        } catch (Exception e) {
            log.error("deal semi event error.", e);
        }
        return pqVariationEventSemiInfos;
    }

    /**
     * @Description: 转semi事件
     **/
    private List<SemiEvent> transformSemi(List<PqEventVariation> pqEventVariations) {
        List<SemiEvent> semiEvents = new ArrayList<>();
        pqEventVariations.forEach(t -> {
            if (t.getMagnitude() != null && t.getDuration() != null) {
                SemiEvent semiEvent = new SemiEvent();
                semiEvent.setEventId(t.getId());
                semiEvent.setDeviceId(t.getDeviceid());
                semiEvent.setDuration(t.getDuration());
                semiEvent.setMag(t.getMagnitude());
                semiEvents.add(semiEvent);
            }
        });
        return semiEvents;
    }
}
