package com.cet.pqscheduleservice.service.impl;

import com.cet.pqscheduleservice.mapper.matterhorn.MatterhornMapper;
import com.cet.pqscheduleservice.model.common.Result;
import com.cet.pqscheduleservice.service.ProcedureService;
import com.cet.pqscheduleservice.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/7/19 16:40
 * @description
 */
@Service
@Slf4j
public class ProcedureServiceImpl implements ProcedureService {

    @Autowired
    private MatterhornMapper matterhornMapper;

    @Override
    public Result callPqStable() {
        try {
            log.info("start call S_PQRMS");
            matterhornMapper.callS_PQRMS();
            log.info("end call S_PQRMS");
        } catch (Exception e) {
            log.error("error in S_PQRMS", e);
        }

        try {
            log.info("start call S_PQSTABLE2");
            matterhornMapper.callS_PQSTABLE2();
            log.info("end call S_PQSTABLE2");
        } catch (Exception e) {
            log.error("error in S_PQSTABLE2", e);
        }

        try {
            log.info("start call S_PQSTABLE1_1");
            matterhornMapper.callS_PQSTABLE1_1();
            log.info("end call S_PQSTABLE1_1");
        } catch (Exception e) {
            log.error("error in S_PQSTABLE1_1", e);
        }


        try {
            log.info("start call S_PQSTABLE1_2");
            matterhornMapper.callS_PQSTABLE1_2();
            log.info("end call S_PQSTABLE1_2");
        } catch (Exception e) {
            log.error("error in S_PQSTABLE1_2", e);
        }

        try {
            log.info("start call S_PQSTABLE1_3");
            matterhornMapper.callS_PQSTABLE1_3();
            log.info("end call S_PQSTABLE1_3");
        } catch (Exception e) {
            log.error("error in S_PQSTABLE1_3", e);
        }

        try {
            log.info("start call S_PQSTABLE1_4");
            matterhornMapper.callS_PQSTABLE1_4();
            log.info("end call S_PQSTABLE1_4");
        } catch (Exception e) {
            log.error("error in S_PQSTABLE1_4", e);
        }

        return Result.success();
    }

    @Override
    public Result callMonthPqStable() {

        try {
            log.info("start deletePqStatLog");
            String lastDayOfMonth = DateUtil.getLastDayOfMonth(new Date());
            matterhornMapper.deletePqStatLog(lastDayOfMonth);
            log.info("end deletePqStatLog");
        } catch (Exception e) {
            log.error("error in deletePqStatLog", e);
        }

        try {
            log.info("start call callS_PQSTABLE_LASTMONTH");
            matterhornMapper.callS_PQSTABLE_LASTMONTH();
            log.info("end call callS_PQSTABLE_LASTMONTH");
        } catch (Exception e) {
            log.error("error in callS_PQSTABLE_LASTMONTH", e);
        }

        try {
            log.info("start call callADDPQPARTITIONS");
            matterhornMapper.callADDPQPARTITIONS();
            log.info("end call callADDPQPARTITIONS");
        } catch (Exception e) {
            log.error("error in callADDPQPARTITIONS", e);
        }
        return Result.success();
    }
}
