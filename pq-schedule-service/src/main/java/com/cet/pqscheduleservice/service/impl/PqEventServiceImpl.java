package com.cet.pqscheduleservice.service.impl;

import Jama.Matrix;
import Jama.SingularValueDecomposition;
import com.cet.pqscheduleservice.feign.DeviceDataService;
import com.cet.pqscheduleservice.mapper.data.DataMapper;
import com.cet.pqscheduleservice.mapper.matterhorn.InterferenceMapper;
import com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper;
import com.cet.pqscheduleservice.model.algorithmUtil.Compx;
import com.cet.pqscheduleservice.model.algorithmUtil.DefUtil;
import com.cet.pqscheduleservice.model.common.Result;
import com.cet.pqscheduleservice.model.common.ResultWithTotal;
import com.cet.pqscheduleservice.model.datalog.EventCondition;
import com.cet.pqscheduleservice.model.datalog.TrendSearchListVo;
import com.cet.pqscheduleservice.model.datalog.TrendSearchVo;
import com.cet.pqscheduleservice.model.enums.CauseAnalysisEnum;
import com.cet.pqscheduleservice.model.event.*;
import com.cet.pqscheduleservice.model.fault.LineFaultEnum;
import com.cet.pqscheduleservice.model.fault.RmsResult;
import com.cet.pqscheduleservice.model.query.ConditionBlock;
import com.cet.pqscheduleservice.model.waveinfo.*;
import com.cet.pqscheduleservice.service.PqEventService;
import com.cet.pqscheduleservice.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/30 8:49
 * @description
 */
@Slf4j
@Service
public class PqEventServiceImpl implements PqEventService {

    @Autowired
    private PqEventMapper pqEventMapper;

    @Autowired
    private DataMapper dataMapper;

    @Autowired
    private DeviceDataService deviceDataService;

    @Autowired
    private InterferenceMapper interferenceMapper;

    @Value(value = "${interference.ifescape:false}")
    private Boolean ifescape;

    @Value(value = "${event.aireasonenabled}")
    private Boolean eventAIReasonEnabled;

    @Value(value = "${event.faultreasonenabled:false}")
    private Boolean faultReasonEnabled;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final String ERROR_MES = "eveStr1 analysis error. ";
    // 判断故障类型阈值
    private final static double EPSILON_1_COEFFICIENT = 0.3d;
    private final static double EPSILON_2 = 1.4d;
    private final static double EPSILON_3 = 0.1d;

    // 置信度
    private final static double CONFIDENCE_LEVEL_1 = 92.27;

    private final static double CONFIDENCE_LEVEL_2 = 89.53;

    private final static double CONFIDENCE_LEVEL_3 = 76.23;

    private final static double CONFIDENCE_LEVEL_4 = 94.15;

    private final static double CONFIDENCE_LEVEL_5 = 90.44;

    private final static double CONFIDENCE_LEVEL_6 = 84.29;

    // 周波20ms
    private final static int CYCLE = 20;

    private final static double diff = 1e-6F;;

    @Override
    public Integer dealPqEvent() {
        log.info("start deal pq event.");
        List<EventVo> allEventVos = new ArrayList<>();
        //查询最近两年的事件
        for (int i = 0; i < 2; i++) {
            Integer year = DateUtil.getYear(new Date()) - i;
            //查询poi，统计最近两年数据
            List<EventVo> eventVos = readEventVo(year);
            if (CollectionUtils.isEmpty(eventVos)) {
                log.info("{} year no event update.", year);
                continue;
            }
            // 处理17暂态事件
            List<PqEventVariation> pqEventTransients = new ArrayList<>();
            dealEvent17(eventVos, pqEventTransients);
            // 处理18暂态事件
            dealEvent18(eventVos, pqEventTransients);
            //设置容忍度
            List<PqEventVariation> pqEventVariationList = dealEventToleranceband(pqEventTransients);
            // 计算原因分析
            if(Boolean.TRUE.equals(eventAIReasonEnabled)){
                calcEventAnalysis(pqEventVariationList);
            }
            //添加暂态事件
            if (!CollectionUtils.isEmpty(pqEventVariationList)) {
                try {
                    pqEventMapper.insertPqEventVariation(pqEventVariationList);
                    log.info("insert pqevent count {}.", pqEventVariationList.size());
                } catch (Exception e) {
                    log.error("insert event error.", e);
                }
            }
            //更新poi
            Long maxId = eventVos.stream().map(EventVo::getId).max(Long::compareTo).orElse(0L);
            if (maxId != 0L) {
                pqEventMapper.updatePqEventPoi(year, maxId);
            }
            allEventVos.addAll(eventVos);
        }
        return allEventVos.size();
    }

    private void calcEventAnalysis(List<PqEventVariation> pqEventTransients) {
        pqEventTransients.forEach(event ->{
            try {
                singleEventCauseAnalysis(event);
            }catch (Exception e){
                log.error("监测点deviceId:{}在{}发生事件原因分析计算失败，异常{}", event.getDeviceid(),event.getEventtime(),e);
            }
        });
    }

    @Override
    public Integer dealDeviceEvent( Integer year) {
        // year == null,算前一天数据, 不为null，算所有数据
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        if (ObjectUtils.isEmpty(year)) {
            year = DateUtil.getFrontDayYear();
             startTime = LocalDate.now()
                    .minusDays(1)          // 昨天开始
                    .atStartOfDay();

             endTime = LocalDate.now()
                    .atStartOfDay();
        }

        List<EventVo> allEventVos = new ArrayList<>();
        //查询事件
        List<EventVo> eventVos = new ArrayList<>();
        // 查询指定时间范围第一个事件id
        Long startId = dataMapper.queryDeviceEventStartId(year, startTime, endTime);

        // 查询指定时间范围事件总数
        Integer totalCount = dataMapper.queryDeviceEventCount(year, startTime, endTime);
        log.info("eventType 100 have totalCount {}" , totalCount);
        EventCondition condition = new EventCondition();
        condition.setEventTypes(Arrays.asList(100));

        int batchSize = 10000;  //每次处理
        int batches = (totalCount + batchSize - 1) / batchSize; //计算总批次数

        // 设备id - 监测点id
        List<Measuredby> measuredbies = pqEventMapper.queryMeasureby();
        Map<Long, Long> mointorMap = new HashMap<>();
        measuredbies.forEach(t -> mointorMap.put(t.getMeasuredBy(), t.getMonitoredId()));
        // 两个时间对应一个波形 - 发生可能不在同1s，波形时间与事件开始时间一致
        for (int i = 0; i < batches; i++) {
            ResultWithTotal<List<EventVo>> eventResult = deviceDataService.queryEventById(startId, null, i, batchSize, year, condition);
            if (eventResult.getCode() == 0 || !CollectionUtils.isEmpty(eventResult.getData())) {
                List<EventVo> eventLogVos = eventResult.getData();
                String tableName = "PD_TB_09_" + year;
                List<WaveData> waveDataList = dataMapper.queryWave(eventLogVos, tableName);
                AtomicInteger count = new AtomicInteger(0);
                eventLogVos.forEach(eventVo -> {
                    waveDataList.forEach(waveData -> {
                        long eventLogtime = eventVo.getEventTime() + eventVo.getMsec();
                        long waveLogtime = waveData.getLogTime().getTime() + waveData.getMsec();
                        // 事件开始时间和事件时间一致
                        if (eventLogtime == waveLogtime) {
                            count.getAndIncrement();
                        }
                    });
                    // 合并后的数据
                    eventVos.add(eventVo);
                });
                log.info("read event and match wave count {}.", count.get());
            }
            if (CollectionUtils.isEmpty(eventVos)) {
                log.info("no event update.");
                continue;
            }
            // 处理100暂态事件
            List<DeviceEvent> deviceEventList = new ArrayList<>();
            dealEvent100(eventVos,deviceEventList,mointorMap);

            //添加暂态事件
            if (!CollectionUtils.isEmpty(deviceEventList)) {
                pqEventMapper.insertDeviceEventList(deviceEventList);
                log.info("insert pqevent count {}.", deviceEventList.size());
            }
            allEventVos.addAll(eventVos);
        }
        return allEventVos.size();
    }

    /**
     * @Description: 事件原因分析判断
     * @param logTime
     */
    @Override
    public void dealEventCauseAnalysis(Long logTime) {
        Long startTime = null;
        Long endTime = null;
        if(Objects.isNull(logTime)){
            //计算昨日零点开始时间
            startTime = DateUtil.getPreFirstTimeOfDay();
            endTime = DateUtil.getFirstTimeOfDay(new Date()).getTime();
        }else {
            startTime = logTime;
            endTime = logTime + 24 * 60 * 60 * 1000;
        }
        // 查询暂态事件
        List<PqEventVariation> pqEventVariations = pqEventMapper.queryEvent(startTime, endTime);
        pqEventVariations.forEach(event ->{
            try {
                singleEventCauseAnalysis(event);
            }catch (Exception e){
                log.info("监测点deviceId:{}在{}发生事件原因分析计算失败",event.getDeviceid(),sdf.format(new Date(event.getEventtime())),e);
            }
        });
        List<EventAnalysisDTO> eventAnalysisList = pqEventVariations.stream().filter(event -> Objects.nonNull(event.getReason())).map(event ->
                new EventAnalysisDTO(event.getId(), event.getReason())
        ).collect(Collectors.toList());

        // 更新事件原因分析
        if (!CollectionUtils.isEmpty(eventAnalysisList)) {
            pqEventMapper.updateEventCauseAnalysis(eventAnalysisList);
        }
        log.info("event cause analysis add {}.", eventAnalysisList.size());
    }

    /**
     * @Description: 处理容忍度
     **/
    private List<PqEventVariation> dealEventToleranceband(List<PqEventVariation> pqEventTransients) {
        List<PqEventVariation> pqEventVariationList = new ArrayList<>();
        List<Measuredby> measuredbies = pqEventMapper.queryMeasureby();
        Map<Long, Long> mointorMap = new HashMap<>();
        measuredbies.forEach(t -> {
            mointorMap.put(t.getMeasuredBy(), t.getMonitoredId());
        });
        List<Long> windLineIds = new ArrayList<>();
        List<Long> photoLineIds1 = new ArrayList<>();
        List<Long> photoLineIds2 = new ArrayList<>();
        if (ifescape) {
            //获取风电专项监测点集合
            windLineIds.addAll(interferenceMapper.getWindLines());
            //获取光伏专项380V及10kV以下点集合
            photoLineIds1.addAll(interferenceMapper.getPhotoLines(Arrays.asList(2, 8, 9)));
            //获取光伏专项其他点集合
            photoLineIds2.addAll(interferenceMapper.getPhotoLines(Arrays.asList(3, 4, 5, 6, 10, 12, 14, 15, 17)));
        }
        pqEventTransients.forEach(pqEventVariation -> {
            if (pqEventVariation.getWaveformlogtime() == null) {
                log.info("event not related wave,event id {}.", pqEventVariation.getId());
                return;
            }
            //获取容忍度
            getToleranceband(pqEventVariation);
            pqEventVariation.setValid(true);
            Long lineId = mointorMap.get(pqEventVariation.getDeviceid());
            //MEASUREDBY 没有匹配到监测点
            if (ParseDataUtil.parseLong(lineId) == 0L) {
                log.info("event not related monitor, event id {}, device id {}.", pqEventVariation.getId(), pqEventVariation.getDeviceid());
                return;
            }
            //判断是否需要计算脱网状态
            if (ifescape) {
                //处理风电场监测点和部分光伏监测点脱网状态
                if (windLineIds.contains(lineId)) {
                    dealWindEscapeEvent(pqEventVariation);
                }
                //处理光伏(10kV，35kV及以上)监测点脱网状态
                if (photoLineIds2.contains(lineId)) {
                    dealPhotoEscapeEvent(pqEventVariation);
                }
                //获取光伏专项380V及10kV以下点集合
                if (photoLineIds1.contains(lineId)) {
                    dealPhotoEscapeEvent380V(pqEventVariation);
                }
            }
            pqEventVariation.setMonitoredid(lineId);
            pqEventVariation.setMonitoredlabel("line");
            pqEventVariation.setWaveformlogtime(pqEventVariation.getWaveformlogtime());
            pqEventVariation.setUpdatetime(System.currentTimeMillis());
            pqEventVariation.setConfirmeventstatus(1);
//            pqEventVariation.setIsminuteextreme(true);
            pqEventVariationList.add(pqEventVariation);
        });
        return pqEventVariationList;
    }

    /**
     * @Description: 处理暂态18事件
     **/
    private void dealEvent18(List<EventVo> eventVos, List<PqEventVariation> pqEventTransients) {
        // 湛江配网增加暂态事件code2=3
        List<EventVo> eventVoList = eventVos.stream().filter(t -> t.getEventType() == 18 && t.getCode2()!= 3).collect(Collectors.toList());
        List<EventVo> eventVoListCode2_3 = eventVos.stream().filter(t -> t.getEventType() == 18 && t.getCode2()== 3).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eventVoList) && CollectionUtils.isEmpty(eventVoListCode2_3)) {
            log.info("no event 18 data.");
            return;
        }
        if(!CollectionUtils.isEmpty(eventVoList)){
            eventVoList.sort(Comparator.comparing(EventVo::getId));
            Map<String, PqEventVariation> pqEventServiceMap = new HashMap<>();
            for (EventVo pqEvent : eventVoList) {
                try {
                    PqEventVariation pqEventVariation = new PqEventVariation();
                    Integer transientFaultDirection = 1;
                    copyProperties18(pqEvent, pqEventVariation);
                    String eveStr1 = pqEvent.getEveStr1();
                    String key = pqEvent.getDeviceId() + ":" + pqEvent.getEventType() + ":" + pqEvent.getEventByte();
                    PqEventVariation startPqEventVariation = pqEventServiceMap.get(key);
                    //判断中间几条是否为暂态启动事件
                    if (startPqEventVariation != null && startPqEventVariation.getWaveformlogtime() != null && StringUtils.isEmpty(eveStr1)) {
                        continue;
                    } else if (!pqEventServiceMap.containsKey(key) && StringUtils.isEmpty(eveStr1) && pqEventVariation.getWaveformlogtime() != null) {
                        //暂态事件两条记录，保存组装暂态事件开始时间和结束时间,描述信息
                        PqEventVariation eventVariation = new PqEventVariation();
                        eventVariation.setEventid(pqEventVariation.getId());
                        eventVariation.setEventtime(pqEventVariation.getEventtime());
                        eventVariation.setWaveformlogtime(pqEventVariation.getWaveformlogtime());
                        eventVariation.setDescription(pqEventVariation.getDescription());
                        pqEventServiceMap.put(key, eventVariation);
                    } else if (!StringUtils.isEmpty(eveStr1) && startPqEventVariation != null && startPqEventVariation.getWaveformlogtime() != null) {
                        //eveStr1值为 ;59109.000000;100.000000;17.160000;100.000000;;;82.460000;100.000000;;;70.450000;100.000000;;
                        // magnitude;duration;nominalvoltage;v1min;v1max;v1avg;;v2min;v2max;v2avg;;v3min;v3max;;
                        int length = eveStr1.split(";").length;
                        if (length == 2) {
                            //南网事件驱动转存
                            convertFileEvent(pqEventTransients, pqEvent, eveStr1, pqEventVariation, transientFaultDirection, startPqEventVariation);
                            pqEventServiceMap.remove(key);
                        } else {
                            //modbus驱动转存
                            modebusEvent(pqEventTransients, pqEvent, eveStr1, pqEventVariation, transientFaultDirection, startPqEventVariation);
                            pqEventServiceMap.remove(key);
                        }
                    }
                } catch (Exception e) {
                    log.error("dealEvent18 error, ", e);
                }
            }
        }
        // code2==3
        if (!CollectionUtils.isEmpty(eventVoListCode2_3)) {
            eventVoListCode2_3.sort(Comparator.comparing(EventVo::getId));
            for (EventVo pqEvent : eventVoListCode2_3) {
                try {
                    PqEventVariation pqEventVariation = new PqEventVariation();
                    Integer transientFaultDirection = 1;
                    copyProperties18(pqEvent, pqEventVariation);
                    String eveStr1 = pqEvent.getEveStr1();

                    //eveStr1值为 ;59109.000000;100.000000;17.160000;100.000000;;;82.460000;100.000000;;;70.450000;100.000000;;
                    // magnitude;duration;nominalvoltage;v1min;v1max;v1avg;;v2min;v2max;v2avg;;v3min;v3max;;
                    String newEveStr1 = eveStr1.replaceAll(";", "0,");
                    String[] eveStr1Split = newEveStr1.split(",");
                    Double magnitude = ParseDataUtil.parseDouble(eveStr1Split[0]);
                    Long duration = ParseDataUtil.parseDouble(ParseDataUtil.parseDouble(eveStr1Split[1]) * 1000).longValue();
                    Double nominalvoltage = ParseDataUtil.parseDouble(eveStr1Split[2]);
                    Double v1min = ParseDataUtil.parseDouble(eveStr1Split[3]);
                    Double v1max = ParseDataUtil.parseDouble(eveStr1Split[4]);
                    Double v1avg = ParseDataUtil.parseDouble(eveStr1Split[5]);
                    Double v1magnitude = v1min;
                    if (Math.abs(v1max - 100) > Math.abs(v1min - 100)) {
                        v1magnitude = v1max;
                    }
                    Double v2min = ParseDataUtil.parseDouble(eveStr1Split[7]);
                    Double v2max = ParseDataUtil.parseDouble(eveStr1Split[8]);
//                    Double v2avg = ParseDataUtil.parseDouble(eveStr1Split[9]);
                    Double v2magnitude = v2min;
                    if (Math.abs(v2max - 100) > Math.abs(v2min - 100)) {
                        v2magnitude = v2max;
                    }
                    Double v3min = ParseDataUtil.parseDouble(eveStr1Split[11]);
                    Double v3max = ParseDataUtil.parseDouble(eveStr1Split[12]);
//                    Double v3avg = ParseDataUtil.parseDouble(eveStr1Split[13]);
                    Double v3magnitude = v3min;
                    if (Math.abs(v3max - 100) > Math.abs(v3min - 100)) {
                        v3magnitude = v3max;
                    }
                    Boolean v1trigger = false, v2trigger = false, v3trigger = false;
                    Long v1duration = null, v2duration = null, v3duration = null;
                    if (Math.abs(v1magnitude) > 0D) {
                        magnitude = v1magnitude;
                        v1trigger = true;
                        v2trigger = false;
                        v3trigger = false;
                    }
                    if (Math.abs(v2magnitude) > 0D && Math.abs(v2magnitude - 100) > Math.abs(magnitude - 100)) {
                        magnitude = v2magnitude;
                        v2duration = duration;
                        v1trigger = false;
                        v2trigger = true;
                        v3trigger = false;
                    }
                    if (Math.abs(v3magnitude) > 0D && Math.abs(v3magnitude - 100) > Math.abs(magnitude - 100)) {
                        magnitude = v3magnitude;
                        v3duration = duration;
                        v1trigger = false;
                        v2trigger = false;
                        v3trigger = true;
                    }
                    // 幅值超过10000，需要除以100
                    if (magnitude >= 10000) {
                        magnitude = magnitude / 100;
                    }
                    //根据eventbyte重新设置触发相
                    Integer eventByte = pqEvent.getEventByte();
                    if (eventByte == 1) {
                        v1trigger = true;
                        v2trigger = false;
                        v3trigger = false;
                    } else if (eventByte == 2) {
                        v1trigger = false;
                        v2trigger = true;
                        v3trigger = false;
                    } else if (eventByte == 3) {
                        v1trigger = false;
                        v2trigger = false;
                        v3trigger = true;
                    }
                    //计算pqvariationeventtype
                    //默认为其他
                    final Integer pqvariationeventtype = dealPqEventType(pqEvent, duration, magnitude);
                    if (magnitude < diff && duration == 0) {
                        log.error(ERROR_MES);
                        return;
                    }
                    pqEventVariation.setNominalvoltage(nominalvoltage);
                    pqEventVariation.setV1duration(v1duration);
                    pqEventVariation.setV1magnitude(v1magnitude);
                    pqEventVariation.setV2duration(v2duration);
                    pqEventVariation.setV2magnitude(v2magnitude);
                    pqEventVariation.setV3duration(v3duration);
                    pqEventVariation.setV3magnitude(v3magnitude);
                    pqEventVariation.setMagnitude(magnitude);
                    pqEventVariation.setDuration(duration);
                    pqEventVariation.setV1trigger(v1trigger);
                    pqEventVariation.setV2trigger(v2trigger);
                    pqEventVariation.setV3trigger(v3trigger);
                    pqEventVariation.setTransientfaultdirection(transientFaultDirection);
                    pqEventVariation.setEventendtime(pqEventVariation.getEventtime());
                    pqEventVariation.setPqvariationeventtype(pqvariationeventtype);

                    List<PqEventData> pqData18 = queryPqEventVariation(Collections.singletonList(pqEventVariation));
                    convertPqRms(Collections.singletonList(pqEventVariation), pqData18);
                    pqEventTransients.add(pqEventVariation);

                } catch (Exception e) {
                    log.error("dealEvent18 error, ", e);
                }
            }
        }
    }

    private void convertFileEvent(List<PqEventVariation> pqEventTransients, EventVo pqEvent, String eveStr1,
                              PqEventVariation pqEventVariation, Integer transientFaultDirection,
                              PqEventVariation startPqEventVariation) {
        String[] eveStr1Split = eveStr1.split(";");
        Double magnitude = ParseDataUtil.parseDouble(eveStr1Split[0]);
        Long duration = ParseDataUtil.parseDouble(ParseDataUtil.parseDouble(eveStr1Split[1]) * 1000).longValue();
        Double nominalvoltage = 0d;
        Double v1min = 0d;
        Double v1max = 0d;
        Double v1magnitude = v1min;
        if (Math.abs(v1max - 100) > Math.abs(v1min - 100)) {
            v1magnitude = v1max;
        }
        Double v2min = 0d;
        Double v2max = 0d;
        Double v2magnitude = v2min;
        if (Math.abs(v2max - 100) > Math.abs(v2min - 100)) {
            v2magnitude = v2max;
        }
        Double v3min = 0d;
        Double v3max = 0d;
        Double v3magnitude = v3min;
        if (Math.abs(v3max - 100) > Math.abs(v3min - 100)) {
            v3magnitude = v3max;
        }
        Boolean v1trigger = false;
        Boolean v2trigger = false;
        Boolean v3trigger = false;
        Long v1duration = null;
        Long v2duration = null;
        Long v3duration = null;
        //根据eventbyte重新设置触发相
        Integer eventByte = pqEvent.getEventByte();
        if (eventByte == 1) {
            v1trigger = true;
            v2trigger = false;
            v3trigger = false;
        } else if (eventByte == 2) {
            v1trigger = false;
            v2trigger = true;
            v3trigger = false;
        } else if (eventByte == 3) {
            v1trigger = false;
            v2trigger = false;
            v3trigger = true;
        }
        //计算pqvariationeventtype
        //默认为其他
        final Integer pqvariationeventtype = dealPqEventType(pqEvent, duration, magnitude);
        if (magnitude < diff && duration == 0) {
            log.error(ERROR_MES);
            return;
        }
        pqEventVariation.setNominalvoltage(nominalvoltage);
        pqEventVariation.setV1duration(v1duration);
        pqEventVariation.setV1magnitude(v1magnitude);
        pqEventVariation.setV2duration(v2duration);
        pqEventVariation.setV2magnitude(v2magnitude);
        pqEventVariation.setV3duration(v3duration);
        pqEventVariation.setV3magnitude(v3magnitude);
        pqEventVariation.setMagnitude(magnitude);
        pqEventVariation.setDuration(duration);
        pqEventVariation.setV1trigger(v1trigger);
        pqEventVariation.setV2trigger(v2trigger);
        pqEventVariation.setV3trigger(v3trigger);
        pqEventVariation.setTransientfaultdirection(transientFaultDirection);
        pqEventVariation.setEventendtime(pqEventVariation.getEventtime());
        pqEventVariation.setPqvariationeventtype(pqvariationeventtype);
        //设置事件id和时间
        pqEventVariation.setEventid(startPqEventVariation.getEventid());
        pqEventVariation.setEventtime(startPqEventVariation.getEventtime());
        pqEventVariation.setWaveformlogtime(startPqEventVariation.getWaveformlogtime());
        pqEventVariation.setDescription(startPqEventVariation.getDescription());
        List<PqEventData> pqData18 = queryPqEventVariation(Collections.singletonList(pqEventVariation));
        convertPqRms(Collections.singletonList(pqEventVariation), pqData18);
        pqEventTransients.add(pqEventVariation);
    }
    private void modebusEvent(List<PqEventVariation> pqEventTransients, EventVo pqEvent, String eveStr1, PqEventVariation pqEventVariation, Integer transientFaultDirection, PqEventVariation startPqEventVariation) {
        String newEveStr1 = eveStr1.replaceAll(";", "0,");
        String[] eveStr1Split = newEveStr1.split(",");
        Double magnitude = ParseDataUtil.parseDouble(eveStr1Split[0]);
        Long duration = ParseDataUtil.parseDouble(ParseDataUtil.parseDouble(eveStr1Split[1]) * 1000).longValue();
        Double nominalvoltage = ParseDataUtil.parseDouble(eveStr1Split[2]);
        Double v1min = ParseDataUtil.parseDouble(eveStr1Split[3]);
        Double v1max = ParseDataUtil.parseDouble(eveStr1Split[4]);
        Double v1avg = ParseDataUtil.parseDouble(eveStr1Split[5]);
        Double v1magnitude = v1min;
        if (Math.abs(v1max - 100) > Math.abs(v1min - 100)) {
            v1magnitude = v1max;
        }
        Double v2min = ParseDataUtil.parseDouble(eveStr1Split[7]);
        Double v2max = ParseDataUtil.parseDouble(eveStr1Split[8]);
//                    Double v2avg = ParseDataUtil.parseDouble(eveStr1Split[9]);
        Double v2magnitude = v2min;
        if (Math.abs(v2max - 100) > Math.abs(v2min - 100)) {
            v2magnitude = v2max;
        }
        Double v3min = ParseDataUtil.parseDouble(eveStr1Split[11]);
        Double v3max = ParseDataUtil.parseDouble(eveStr1Split[12]);
//                    Double v3avg = ParseDataUtil.parseDouble(eveStr1Split[13]);
        Double v3magnitude = v3min;
        if (Math.abs(v3max - 100) > Math.abs(v3min - 100)) {
            v3magnitude = v3max;
        }
        Boolean v1trigger = false;
        Boolean v2trigger = false;
        Boolean v3trigger = false;
        Long v1duration = null;
        Long v2duration = null;
        Long v3duration = null;
        if (Math.abs(v1magnitude) > 0D) {
            magnitude = v1magnitude;
            v1trigger = true;
            v2trigger = false;
            v3trigger = false;
        }
        if (Math.abs(v2magnitude) > 0D && Math.abs(v2magnitude - 100) > Math.abs(magnitude - 100)) {
            magnitude = v2magnitude;
            v2duration = duration;
            v1trigger = false;
            v2trigger = true;
            v3trigger = false;
        }
        if (Math.abs(v3magnitude) > 0D && Math.abs(v3magnitude - 100) > Math.abs(magnitude - 100)) {
            magnitude = v3magnitude;
            v3duration = duration;
            v1trigger = false;
            v2trigger = false;
            v3trigger = true;
        }
        // 幅值超过10000，需要除以100
        if (magnitude >= 10000) {
            magnitude = magnitude / 100;
        }
        //根据eventbyte重新设置触发相
        Integer eventByte = pqEvent.getEventByte();
        if (eventByte == 1) {
            v1trigger = true;
            v2trigger = false;
            v3trigger = false;
        } else if (eventByte == 2) {
            v1trigger = false;
            v2trigger = true;
            v3trigger = false;
        } else if (eventByte == 3) {
            v1trigger = false;
            v2trigger = false;
            v3trigger = true;
        }
        final Integer pqvariationeventtype = dealPqEventType(pqEvent, duration, magnitude);
        pqEventVariation.setV1trigger(v1trigger);
        pqEventVariation.setV2trigger(v2trigger);
        pqEventVariation.setV3trigger(v3trigger);
        pqEventVariation.setNominalvoltage(nominalvoltage);
        pqEventVariation.setV1duration(v1duration);
        pqEventVariation.setV1magnitude(v1magnitude);
        pqEventVariation.setV2duration(v2duration);
        pqEventVariation.setV2magnitude(v2magnitude);
        pqEventVariation.setV3duration(v3duration);
        pqEventVariation.setV3magnitude(v3magnitude);
        pqEventVariation.setMagnitude(magnitude);
        pqEventVariation.setDuration(duration);

        pqEventVariation.setTransientfaultdirection(transientFaultDirection);
        pqEventVariation.setEventendtime(pqEventVariation.getEventtime());
        pqEventVariation.setPqvariationeventtype(pqvariationeventtype);
        //设置事件id和时间
        pqEventVariation.setEventid(startPqEventVariation.getEventid());
        pqEventVariation.setEventtime(startPqEventVariation.getEventtime());
        pqEventVariation.setWaveformlogtime(startPqEventVariation.getWaveformlogtime());
        pqEventVariation.setDescription(startPqEventVariation.getDescription());
        List<PqEventData> pqData18 = queryPqEventVariation(Collections.singletonList(pqEventVariation));
        convertPqRms(Collections.singletonList(pqEventVariation), pqData18);
        pqEventTransients.add(pqEventVariation);
    }

    private static Integer dealPqEventType(EventVo pqEvent, Long duration, Double magnitude) {
        //计算pqvariationeventtype
        //默认为其他
        Integer pqvariationeventtype = 9;
        if (pqEvent.getCode1() == 23) {
            //暂态启动
            pqvariationeventtype = 8;
        } else if (duration >= 10000 && duration <= 60000000) {
            pqvariationeventtype = getPqvariationeventtype(magnitude, pqvariationeventtype);
        } else if (duration > 60000000) {
            pqvariationeventtype = dealPqvariationeventtype(magnitude, pqvariationeventtype);
        }
        return pqvariationeventtype;
    }

    private static Integer dealPqvariationeventtype(Double magnitude, Integer pqvariationeventtype) {
        if (magnitude >= 0 && magnitude < 10) {
            //长时中断
            pqvariationeventtype = 7;
        } else if (magnitude >= 10 && magnitude <= 90) {
            //长时欠电压
            pqvariationeventtype = 6;
        } else if (magnitude >= 110) {
            //长时过电压
            pqvariationeventtype = 5;
        }
        return pqvariationeventtype;
    }

    private static Integer getPqvariationeventtype(Double magnitude, Integer pqvariationeventtype) {
        //暂态范围
        if (magnitude >= 0 && magnitude < 10) {
            //中断
            pqvariationeventtype = 4;
        } else if (magnitude >= 10 && magnitude < 90) {
            //暂降
            pqvariationeventtype = 3;
        } else if (magnitude >= 110 && magnitude <= 180) {
            //暂升
            pqvariationeventtype = 2;
        }
        return pqvariationeventtype;
    }

    /**
     * @description 容忍区判断
     */
    private void getToleranceband(PqEventVariation value) {
        //获取计算上限
        Long duration = ParseDataUtil.parseLong(value.getDuration());
        Double magnitude = ParseDataUtil.parseDouble(value.getMagnitude());
        value.setToleranceband( getToleranceband(duration, magnitude));
    }

    private Integer getToleranceband(Long duration, Double magnitude) {
        Double lefthighduration = 0.0;
        Long lefthighlimit = 0L;
        Double righthighduration = 0.0;
        Long righthighlimit = 0L;
        if (duration >= 0 && duration < 400) {
            lefthighduration = 0.0;
            lefthighlimit = 100000000L;
            righthighduration = 400.0;
            righthighlimit = 500L;
        } else if (duration >= 400 && duration < 1000) {
            lefthighduration = 400.0;
            lefthighlimit = 500L;
            righthighduration = 1000.0;
            righthighlimit = 200L;
        } else if (duration >= 1000 && duration < 3000) {
            lefthighduration = 1000.0;
            lefthighlimit = 200L;
            righthighduration = 3000.0;
            righthighlimit = 140L;
        } else if (duration >= 3000 && duration < 20000) {
            lefthighduration = 3000.0;
            lefthighlimit = 140L;
            righthighduration = 20000.0;
            righthighlimit = 120L;
        } else if (duration >= 20000 && duration < 500000) {
            lefthighduration = 20000.0;
            lefthighlimit = 120L;
            righthighduration = 500000.0;
            righthighlimit = 110L;
        } else if (duration >= 500000) {
            lefthighduration = 500000.0;
            lefthighlimit = 110L;
            righthighduration = 1000000000.0;
            righthighlimit = 110L;
        }
        //计算上限
        Long highlimit = 0L;
        if (ParseDataUtil.parseDouble(lefthighduration * 10).longValue() == ParseDataUtil.parseDouble(righthighduration * 10).longValue()) {
            highlimit = righthighlimit;
        } else {
            highlimit = lefthighlimit + ParseDataUtil.parseDouble((righthighlimit - lefthighlimit) * (duration - lefthighduration) / (righthighduration - lefthighduration)).longValue();
        }

        //获取计算下限
        Double leftlowduration = 0.0;
        Long leftlowlimit = 0L;
        Double rightlowduration = 0.0;
        Long rightlowlimit = 0L;
        if (duration >= 0 && duration < 20000) {
            leftlowduration = 0.0;
            leftlowlimit = -1L;
            rightlowduration = 20000.0;
            rightlowlimit = 0L;
        } else if (duration >= 20000 && duration < 50000) {
            leftlowduration = 20000.0;
            leftlowlimit = 70L;
            rightlowduration = 50000.0;
            rightlowlimit = 70L;
        } else if (duration >= 50000 && duration < 10000000) {
            leftlowduration = 50000.0;
            leftlowlimit = 80L;
            rightlowduration = 10000000.0;
            rightlowlimit = 90L;
        } else if (duration >= 10000000 && duration < 100000000) {
            leftlowduration = 10000000.0;
            leftlowlimit = 90L;
            rightlowduration = 100000000.0;
            rightlowlimit = 90L;
        }
        //计算上限
        Long lowlimit = 0L;
        if (ParseDataUtil.parseDouble(leftlowduration * 10).longValue() == ParseDataUtil.parseDouble(rightlowduration * 10).longValue()) {
            lowlimit = leftlowlimit;
        } else {
            Double tempDuration = rightlowduration - leftlowduration;
            if (tempDuration > 0 || tempDuration < 0) {
                lowlimit = ParseDataUtil.parseDouble(leftlowlimit + (rightlowlimit - leftlowlimit) * (duration - leftlowduration) / tempDuration).longValue();
            }
        }

        Integer toleranceband;
        if (duration < 0 || duration > 100000000 || magnitude < 0 || magnitude > 500) {
            //特征值不存在或者超出区间，容忍区设置为无
            toleranceband = 1;
        } else if (magnitude > highlimit) {
            //损坏区
            toleranceband = 3;
        } else if (magnitude <= lowlimit) {
            //无损区
            toleranceband = 4;
        } else {
            //容忍区
            toleranceband = 2;
        }
        return toleranceband;
    }

    /**
     * @Description: 匹配电压变动事件特征值
     **/
    private void convertPqRms(List<PqEventVariation> pqEvent18, List<PqEventData> pqData18) {
        pqEvent18.forEach(pqEvent -> {
            List<PqEventData> pqEventDataList = pqData18.stream().filter(t -> pqEvent.getDeviceid().equals(t.getDeviceId()) && pqEvent.getEventendtime().equals(t.getDataTime())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pqEventDataList)) {
                PqEventData pqEventData = pqEventDataList.get(0);
                //duration
                Long duration = pqEventData.getDur();
                //nominalvoltage
                Double nominalvoltage = pqEventData.getVolt();
                //计算v1magnitude
                Double v1magnitude = pqEventData.getV1min();
                if (Math.abs(pqEventData.getV1max() - 100) > Math.abs(pqEventData.getV1min() - 100)) {
                    v1magnitude = pqEventData.getV1max();
                }
                //计算v2magnitude
                Double v2magnitude = pqEventData.getV2min();
                if (Math.abs(pqEventData.getV2max() - 100) > Math.abs(pqEventData.getV2min() - 100)) {
                    v2magnitude = pqEventData.getV2max();
                }
                //计算v3magnitude
                Double v3magnitude = pqEventData.getV3min();
                if (Math.abs(pqEventData.getV3max() - 100) > Math.abs(pqEventData.getV3min() - 100)) {
                    v3magnitude = pqEventData.getV3max();
                }
                //计算magnitude,触发相别，v1duration,v2duration,v3duration
                Double magnitude = 0.0;
                Long v2duration = null, v1duration = null, v3duration = null;
                if (v1magnitude != null) {
                    magnitude = v1magnitude;
                }
                if (Math.abs(v2magnitude - 100) > Math.abs(magnitude - 100)) {
                    magnitude = v2magnitude;
                    v2duration = pqEventData.getDur();
                } else if (Math.abs(v3magnitude - 100) > Math.abs(magnitude - 100)) {
                    magnitude = v3magnitude;
                    v3duration = pqEventData.getDur();
                } else {
                    v1duration = pqEventData.getDur();
                }
                //计算pqvariationeventtype
                Integer pqvariationeventtype = 9;//默认为其他
                if (duration >= 10000 && duration <= 60000000) {
                    //暂态范围
                    pqvariationeventtype = getPqvariationeventtype(magnitude, pqvariationeventtype);
                } else if (duration > 60000000) {
                    pqvariationeventtype = dealPqvariationeventtype(magnitude, pqvariationeventtype);
                }
                pqEvent.setDuration(duration);
                pqEvent.setNominalvoltage(nominalvoltage);
                pqEvent.setMagnitude(magnitude);
                pqEvent.setPqvariationeventtype(pqvariationeventtype);
                pqEvent.setV1magnitude(v1magnitude);
                pqEvent.setV2magnitude(v2magnitude);
                pqEvent.setV3magnitude(v3magnitude);
                pqEvent.setV1duration(v1duration);
                pqEvent.setV2duration(v2duration);
                pqEvent.setV3duration(v3duration);
            }
        });
    }

    /**
     * @Description: 查询电压变动事件特征值
     **/
    private List<PqEventData> queryPqEventVariation(List<PqEventVariation> pqEvent18) {
        List<PqEventVariation> pqEventVariations = pqEvent18.stream().filter(t -> t.getMagnitude() == null || t.getDuration() == null).collect(Collectors.toList());
        Set<Long> deviceIds = pqEventVariations.stream().map(PqEventVariation::getDeviceid).collect(Collectors.toSet());
        List<Long> eventTimes = pqEventVariations.stream().map(PqEventVariation::getEventtime).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eventTimes)) {
            return new ArrayList<>();
        }
        Long startTime = eventTimes.stream().min(Long::compareTo).orElse(null);
        Long endTime = eventTimes.stream().max(Long::compareTo).orElse(null);
        if (endTime != null) {
            endTime = endTime + 6000;
        }
        TrendSearchListVo trendSearchListVo = new TrendSearchListVo();
        List<TrendSearchVo> trendSearchVos = new ArrayList<>();
        for (Long dataId = 3000001L; dataId <= 3000014; dataId++) {
            TrendSearchVo trendSearchVo = new TrendSearchVo();
            for (Long deviceId : deviceIds) {
                trendSearchVo.setDataId(dataId);
                trendSearchVo.setLogicalId(1);
                trendSearchVo.setDataTypeId(1);
                trendSearchVo.setDeviceId(deviceId);
                trendSearchVos.add(trendSearchVo);
            }
        }
        trendSearchListVo.setStartTime(DateUtil.formatDate(startTime, DateUtil.DATE_TO_STRING_DETAIAL_PATTERN));
        trendSearchListVo.setEndTime(DateUtil.formatDate(endTime, DateUtil.DATE_TO_STRING_DETAIAL_PATTERN));
        trendSearchListVo.setInterval(1);
        ResultWithTotal<Object> highSpeedData = deviceDataService.highSpeedData(trendSearchListVo);
        List<PqEventData> pqEventDataList = new ArrayList<>();
        if (highSpeedData.getCode() == Result.SUCCESS_CODE) {
            List<PqEventSpeed> pqEventSpeeds = ParseDataUtil.parseList(highSpeedData.getData());
            Map<String, List<PqEventSpeed>> pqEventSpeedMap = pqEventSpeeds.stream().collect(Collectors.groupingBy(t -> t.getDataId() + "#" + t.getUnixTime()));
            pqEventSpeedMap.forEach((k, v) -> {
                PqEventData pqEventData = new PqEventData();
                String[] split = k.split("#");
                Long deviceId = ParseDataUtil.parseLong(split[0]);
                Long dataTime = ParseDataUtil.parseLong(split[1]) * 1000;
                AtomicReference<Integer> dataTypeId = new AtomicReference<>(0);
                AtomicReference<Integer> logicalId = new AtomicReference<>(0);
                pqEventData.setDeviceId(deviceId);
                pqEventData.setDataTime(dataTime);
                v.forEach(pqEventSpeed -> {
                    dataTypeId.set(pqEventSpeed.getDataTypeId());
                    logicalId.set(pqEventSpeed.getLogicalId());
                    Long dataId = pqEventSpeed.getDataId();
                    if (dataId == 3000001) {
                        pqEventData.setDur(ParseDataUtil.parseLong(pqEventSpeed.getValue()));
                    } else if (dataId == 3000002) {
                        pqEventData.setVolt(pqEventSpeed.getValue());
                    } else if (dataId == 3000003) {
                        pqEventData.setV1min(pqEventSpeed.getValue());
                    } else if (dataId == 3000004) {
                        pqEventData.setV1max(pqEventSpeed.getValue());
                    } else if (dataId == 3000005) {
                        pqEventData.setV1avg(pqEventSpeed.getValue());
                    } else if (dataId == 3000006) {
                        pqEventData.setV1lost(pqEventSpeed.getValue());
                    } else if (dataId == 3000007) {
                        pqEventData.setV2min(pqEventSpeed.getValue());
                    } else if (dataId == 3000008) {
                        pqEventData.setV2max(pqEventSpeed.getValue());
                    } else if (dataId == 3000009) {
                        pqEventData.setV2avg(pqEventSpeed.getValue());
                    } else if (dataId == 3000010) {
                        pqEventData.setV2lost(pqEventSpeed.getValue());
                    } else if (dataId == 3000011) {
                        pqEventData.setV3min(pqEventSpeed.getValue());
                    } else if (dataId == 3000012) {
                        pqEventData.setV3max(pqEventSpeed.getValue());
                    } else if (dataId == 3000013) {
                        pqEventData.setV3avg(pqEventSpeed.getValue());
                    } else if (dataId == 3000014) {
                        pqEventData.setV3lost(pqEventSpeed.getValue());
                    }
                });
                pqEventData.setLogicalId(logicalId.get());
                pqEventData.setDataTypeId(dataTypeId.get());
                pqEventDataList.add(pqEventData);
            });
        }
        return pqEventDataList;
    }

    private void copyProperties18(EventVo pqEvent, PqEventVariation pqEventTransient) {
        pqEventTransient.setDescription(pqEvent.getDescription());
        pqEventTransient.setDeviceid(pqEvent.getDeviceId());
        pqEventTransient.setEventtime(pqEvent.getEventTime() + pqEvent.getMsec());
        pqEventTransient.setConfirmeventstatus(pqEvent.getConfirmeventstatus());
        pqEventTransient.setRemark(pqEvent.getRemark());
        pqEventTransient.setEventid(pqEvent.getId());
        pqEventTransient.setWaveformlogtime(pqEvent.getWaveTime());
    }

    private void copyProperties(EventVo pqEvent, PqEventVariation pqEventTransient) {
        pqEventTransient.setDescription(pqEvent.getDescription());
        pqEventTransient.setDeviceid(pqEvent.getDeviceId());
        pqEventTransient.setEventtime(pqEvent.getEventTime() + pqEvent.getMsec());
        pqEventTransient.setConfirmeventstatus(pqEvent.getConfirmeventstatus());
        pqEventTransient.setRemark(pqEvent.getRemark());
        pqEventTransient.setEventid(pqEvent.getId());
        pqEventTransient.setWaveformlogtime(pqEvent.getWaveTime());
    }

    private void copyProperties(EventVo pqEvent, DeviceEvent deviceEvent,Map<Long, Long> mointorMap) {
        deviceEvent.setDescription(pqEvent.getDescription());
        deviceEvent.setDeviceid(pqEvent.getDeviceId());
        deviceEvent.setEventtime(pqEvent.getEventTime() + pqEvent.getMsec());
        deviceEvent.setStationid(pqEvent.getStationId());
        deviceEvent.setMonitoredlabel("line");
        deviceEvent.setMonitoredid(mointorMap.get(deviceEvent.getDeviceid()) == null ? 0L : mointorMap.get(deviceEvent.getDeviceid()));

    }

    /**
     * @Description: 处理暂态17事件
     **/
    private void dealEvent17(List<EventVo> eventVos, List<PqEventVariation> pqEventTransients) {
        List<EventVo> eventVoList = eventVos.stream().filter(t -> t.getEventType() == 17).collect(Collectors.toList());
        eventVoList.forEach(pqEvent -> {
            PqEventVariation pqEventTransient = new PqEventVariation();
            copyProperties(pqEvent, pqEventTransient);
            String eveStr2 = pqEvent.getEveStr2();
            if (!StringUtils.isEmpty(eveStr2) && eveStr2.contains(";")) {
                String newEveStr2 = eveStr2.replaceAll(";", "0,");
                String[] eveStr2Split = newEveStr2.split(",");
                Double nominalvoltage = ParseDataUtil.parseDouble(eveStr2Split[0]);
                Long v1duration = ParseDataUtil.parseLong(eveStr2Split[1]) * 1000;
                Double v1magnitude = ParseDataUtil.parseDouble(eveStr2Split[2]);
                Long v2duration = ParseDataUtil.parseLong(eveStr2Split[3]) * 1000;
                Double v2magnitude = ParseDataUtil.parseDouble(eveStr2Split[4]);
                Long v3duration = ParseDataUtil.parseLong(eveStr2Split[5]) * 1000;
                Double v3magnitude = ParseDataUtil.parseDouble(eveStr2Split[6]);
                Double magnitude = ParseDataUtil.parseDouble(eveStr2Split[7]);
                Long duration = ParseDataUtil.parseLong(eveStr2Split[8]);
                pqEventTransient.setNominalvoltage(nominalvoltage);
                pqEventTransient.setV1duration(v1duration);
                pqEventTransient.setV1magnitude(v1magnitude);
                pqEventTransient.setV2duration(v2duration);
                pqEventTransient.setV2magnitude(v2magnitude);
                pqEventTransient.setV3duration(v3duration);
                pqEventTransient.setV3magnitude(v3magnitude);
                pqEventTransient.setMagnitude(magnitude);
                pqEventTransient.setDuration(duration);
                Boolean v1trigger = false, v2trigger = false, v3trigger = false;
                if (Math.abs(v2magnitude - 100) > Math.abs(magnitude - 100)) {
                    v1trigger = false;
                    v2trigger = true;
                    v3trigger = false;
                } else if (Math.abs(v3magnitude - 100) > Math.abs(magnitude - 100)) {
                    v1trigger = false;
                    v2trigger = false;
                    v3trigger = true;
                } else {
                    v1trigger = true;
                    v2trigger = false;
                    v3trigger = false;
                }
                Integer eventByte = pqEvent.getEventByte();
                if (eventByte == 1) {
                    v1trigger = true;
                    v2trigger = false;
                    v3trigger = false;
                } else if (eventByte == 2) {
                    v1trigger = false;
                    v2trigger = true;
                    v3trigger = false;
                } else if (eventByte == 3) {
                    v1trigger = false;
                    v2trigger = false;
                    v3trigger = true;
                }
                pqEventTransient.setV1trigger(v1trigger);
                pqEventTransient.setV2trigger(v2trigger);
                pqEventTransient.setV3trigger(v3trigger);
                pqEventTransient.setTransientfaultdirection(0);
                List<PqEventData> pqData17 = queryPqEventSpeed(pqEventTransient);
                convertPqTransient(pqEventTransient, pqData17);
            } else {
                pqEventTransient.setPqvariationeventtype(1);
            }
            pqEventTransients.add(pqEventTransient);
        });
    }

    /**
     * @Description: 处理暂态17事件
     **/
    private void dealEvent100(List<EventVo> eventVos,List<DeviceEvent> deviceEventList,Map<Long, Long> mointorMap) {
        List<EventVo> eventVoList = eventVos.stream().filter(t -> t.getEventType() == 100).collect(Collectors.toList());
        eventVoList.forEach(pqEvent -> {
            String description = pqEvent.getDescription();
            // [黄埔区_广州粤芯半导体技术有限公司_PQC（新）_1@1] 电压治理事件结束。暂态事件类型：暂降, 相序类型：单相, 总特征幅值=96.4%,
            // 持续时间=21ms。MaxInputUa=231.5V,MaxInputUb=227.1V,MaxInputUc=229.2V,MinInputUa=225.6V,MinInputUb=221.9V,MinInputUc=226.8V, MaxUa=230.1
            if (!StringUtils.isEmpty(description) && description.contains("事件结束")) {
                DeviceEvent deviceEvent = new DeviceEvent();
                copyProperties(pqEvent, deviceEvent, mointorMap );

                // 1. 解析暂态事件类型
                eventtypename(description, deviceEvent);

                // 2. 解析相序类型
                phase(description, deviceEvent);

                // 3. 解析总特征幅值
                magnitude(description, deviceEvent);

                // 4. 解析持续时间
                duration(description, deviceEvent);

                // 5. 解析各相电压值
                Pattern voltagePattern = Pattern.compile("(Max|Min)(In|Int)putU([abc])=(-?[0-9.]+)V");
                Matcher voltageMatcher = voltagePattern.matcher(description);
                dealDataValue(deviceEvent, voltageMatcher);
                deviceEvent.setUpdatetime(System.currentTimeMillis());
                // 波形时间
                deviceEvent.setEventtime(deviceEvent.getEventtime() -deviceEvent.getDuration());

                // 容忍区
                Integer toleranceband = getToleranceband(deviceEvent.getDuration() * 1000, deviceEvent.getMagnitude());
                deviceEvent.setToleranceband(toleranceband == 4 ? 3 : toleranceband);
                deviceEventList.add(deviceEvent);
            }

        });
    }

    private void duration(String description, DeviceEvent deviceEvent) {
        Pattern durationPattern = Pattern.compile("持续时间=(-?[0-9]+)ms");
        Matcher durationMatcher = durationPattern.matcher(description);
        if (durationMatcher.find()) {
            deviceEvent.setDuration(ParseDataUtil.parseLong(durationMatcher.group(1)));
        }
    }

    private void magnitude(String description, DeviceEvent deviceEvent) {
        Pattern amplitudePattern = Pattern.compile("总特征幅值=(-?[0-9.]+)%");
        Matcher amplitudeMatcher = amplitudePattern.matcher(description);
        if (amplitudeMatcher.find()) {
            deviceEvent.setMagnitude(ParseDataUtil.parseDouble(amplitudeMatcher.group(1)));
        }
    }

    private void phase(String description, DeviceEvent deviceEvent) {
        Pattern phaseTypePattern = Pattern.compile("相序类型：([^,]+)");
        Matcher phaseTypeMatcher = phaseTypePattern.matcher(description);
        if (phaseTypeMatcher.find()) {
            deviceEvent.setPhase(phaseTypeMatcher.group(1));
        }
    }

    private void eventtypename(String description, DeviceEvent deviceEvent) {
        Pattern eventTypePattern = Pattern.compile("暂态事件类型：([^,]+)");
        Matcher eventTypeMatcher = eventTypePattern.matcher(description);
        if (eventTypeMatcher.find()) {
            deviceEvent.setEventtypename(eventTypeMatcher.group(1));
        }
    }

    private void dealDataValue(DeviceEvent deviceEvent, Matcher voltageMatcher) {
        while (voltageMatcher.find()) {
            String type = voltageMatcher.group(1); // Max or Min
            String phase = voltageMatcher.group(3); // a, b or c
            double value = Double.parseDouble(voltageMatcher.group(4));
            switch (type + phase) {
                case "Maxa":
                    deviceEvent.setMaxa(value);
                    break;
                case "Maxb":
                    deviceEvent.setMaxb(value);
                    break;
                case "Maxc":
                    deviceEvent.setMaxc(value);
                    break;
                case "Mina":
                    deviceEvent.setMina(value);
                    break;
                case "Minb":
                    deviceEvent.setMinb(value);
                    break;
                case "Minc":
                    deviceEvent.setMinc(value);
                    break;
                default:
                    break;
            }
        }
    }

    public void singleEventCauseAnalysis(PqEventVariation event) {
        // 解析不出来默认给 其他类型
        Integer reason = CauseAnalysisEnum.OTHER.getId();
        Integer faultReason = 0;
        //查询事件波形
        List<String> data = deviceDataService.queryWaveByDeviceId(event.getDeviceid(), event.getEventtime()).getData();
        if(CollectionUtils.isEmpty(data)){
            log.info("监测点deviceId:{}在{}发生事件波形数据为空",event.getDeviceid(),event.getEventtime());
            return;
        }
        //解析波形
        WaveFileObject waveFileObject = WaveOperationUtil.loadWaveData(Base64.getDecoder().decode(data.get(0)));
        WaveDataInfo waveDataInfo = WaveOperationUtil.parseWaveDataInfo(waveFileObject.getCfgFile(), waveFileObject.getDatFile());
        //三个特征幅值存在大于120%，则判断为短路故障
        Double v1 = event.getV1magnitude();
        Double v2 = event.getV2magnitude();
        Double v3 = event.getV3magnitude();
        Double minMagnitude = getMinNotNull(v1, v2, v3);
        Double[] vArr = {v1, v2, v3};
        boolean over120 = (v1 != null && v1 > 120) || (v2 != null && v2 > 120) || (v3 != null && v3 > 120);
        if (over120
                || event.getDuration() < 100000
                || (minMagnitude != null && minMagnitude < 70)) {
            reason = CauseAnalysisEnum.SHORT_CIRCUIT_FAULT.getId();
            if (Boolean.TRUE.equals(faultReasonEnabled)) {
                LineFaultEnum lineFaultEnum = getFaultReason(event, waveDataInfo);
                if (lineFaultEnum != null) {
                    event.setFaultreason(lineFaultEnum.getType());
                    faultReason = lineFaultEnum.getType();
                }
            }
        }
        if (Objects.equals(reason, CauseAnalysisEnum.OTHER.getId())) {
            //获取录波频率
            int samplePerCycle = FftUtil.calSamplePerCycle(waveDataInfo);
            long interval = FftUtil.calTriggerInterval(waveDataInfo);
            Integer begin = (int) interval * samplePerCycle / 20;
            //计算负序和零序不平衡值
            SequenceValue sequenceValue = FftUtil.calcSequenceValue(waveDataInfo.getChannelDetailInfoList(), begin, begin + samplePerCycle);
            //计算谐波数据
            //Specturm specturm = FftUtil.calcSpecturmData(channelDetailInfo.getChannelData(), begin, begin + samplePerCycle);
            //Map<Integer, Double> specturmData = specturm != null ? specturm.getSpecturmData() : new HashMap<>();
            //计算恢复段前90%的奇异值
            //double singularValue = FftUtil.calSingularValue(waveDataInfo, samplePerCycle, channelDetailInfo);
            //计算负序不平衡度和零序不平衡度，如果负序和零序都超过阈值（建议10%），则判断为短路故障
            if (sequenceValue.getUNegativeUnbalance() > 0.1 && sequenceValue.getUZeroUnbalance() > 0.1) {
                reason = CauseAnalysisEnum.SHORT_CIRCUIT_FAULT.getId();
                // 短路故障设置故障原因
                if(Boolean.TRUE.equals(faultReasonEnabled)){
                    LineFaultEnum lineFaultEnum = getFaultReason(event,waveDataInfo);
                    if (lineFaultEnum != null){
                        event.setFaultreason(lineFaultEnum.getType());
                        faultReason = lineFaultEnum.getType();
                    }
                }
            } else if (allNotNullLessThan(vArr, 90)
                    && event.getMagnitude() != null && event.getMagnitude() >= 85
                    && calcDifference(event)) {
                // 三相电压同时发生暂降，三相暂降幅值相同
                // 暂降幅值一般不会低于0.85 p.u.
                reason = CauseAnalysisEnum.MOTOR_START.getId();
            } else {
                reason = CauseAnalysisEnum.OTHER.getId();
            }
        }
        event.setReason(reason);
        event.setFaultreason(faultReason);
    }

    private boolean allNotNullLessThan(Double[] values, double threshold) {
        return Arrays.stream(values)
                .filter(Objects::nonNull)
                .allMatch(v -> v < threshold);
    }

    private Double getMinNotNull(Double... values) {
        return Arrays.stream(values)
                .filter(Objects::nonNull)
                .min(Double::compareTo)
                .orElse(null);
    }

    public LineFaultEnum getFaultReason(PqEventVariation event,WaveDataInfo waveDataInfo) {
        log.info("lineId is " + event.getDeviceid());
        log.info("reason is " + event.getReason());
        // 获取周波采点数
        int cyclesNumber = (int) Math.ceil(waveDataInfo.getSampInfoList().get(0).getSamp() / 50);
        // 波形筛选
        RmsResult rmsResult = verifyIfFault(event, waveDataInfo, cyclesNumber);
        // 获取零序电流
        iZeroValueBuild(waveDataInfo, rmsResult);
        // 计算故障类型
        LineFaultEnum faultTypeEnum = getFaultType(rmsResult);
        return faultTypeEnum;
    }

    /**
     * 获取零序电流
     * @param waveDataInfo
     * @param rmsResult
     */
    private void iZeroValueBuild(WaveDataInfo waveDataInfo, RmsResult rmsResult) {
        Double iZeroValue = null;
        try {
            iZeroValue = FftUtil.calcSequenceValue(waveDataInfo.getChannelDetailInfoList(), rmsResult.getAbnormalStartIndex(), rmsResult.getAbnormalEndIndex()).getIZeroValue();
        } catch (Exception e) {
            log.error("iZeroValue analysis error", e);
        }
        rmsResult.setZeroCurrentRmsDifferenceAbs(Math.abs(iZeroValue == null ? 0D : iZeroValue));
    }


    /**
     * @description: 故障波形筛选，仅满足以下两个条件的波形文件，才认为是发生了故障
     **/
    private RmsResult verifyIfFault(PqEventVariation pqEventVariation, WaveDataInfo waveDataInfo,Integer cyclesNumber ) {
        RmsResult rmsResult = new RmsResult();
        rmsResult.setHasFault(false);
        // 持续时间需要大于2个周波
        if (pqEventVariation.getDuration() / 1000 <= CYCLE * 2L) {
            return rmsResult;
        }
        Long waveStartTime = pqEventVariation.getWaveformlogtime();
        Long eventStartTime = pqEventVariation.getEventtime();
        Long eventEndTime = pqEventVariation.getDuration() / 1000 + eventStartTime;
        // 按采样数量和采样周期计算录波结束时间
        Long waveEndTime = waveStartTime + (int)((double)waveDataInfo.getChannelDetailInfoList().get(0).getChannelData().size() * ((double)CYCLE / cyclesNumber));
        int normalStartIndex = -1;
        int normalEndIndex = -1;
        // （1）如果故障触发时间-波形开始时间>20ms，则表示是一个完整周波，可以取该段时间内的所有电流进行计算；
        if (Math.abs(eventStartTime - waveStartTime )> CYCLE) {
            log.info("get normal current rms in case one");
            normalStartIndex = 0;
            normalEndIndex = ParseDataUtil.parseInteger ((eventStartTime - waveStartTime) * cyclesNumber / CYCLE);
            normalEndIndex = Math.abs(normalEndIndex);
        } else if (Math.abs(eventStartTime - waveStartTime ) < CYCLE && Math.abs(waveEndTime - eventEndTime) > CYCLE) {
            // （2）如果故障触发时间-波形开始时间<20ms，并且波形结束时间-故障结束时间>20ms,则表示故障结束时间到波形结束时间这段时间内是一完整周波，可以取该段时间内所有电流进行计算；
            log.info("get normal current rms in case two");
            normalStartIndex =  ParseDataUtil.parseInteger((eventEndTime - waveStartTime) * cyclesNumber / CYCLE) ; // 故障触发时间 - 故障结束时间
            normalEndIndex = ParseDataUtil.parseInteger((waveEndTime - waveStartTime) * cyclesNumber / CYCLE) ; // 故障结束时间 - 波形结束时间
            normalStartIndex = Math.abs(normalStartIndex);
            normalEndIndex = Math.abs(normalEndIndex);
        } else if (Math.abs(eventStartTime - waveStartTime) < CYCLE && Math.abs(waveEndTime - eventEndTime) < CYCLE) {
            //（3）如果故障触发时间-波形开始时间 和 波形结束时间-故障结束时间 均<20ms，则表示这不是一个完整周波，退出波形处理，不进行计算。
            log.info("wave have not count");
            return rmsResult;
        } else {
            log.info("skip wave for invalid normal current rms");
            return rmsResult;
        }
        if (normalEndIndex <= normalStartIndex) {
            log.info("skip wave for invalid current list index");
            return rmsResult;
        }

        rmsResult.setNormalEndIndex(normalEndIndex);
        rmsResult.setNormalStartIndex(normalStartIndex);
        //获取相电流（A,B,C,N)
        List<Float> aCurrent = WaveOperationUtil.getChannelData(waveDataInfo, "IA", "I1", "A1","Ia","#1I1");
        List<Float> bCurrent = WaveOperationUtil.getChannelData(waveDataInfo, "IB", "I2", "A2","Ib","#1I2");
        List<Float> cCurrent = WaveOperationUtil.getChannelData(waveDataInfo, "IC", "I3", "A3","Ic","#1I3");
        rmsResult.setACurrent(aCurrent);
        rmsResult.setBCurrent(bCurrent);
        rmsResult.setCCurrent(cCurrent);


        // 正常电流有效值,故障前
        double[] normalRms = rms(aCurrent, bCurrent, cCurrent, normalStartIndex, normalEndIndex);
        // 异常电流有效值，故障后开始和结束的下标
        int abnormalStartIndex = ParseDataUtil.parseInteger((eventStartTime - waveStartTime) * cyclesNumber / CYCLE);
        int abnormalEndIndex = ParseDataUtil.parseInteger( (eventEndTime - waveStartTime) * cyclesNumber / CYCLE);
        rmsResult.setAbnormalStartIndex(abnormalStartIndex);
        rmsResult.setAbnormalEndIndex(abnormalEndIndex);
        double[] abnormalRms = rms(aCurrent, bCurrent, cCurrent, abnormalStartIndex, abnormalEndIndex);
        // 各相电流的有效值（rms值）需要有超过50A的变化
        rmsJudge(rmsResult, normalRms, abnormalRms);
        return rmsResult;
    }

    /**
     * rms值判断
     * @param rmsResult
     * @param normalRms
     * @param abnormalRms
     */
    private void rmsJudge(RmsResult rmsResult, double[] normalRms, double[] abnormalRms) {
        for (int i = 0; i < 3; i += 1) {
            if (Math.abs(normalRms[i] - abnormalRms[i]) > 50) {
                rmsResult.setHasFault(true);
            }
            if (i == 0) {
                rmsResult.setACurrentNormalRms(normalRms[i]);
                rmsResult.setACurrentAbnormalRms(abnormalRms[i]);
                rmsResult.setACurrentRmsDifferenceAbs(Math.abs(normalRms[i] - abnormalRms[i]));
            } else if (i == 1) {
                rmsResult.setBCurrentNormalRms(normalRms[i]);
                rmsResult.setBCurrentAbnormalRms(abnormalRms[i]);
                rmsResult.setBCurrentRmsDifferenceAbs(Math.abs(normalRms[i] - abnormalRms[i]));
            } else {
                rmsResult.setCCurrentNormalRms(normalRms[i]);
                rmsResult.setCCurrentAbnormalRms(abnormalRms[i]);
                rmsResult.setCCurrentRmsDifferenceAbs(Math.abs(normalRms[i] - abnormalRms[i]));
            }
        }
        log.info("normalRms is {} , abnormalRms is {} ", normalRms, abnormalRms);
    }

    /**
     * rms值计算
     * @param aCurrent
     * @param bCurrent
     * @param cCurrent
     * @param startIndex
     * @param endIndex
     * @return
     */
    private double[] rms(List<Float> aCurrent, List<Float> bCurrent, List<Float> cCurrent, int startIndex, int endIndex) {
        double[] rmsArray = new double[3];
        int size = endIndex - startIndex;
        double asum = 0;
        double bsum = 0;
        double csum = 0;
        for (int i = startIndex; i < endIndex; i += 1) {
            asum += Math.pow(aCurrent.get(i), 2);

            bsum += Math.pow(bCurrent.get(i), 2);

            csum += Math.pow(cCurrent.get(i), 2);

        }
        double aRms = Math.sqrt(asum / size);
        double bRms = Math.sqrt(bsum / size);
        double cRms = Math.sqrt(csum / size);
        rmsArray[0] = aRms;
        rmsArray[1] = bRms;
        rmsArray[2] = cRms;
        return rmsArray;
    }


    /**
     * @description: 配网故障类型匹配
     **/
    private LineFaultEnum getFaultType(RmsResult rmsResult) {
        Double Da = rmsResult.getACurrentRmsDifferenceAbs();
        Double Db = rmsResult.getBCurrentRmsDifferenceAbs();
        Double Dc = rmsResult.getCCurrentRmsDifferenceAbs();
        Double D0 = rmsResult.getZeroCurrentRmsDifferenceAbs();
        Double epsilon1 = EPSILON_1_COEFFICIENT * rmsResult.getACurrentNormalRms();
        log.info("计算参数打印,Da:{};Db:{};Dc:{};D0:{};epsilon1:{}",Da,Db,Dc,D0,epsilon1);
        // 情况一 当Da、Db、Dc都分别超过阈值ε1（Da≥ε1、Db≥ε1、Dc≥ε1）时
        if (Da >= epsilon1 && Db >= epsilon1 && Dc >= epsilon1) {
            if (Da / Db >= EPSILON_2 && Da / Dc >= EPSILON_2) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_1);
                return LineFaultEnum.AG;
            } else if (Db / Da >= EPSILON_2 && Db / Dc >= EPSILON_2) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_1);
                return LineFaultEnum.BG;
            } else if (Dc / Da >= EPSILON_2 && Dc / Db >= EPSILON_2) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_1);
                return LineFaultEnum.CG;
            } else if (Da / Dc >= EPSILON_2 && Db / Dc >= EPSILON_2 && D0 >= EPSILON_3 * Da) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_2);
                return LineFaultEnum.ABG;
            } else if (Da / Dc >= EPSILON_2 && Db / Dc >= EPSILON_2 && D0 < EPSILON_3 * Da) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_2);
                return LineFaultEnum.AB;
            } else if (Da / Db >= EPSILON_2 && Dc / Db >= EPSILON_2 && D0 >= EPSILON_3 * Da) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_2);
                return LineFaultEnum.CAG;
            } else if (Da / Db >= EPSILON_2 && Dc / Db >= EPSILON_2 && D0 < EPSILON_3 * Da) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_2);
                return LineFaultEnum.CA;
            } else if (Db / Da >= EPSILON_2 && Dc / Da >= EPSILON_2 && D0 >= EPSILON_3 * Db) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_2);
                return LineFaultEnum.BCG;
            } else if (Db / Da >= EPSILON_2 && Dc / Da >= EPSILON_2 && D0 < EPSILON_3 * Db) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_2);
                return LineFaultEnum.BC;
            } else if (Db / Da < EPSILON_2 && Da / Db < EPSILON_2 && Db / Dc < EPSILON_2
                    && Dc / Db < EPSILON_2 && Dc / Da < EPSILON_2 && Da / Dc < EPSILON_2 && D0 < EPSILON_3 * Da) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_3);
                return LineFaultEnum.ABC;
            } else {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_3);
                return LineFaultEnum.ABCG;
            }
        } else {
            //当Da、Db、Dc未都超过阈值ε1时
            if (Da >= epsilon1 && Db >= epsilon1 && Dc < epsilon1 && Da / Db < EPSILON_2 && Db / Da < EPSILON_2 && D0 < EPSILON_3 * Da) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_6);
                return LineFaultEnum.AB;
            } else if (Dc >= epsilon1 && Db >= epsilon1 && Da < epsilon1 && Db / Dc < EPSILON_2 && Dc / Db < EPSILON_2 && D0 < EPSILON_3 * Db) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_6);
                return LineFaultEnum.BC;
            } else if (Dc >= epsilon1 && Da >= epsilon1 && Db < epsilon1 && Dc / Da < EPSILON_2 && Da / Dc < EPSILON_2 && D0 < EPSILON_3 * Dc) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_6);
                return LineFaultEnum.CA;
            } else if (Da >= epsilon1 && Db >= epsilon1 && Dc < epsilon1 && Da / Db < EPSILON_2 && Db / Da < EPSILON_2 && D0 >= EPSILON_3 * Da) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_6);
                return LineFaultEnum.ABG;
            } else if (Db >= epsilon1 && Dc >= epsilon1 && Da < epsilon1 && Db / Dc < EPSILON_2 && Dc / Db < EPSILON_2 && D0 >= EPSILON_3 * Db) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_6);
                return LineFaultEnum.BCG;
            } else if (Dc >= epsilon1 && Da >= epsilon1 && Db < epsilon1 && Dc / Da < EPSILON_2 && Da / Dc < EPSILON_2 && D0 >= EPSILON_3 * Dc) {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_6);
                return LineFaultEnum.CAG;
            } else {
                rmsResult.setConfidenceLevel(CONFIDENCE_LEVEL_5);
                return LineFaultEnum.AG;
            }
        }
    }

    public void singleEventCauseAnalysis2(PqEventVariation event) {
        //查询事件对应监测点的deviceId
        List<Map<String, Object>> measuredbyList = getMeterIds(Arrays.asList(event.getMonitoredid())).getData();
        Optional<Map<String, Object>> monitorFirst = measuredbyList.stream().filter(x -> ParseDataUtil.parseLong(x.get("monitoredid")).equals(event.getMonitoredid())).findFirst();
        if (monitorFirst.isPresent()) {
            Long deviceId = ParseDataUtil.parseLong(monitorFirst.get().get("measuredby"));
            Long waveTime = event.getEventtime();
            Integer reason = calcEventAnalysis(deviceId, waveTime);
            event.setReason(reason);
        }
    }

    private Integer calcEventAnalysis(Long deviceId, Long waveTime) {

        List<String> data = deviceDataService.queryWaveByDeviceId(deviceId, waveTime).getData();
        //解析波形
        WaveFileObject waveFileObject = WaveOperationUtil.loadWaveData(Base64.getDecoder().decode(data.get(0)));

        WaveDataInfo waveDataInfo = WaveOperationUtil.parseWaveDataInfo(waveFileObject.getCfgFile(), waveFileObject.getDatFile());
        Double[] ua = getChannelData(waveDataInfo, 0);
        Double[] ub = getChannelData(waveDataInfo, 1);
        Double[] uc = getChannelData(waveDataInfo, 2);
        List<Float> collect = waveDataInfo.getChannelDetailInfoList().get(0).getChannelData().stream().map(KeyValuePair::getXField).collect(Collectors.toList());
        List<Double> collect1 = collect.stream().map(x -> Double.parseDouble(String.valueOf(x / (1000.0 * 1000.0)))).collect(Collectors.toList());
        Double[] t = collect1.toArray(new Double[0]);
        //采样率
        int samplePerCycle = calSamplePerCycle(waveDataInfo);
        return calcCauseType(ua, ub, uc, t, samplePerCycle, waveDataInfo.getFrequency());
    }

    public Integer calcCauseType(Double[] va, Double[] vb, Double[] vc, Double[] t, int sample, double f) {
        Juzhen juzhen = featureCalUtil(va, vb, vc, t, sample, f);//特征值計算
        if (juzhen.getDuration_dw() > 0) {
            double ts = t[juzhen.getTs()] * 1000.0;
            double te = t[juzhen.getTe()] * 1000.0;
            double cxsj = te - ts;
            juzhen.setDuration(cxsj);
        }
        classthreshold(juzhen);
        return Integer.parseInt(juzhen.getRdlx());
    }

    public void classthreshold(Juzhen juzhen) {
        int yc = 0;//Yc=1,2,3,4对应普通故障、雷击、变压器激磁、电机启动
        //得到波形对应的特征参数
        //若暂降前谐波含量太大，则去掉正常时的影响量
        if (juzhen.getPu1_pre() > 0.01) {
            juzhen.setPu1(juzhen.getPu1() - juzhen.getPu1_pre());
        }
        //第一步，特征明显的故障类型判断
        //1.1 如果发生较大的暂升和零序不平衡度，判断为故障
        if (juzhen.getUn_0v() > 0.4 && juzhen.getU_max() > 1.2) {
            yc = 10;
        }
        //1.2 如果持续时间小于5个周波，判断为故障
        if (juzhen.getDuration_dw() < juzhen.getSample() * 5) {
            yc = 10;
        }
        //1.3 判断各相暂降幅值最小值是否有低于阀值，低于阀值判断为短路故障
        if (juzhen.getU_min() < 0.7) {
            yc = 10;
        }
        //1.4 如果零序不平衡度和负序不平衡度超过阀值（建议10%）,形状因子大于0.5，
        // 且事件前负序和零序不平衡度小于6% ,判断为短路故障
        if (juzhen.getUn_0v() > 0.1 && juzhen.getUn_nV() > 0.1 && juzhen.getBi1() > 0.5
                && juzhen.getUn_0v_pre() < 0.06 && juzhen.getUn_nV_pre() < 0.06) {
            yc = 10;//故障
        }
        //第二步，其他暂降判断
        if (yc == 0) {
            //筛选出对称故障和电机  （零序分量、负序分量小于0.02；且都是三相暂降；2、4次谐波小于0.02）\
            //如果事件前零序和负序都不超过0.01
            if (juzhen.getUn_0v() < 0.01 && juzhen.getUn_nV() < 0.01 &&
                    juzhen.getUa_min() < 0.9 && juzhen.getUb_min() < 0.9 && juzhen.getUc_min() < 0.9 &&
                    juzhen.getPu1() < 0.02 && juzhen.getUn_0v_pre() < 0.01 && juzhen.getUn_nV_pre() < 0.01) {
                // 2.1.1 如果恢复段奇异值小于0.015，且持续时间大于5秒，判断为电机启动
                if (juzhen.getSvd() < 0.015 && juzhen.getDuration_dw() > 250 * juzhen.getSample()) {
                    yc = 7;
                } else {
                    yc = 10;
                }


            }//如果事件前负序不超过0.01，零序超过0.01，减掉事件段零序的判剧，加上形状因子判据
            else if (juzhen.getUn_nV() < 0.01 && juzhen.getUa_min() < 0.9 && juzhen.getUb_min() < 0.9 && juzhen.getUc_min() < 0.9 &&
                    juzhen.getPu1() < 0.02 && juzhen.getUn_0v_pre() >= 0.01 && juzhen.getUn_nV_pre() < 0.01 && juzhen.getBi2() < 0.25) {
                if (juzhen.getSvd() < 0.015 && juzhen.getDuration_dw() > 250 * juzhen.getSample()) {
                    yc = 7;
                } else {
                    yc = 10;
                }
            }//如果事件前零序不超过0.01，负序超过0.01，减掉事件段负序的判剧，加上形状因子判据
            else if (juzhen.getUn_0v() < 0.01 && juzhen.getUa_min() < 0.9 && juzhen.getUb_min() < 0.9 && juzhen.getUc_min() < 0.9 &&
                    juzhen.getPu1() < 0.02 && juzhen.getUn_0v_pre() < 0.01 && juzhen.getUn_nV_pre() >= 0.01 && juzhen.getBi2() < 0.25) {
                if (juzhen.getSvd() < 0.015 && juzhen.getDuration_dw() > 250 * juzhen.getSample()) {
                    yc = 4;
                } else {
                    yc = 10;
                }
            }//如果事件前零序、零序均超过0.01，减掉事件段零序、负序的判剧，加上形状因子判据
            else if (juzhen.getUa_min() < 0.9 && juzhen.getUb_min() < 0.9 && juzhen.getUc_min() < 0.9 &&
                    juzhen.getPu1() < 0.02 && juzhen.getUn_0v_pre() >= 0.01 && juzhen.getUn_nV_pre() >= 0.01 && juzhen.getBi2() < 0.25) {
                if (juzhen.getSvd() < 0.015 && juzhen.getDuration_dw() > 250 * juzhen.getSample()) {
                    yc = 7;
                } else {
                    yc = 10;
                }
            }//  2.2.1 如果谐波含量大于0.04，持续时间超过200ms，结合形状因子，判断为激磁
            else if (juzhen.getPu1() > 0.04 && juzhen.getDuration_dw() > 10 * juzhen.getSample() && juzhen.getBi1() < 0.4 && juzhen.getBi2() > 0.15) {
                yc = 6;
            } else {
                yc = 10;
            }
        }

        if (yc == 10) {
            if ((juzhen.getPa1() > 0.1 && juzhen.getPa2() > 0.1) || (juzhen.getPb1() > 0.1 && juzhen.getPb2() > 0.1) ||
                    (juzhen.getPc1() > 0.1 && juzhen.getPc2() > 0.1)) {
                yc = 5;  //雷击
            } else {
                yc = 20;//普通故障
            }
        }
        if (yc == 20) {
            if (juzhen.getUa_min() < 0.9 && juzhen.getUb_min() < 0.9 && juzhen.getUc_min() < 0.9) {
                yc = 4;
            } else if ((juzhen.getUa_min() < 0.9 && juzhen.getUb_min() < 0.9 && juzhen.getUc_max() < 1.2)
                    || (juzhen.getUb_min() < 0.9 && juzhen.getUc_min() < 0.9 && juzhen.getUa_max() < 1.2)
                    || (juzhen.getUc_min() < 0.9 && juzhen.getUa_min() < 0.9 && juzhen.getUb_max() < 1.2)) {
                yc = 3;
            } else if ((juzhen.getUa_min() < 0.9 && juzhen.getUb_min() < 0.9 && juzhen.getUc_max() > 1.2)
                    || (juzhen.getUb_min() < 0.9 && juzhen.getUc_min() < 0.9 && juzhen.getUa_max() > 1.2)
                    || (juzhen.getUc_min() < 0.9 && juzhen.getUa_min() < 0.9 && juzhen.getUb_max() > 1.2)) {
                yc = 2;
            } else {
                yc = 1;
            }

        }

        juzhen.setRdlx(String.valueOf(yc));

    }

    private double getMaxFz(Double[] va, int tsa) {
        double max = 0;
        if (va != null && va.length > 0) {
            max = va[0];
            for (int i = 1; i < tsa; i++) {
                if (va[i] > max) {
                    max = va[i];
                }

            }
        }
        return max;
    }

    private Double[] fzgy(Double[] vaRms, double v) {
        for (int i = 0; i < vaRms.length; i++) {
            double value = vaRms[i];
            vaRms[i] = value / v;
        }
        return vaRms;
    }

    private Double[] getMin(Double[] vaRms, Double[] vbRms, Double[] vcRms) {
        Double[] vMin = new Double[vaRms.length];
        for (int i = 0; i < vaRms.length; i++) {
            double min = 0;
            min = vaRms[i];
            if (vbRms[i] < min) {
                min = vbRms[i];
            }
            if (vcRms[i] < min) {
                min = vcRms[i];
            }
            vMin[i] = min;

        }
        return vMin;
    }

    private double getMax(Double[] vaRms, Double[] vbRms, Double[] vcRms, int ts, int te) {
        double max = vaRms[ts];
        for (int i = ts; i <= te; i++) {
            if (vaRms[i] > max) {
                max = vaRms[i];
            }
            if (vbRms[i] > max) {
                max = vbRms[i];
            }
            if (vcRms[i] > max) {
                max = vcRms[i];
            }
        }
        return max;
    }

    private double getMinFz(Double[] vMin, int ts, int te) {
        double min = vMin[ts];
        for (int i = ts; i <= te; i++) {
            if (vMin[i] < min) {
                min = vMin[i];
            }
        }
        return min;
    }

    public Double[] getBI(int ts, int te1, Double[] vMin) {
        Double[] yy = new Double[5];
        Double[] v = Arrays.copyOfRange(vMin, ts, te1 + 1);
        List<Double> vList = Arrays.asList(v);
        Collections.sort(vList);
        double min = vList.get(0);
        double max = vList.get(vList.size() - 1);
        double jg = (max - min) / 5;
        Double[] xout = new Double[5];
        xout[4] = max;
        for (int i = 1; i < 5; i++) {
            xout[i - 1] = min + i * jg;
        }
        for (int i = 0; i < 5; i++) {
            int num = 0;
            for (int j = 0; j < vList.size(); j++) {
                if (vList.get(j) <= xout[i]) {
                    if (i == 0) {
                        num++;
                    } else {
                        if (xout[i - 1] < vList.get(j)) {
                            num++;
                        }
                    }
                } else {
                    break;
                }
            }
            yy[i] = (double) num / (te1 - ts);
        }
        return yy;

    }

    public Juzhen featureCalUtil(Double[] va, Double[] vb, Double[] vc, Double[] t, int sample, double f) {
        Juzhen juzhen = new Juzhen();
        juzhen.setSample(sample);
        //求取有效值
        //Va_rms,Vb_rms,Vc_rms
        Double[] vaRms = rms(va, sample);
        Double[] vbRms = rms(vb, sample);
        Double[] vcRms = rms(vc, sample);
        //求V，V=max(Va_rms(1:sample));
        double v = getMaxFz(vaRms, sample);
        //幅值归一化
        vaRms = fzgy(vaRms, v);
        vbRms = fzgy(vbRms, v);
        vcRms = fzgy(vcRms, v);
        //计算每个采样点，三相电压最小值
        Double[] vMin = getMin(vaRms, vbRms, vcRms);
        int n = vMin.length;
        double ut = 0.9;
        //计算特征，计算持续时间
        int ts = 0;
        int te = 0;
        for (int i = 1; i < n; i++) {
            if (vMin[i] < ut && vMin[i - 1] >= ut) {
                ts = i;
                break;
            }
        }

        for (int i = n - 1; i >= 1; i--) {
            if (vMin[i] >= ut && vMin[i - 1] < ut) {
                te = i;
                break;
            }
        }
        boolean sfwz = true;
        if (te == 0) {
            int len = (n - ts) / sample;
            if (len > 33) {
                len = 31;
            } else if (len > 4) {
                len = len - 2;
            }
            te = ts + len * sample;
            juzhen.setStatus(0);
            sfwz = false;
            log.info("数据不完整");
        }
        //持续时间
        double duration = 0.0;
        if (sfwz) {
            duration = (double) (te - ts + 1);
            juzhen.setTe(te);
        } else {
            duration = (double) (n - ts);
            juzhen.setTe(n - 1);
        }
        juzhen.setDuration_dw(duration);
        juzhen.setTs(ts);

        //特征2 幅值最大值u_max，用于判断是否发生暂升
        double vMax = getMax(vaRms, vbRms, vcRms, ts, te);
        juzhen.setU_max(vMax);
        //特征3 幅值特征u_min、ua_min、ub_min、uc_min
        double uMin = getMinFz(vMin, ts, te);
        double uaMin = getMinFz(vaRms, ts, te);
        double ubMin = getMinFz(vbRms, ts, te);
        double ucMin = getMinFz(vcRms, ts, te);
        int tsa = this.getTs(vaRms, ut);
        int tsb = this.getTs(vbRms, ut);
        int tsc = this.getTs(vcRms, ut);
        double vaMax = this.getMaxFz(vaRms, tsa);
        double vbMax = this.getMaxFz(vbRms, tsb);
        double vcMax = this.getMaxFz(vcRms, tsc);
        juzhen.setU_min(uMin);
        juzhen.setUa_min(uaMin);
        juzhen.setUb_min(ubMin);
        juzhen.setUc_min(ucMin);
        juzhen.setUa_max(vaMax);
        juzhen.setUb_max(vbMax);
        juzhen.setUc_max(vcMax);
        //特征4  UN_0V零序不平衡度、UN_nV负序不平衡度、UN_np三相不平衡
        //如果存在两个暂降，选取第一个暂降的事件段提取特征
        int te1 = te; //第一个暂降结束时间
        for (int i = ts + 2 * sample; i <= te; i++) {
            if (vMin[i] >= ut && vMin[i - 1] < ut) {
                te1 = i;
                break;
            }
        }
        //提取计算三相不平衡的波形
        int qs = ts;
        int js = te1;
        boolean flag = false;
        double uN0V = 0;
        ;//零序不平衡度
        double uNNV = 0;//负序不平衡度
        double uNPV = 0;//正序不平衡度
        double uNNp = 0;//负序除以正序，电压不平衡度
        if (te1 - ts < 2 * sample) {
            //持续时间过短的话，特征为0
            log.info("持续时间过短的话，特征为0");
        } else if (te1 - ts <= 30 * sample) {
            //如果小于30个周期，采用对整个事件段的波形进行分析
            qs = ts;
            js = te1 - sample;
            flag = true;
        } else {
            int k = (ts + te1) / 2;
            qs = k - 4 * sample;
            js = k + 4 * sample;
            flag = true;
        }
        if (flag) {
            //计算三相不平衡
            Map<String, Double> map = getSxsj(va, vb, vc, t, qs, js + 1, v, sample, f);
            uN0V = map.get("UN_0V");
            uNNV = map.get("UN_NV");
            uNPV = map.get("UN_PV");
            uNNp = map.get("UN_NP");
        }
        //频谱特征
        int ks = 0;
        int end = 0;
        int hh = 0;
        double pu1 = 0;
        flag = true;
        if ((te1 - ts) >= sample * 30) {//如果大于30个周期，采用中间段的波形进行分析
            ks = (ts + te1) / 2 - 4 * sample;
            end = (ts + te1) / 2 + 4 * sample;
            hh = 8 * sample + 1;
        } else if ((te1 - ts) > 2 * sample) {
            ks = ts;
            end = te1 - sample;
            hh = te1 - ts - sample + 1;
        } else {
            pu1 = 0;
            flag = false;
        }
        if (flag) {
            pu1 = getDistortion(va, vb, vc, ks, end, hh, v, sample);//频谱特征
        }
        //求解奇异值
        double sdv = jsSdv(va, vb, vc, t, sample, te1, f, ut, v);
        juzhen.setPu1(pu1);
        juzhen.setSvd(sdv);

        //3-4 形状因子bi
        double bi1 = 0.0;
        double bi2 = 0.0;
        if ((te1 - ts) > 0) {
            Double[] yy = getBI(ts, te1, vMin);
            bi1 = yy[0];
            bi2 = yy[4];
        } else {
            bi1 = 0;
            bi2 = 0;
            log.info("持续时间过短，无法评估");
        }
        juzhen.setBi1(bi1);
        juzhen.setBi2(bi2);
        //事前频谱特征
        flag = true;
        double pu1Pec = 0;
        if ((ts + 1) > (sample * 5 - 10)) {//如果大于30个周期，采用中间段的波形进行分析
            ks = 0;
            end = 4 * sample - 1;
            hh = 4 * sample;
        } else if ((ts + 1) > 2 * sample) {
            ks = 0;
            end = ts - ParseDataUtil.parseInteger(Math.round((double) sample / 2));
            hh = ts - ParseDataUtil.parseInteger(Math.round((double) sample / 2) + 1);
        } else {
            pu1Pec = 0;
            flag = false;
        }
        if (flag) {
            pu1Pec = getDistortion(va, vb, vc, ks, end, hh, v, sample);//频谱特征
        }
        juzhen.setPu1_pre(pu1Pec);

        //计算事前三相电压不平衡度
        double uN0VPec = 0;//零序不平衡度
        double uNNVPec = 0;//负序不平衡度
        double uNPVPec = 0;//正序不平衡度
        double uNNPPec = 0;//负序除以正序，电压不平衡度
        if (ts > (sample * 5 - 10)) {
            //如果小于30个周期，采用对整个事件段的波形进行分析
            qs = 0;
            js = 4 * sample - 1;
            flag = true;
        } else if (ts - ParseDataUtil.parseInteger(Math.round((double) sample / 2)) > 0) {
            qs = 0;
            js = ts - ParseDataUtil.parseInteger(Math.round((double) sample / 2));
            flag = true;
        } else {
            log.info("故障前波形时间较短，无法评估");
        }
        if (flag) {
            //计算三相不平衡
            Map<String, Double> map = getSxsj(va, vb, vc, t, qs, js, v, sample, f);
            uN0VPec = map.get("UN_0V");
            uNNVPec = map.get("UN_NV");
            uNPVPec = map.get("UN_PV");
            uNNPPec = map.get("UN_NP");

        }
        //相序接错判断,如果事件前负序分量大于0.8，正序分量小于0.5，则交换正、负序值
        if (uNNVPec > 0.8 && uNPVPec < 0.5) {
            //事件前不平衡度
            double uNpreA = uNNVPec;//交换正、负序不平衡度数值
            uNNVPec = uNPVPec;
            uNPVPec = uNpreA;
            if(uNPVPec > 0){
                uNNPPec = uNNVPec / uNPVPec;//负序除以正序，电压不平衡度
            }
            if (uNPVPec < 0){
                uNNPPec = uNNVPec / uNPVPec;//负序除以正序，电压不平衡度
            }
            if (te1 - ts >= 2 * sample) {//在正负序不平衡度都存在的情况下
                double uNA = uNNV;
                uNNV = uNPV;
                uNPV = uNA;
                if(uNPV < 0){
                    uNNp = uNNV / uNPV;
                }
                if (uNPV > 0){
                    uNNp = uNNV / uNPV;
                }
            }
        }
        juzhen.setUn_0v_pre(uN0VPec);
        juzhen.setUn_np_pre(uNNPPec);
        juzhen.setUn_nV_pre(uNNVPec);
        juzhen.setUn_0v(uN0V);
        juzhen.setUn_np(uNNp);
        juzhen.setUn_nV(uNNV);

        DefUtil def = new DefUtil();
        Double[] dfa = def.def(va, 6, "sym");
        juzhen.setPa1(dfa[0]);
        juzhen.setPa2(dfa[1]);
        Double[] dfb = def.def(vb, 6, "sym");
        juzhen.setPb1(dfb[0]);
        juzhen.setPb2(dfb[1]);
        Double[] dfc = def.def(vc, 6, "sym");
        juzhen.setPc1(dfc[0]);
        juzhen.setPc2(dfc[1]);

        juzhen.setStatus(1);
        return juzhen;
    }

    public Double[] dpDelayRms(Double[] va1, Double[] t1, int sample, double f) {
        int n = va1.length;
        Double[] rmsA = new Double[n];
        Double[] upd = new Double[n];
        Double[] upq = new Double[n];
        this.dd(va1, t1, sample, f, upd, upq);
        //求正序幅值，即Va幅值
        for (int i = 0; i < n; i++) {
            rmsA[i] = (1 / Math.sqrt(3)) * Math.sqrt(Math.pow(upd[i], 2) + Math.pow(upq[i], 2));
        }
        return rmsA;

    }

    private double jsSdv(Double[] va, Double[] vb, Double[] vc, Double[] t, int sample, int te1, double f, double ut, double v) {
        int matlen = Math.round((float) Math.sqrt((double) sample / 2));//求矩阵长度
        int winlen = matlen * matlen;//求窗口长度

        int qs = te1 - ParseDataUtil.parseInteger(Math.round((double) winlen / 2)) - 2 * sample;
        int js = te1 + winlen - ParseDataUtil.parseInteger(Math.round((double) winlen / 2)) + sample;
        if (qs < 0) {
            log.info("持续时间过短，无法评估");
            return 999;
        }

        Double[] va2 = Arrays.copyOfRange(va, qs, js + 1);
        Double[] vb2 = Arrays.copyOfRange(vb, qs, js + 1);
        Double[] vc2 = Arrays.copyOfRange(vc, qs, js + 1);
        for (int i = 0; i < va2.length; i++) {
            va2[i] = va2[i] / v;
            vb2[i] = vb2[i] / v;
            vc2[i] = vc2[i] / v;
        }
        Double[] t2 = Arrays.copyOfRange(t, qs, js + 1);

        Double[] rmsA2 = this.dpDelayRms(va2, t2, sample, f);
        Double[] rmsB2 = this.dpDelayRms(vb2, t2, sample, f);
        Double[] rmsC2 = this.dpDelayRms(vc2, t2, sample, f);
//        dp_delay(Va2,t2,sample,f,phasor_ua_2,rms_a_2,angle_a_2);
//        dp_delay(Vb2,t2,sample,f,phasor_ub_2,rms_b_2,angle_b_2);
//        dp_delay(Vc2,t2,sample,f,phasor_uc_2,rms_c_2,angle_c_2);
        int nua2 = rmsA2.length;
        //1/2周波窗口中值滤波
        int ck = sample / 4;
        int zjs = ck;
        int zje = ck + 1;
        Double[] ua2 = new Double[nua2];
        Double[] ub2 = new Double[nua2];
        Double[] uc2 = new Double[nua2];
        ua2 = rmsA2.clone();
        ub2 = rmsB2.clone();
        uc2 = rmsC2.clone();
        for (int i = sample / 4; i < (nua2 - sample / 4); i++) {
            List<Double> aList = Arrays.asList(Arrays.copyOfRange(rmsA2, i - sample / 4, (i + sample / 4) + 1));
            Collections.sort(aList);
            int a = aList.size();
            List<Double> bList = Arrays.asList(Arrays.copyOfRange(rmsB2, i - sample / 4, (i + sample / 4) + 1));
            Collections.sort(bList);
            List<Double> cList = Arrays.asList(Arrays.copyOfRange(rmsC2, i - sample / 4, (i + sample / 4) + 1));
            Collections.sort(cList);
            if (a % 2 == 0) {
                ua2[i] = (aList.get(zjs) + aList.get(zje)) / 2;
                ub2[i] = (bList.get(zjs) + bList.get(zje)) / 2;
                uc2[i] = (cList.get(zjs) + cList.get(zje)) / 2;
            } else {
                ua2[i] = aList.get(a / 2);
                ub2[i] = bList.get(a / 2);
                uc2[i] = cList.get(a / 2);
            }
        }

        Double[] udq = getMin(ua2, ub2, uc2);
        int te2 = 0;
        for (int i = sample - 1; i < ua2.length; i++) {
            if (udq[i] >= ut && udq[i - 1] < ut) {
                te2 = i;
                break;
            } else {
                te2 = ParseDataUtil.parseInteger(Math.round((double) winlen / 2)) + 2 * sample;//特殊情况下没有TE2，采用TE1位置代替
            }
        }
        Double[] sdva = getSDV(ua2, te2, winlen, matlen);
        Double[] sdvb = getSDV(ub2, te2, winlen, matlen);
        Double[] sdvc = getSDV(uc2, te2, winlen, matlen);
        double sdv = 0.0;
        for (int i = 0; i < sdva.length; i++) {
            if (i == 0) {
                sdv = sdva[i];
            } else {
                if (sdv < sdva[i]) {
                    sdv = sdva[i];
                }
            }
            if (sdv < sdvb[i]) {
                sdv = sdvb[i];
            }
            if (sdv < sdvc[i]) {
                sdv = sdvc[i];
            }
        }
        return sdv;
    }

    private Double[] diff(Double[] da1) {
        Double[] da11 = new Double[da1.length - 1];
        if (da1.length > 2) {
            for (int i = 0; i < da1.length - 1; i++) {
                da11[i] = da1[i + 1] - da1[i];
            }
        }
        return da11;
    }

    public Double[] getSDV(Double[] ua2, int te2, int winlen, int matlen) {
        Double[] da1 = Arrays.copyOfRange(ua2, te2 - winlen + ParseDataUtil.parseInteger(Math.round((double) winlen / 8)), te2 + ParseDataUtil.parseInteger(Math.round((double) winlen / 8)) + 1);
        Double[] da11 = diff(da1);
        Double[][] daH1 = new Double[matlen][matlen];
        int k = 0;
        for (int c = 0; c < matlen; c++) {
            for (int zx = 0; zx < matlen; zx++) {
                daH1[zx][c] = da11[k];
                k++;
            }
        }

        Matrix a = new Matrix(matlen, matlen);
        for (int i = 0; i < matlen; i++) {
            for (int j = 0; j < matlen; j++) {
                a.set(i, j, daH1[i][j]);
            }
        }
        SingularValueDecomposition s = a.svd();
        Matrix matrix = s.getS();
        Double[] sdv1 = new Double[matrix.getColumnDimension()];
        for (int i = 0; i < matrix.getColumnDimension(); i++) {
            double max = matrix.get(i, i);
            sdv1[i] = max;
        }
        return sdv1;
    }


    private double getDistortion(Double[] va, Double[] vb, Double[] vc, int ks, int end, int hh, double v, int sample) {
        double pu1 = 0;
        Double[] distortionVa = fFTdistortion(Arrays.copyOfRange(va, ks, end + 1), v, sample);
        Double[] distortionVb = fFTdistortion(Arrays.copyOfRange(vb, ks, end + 1), v, sample);
        Double[] distortionVc = fFTdistortion(Arrays.copyOfRange(vc, ks, end + 1), v, sample);
        double sum = 0;
        for (int i = 0; i < hh; i++) {
            sum = sum + distortionVa[i] + distortionVb[i] + distortionVc[i];
        }
        pu1 = sum / (3 * hh);
        return pu1;
    }

    private Double[] fFTdistortion(Double[] va, double v, int sample) {
        int n = va.length;
        for (int i = 0; i < n; i++) {
            va[i] = va[i] / v;
        }
        Double[][] xStat = new Double[5][n];
        for (int i = sample - 1; i < n; i++) {
            double spectrumamp = getFFT(Arrays.copyOfRange(va, i - sample + 1, i + 1), 2);
            spectrumamp = (spectrumamp / sample) * Math.sqrt(2);
            xStat[2][i] = spectrumamp;
            double spectrumamp4 = getFFT(Arrays.copyOfRange(va, i - sample + 1, i + 1), 4);
            spectrumamp4 = (spectrumamp4 / sample) * Math.sqrt(2);
            xStat[4][i] = spectrumamp4;
        }

        for (int i = 0; i < 5; i++) {
            for (int k = 0; k < sample - 1; k++) {
                if (xStat[i][sample - 1] == null) {
                    xStat[i][sample - 1] = 0.0;
                }
                xStat[i][k] = xStat[i][sample - 1];
            }
        }
        Double[] rMSharm = new Double[n];
        for (int i = 0; i < n; i++) {
            double val = 0;
            for (int k = 0; k < 5; k++) {
                if (xStat[k][i] == null) {
                    xStat[k][i] = 0.0;
                }
                val = val + Math.pow(xStat[k][i], 2);
            }
            rMSharm[i] = Math.sqrt(val);
        }
        return rMSharm;

    }

    public double getFFT(Double[] copyOfRange, int fl) {
        double real = 0.0;
        double imag = 0.0;
        int n = copyOfRange.length;
        for (int i = 0; i < copyOfRange.length; i++) {
            real = real + copyOfRange[i] * Math.cos((2 * Math.PI * fl * i) / n);
            imag = imag - copyOfRange[i] * Math.sin((2 * Math.PI * fl * i) / copyOfRange.length);
        }
        double rms = Math.sqrt(Math.pow(real, 2) + Math.pow(imag, 2));
        return rms;
    }

    private Map<String, Double> getSxsj(Double[] va, Double[] vb, Double[] vc, Double[] t, int qs, int js, double v, int sample, double f) {
        Compx[] phasorUa = this.dpDelayPhasor(Arrays.copyOfRange(va, qs, js), Arrays.copyOfRange(t, qs, js), sample, f);
        Compx[] phasorUb = this.dpDelayPhasor(Arrays.copyOfRange(vb, qs, js), Arrays.copyOfRange(t, qs, js), sample, f);
        Compx[] phasorUc = this.dpDelayPhasor(Arrays.copyOfRange(vc, qs, js), Arrays.copyOfRange(t, qs, js), sample, f);
//        dp_delay(Va1,t1,sample,f,phasor_ua,rms_a,angle_a);
//        dp_delay(Vb1,t1,sample,f,phasor_ub,rms_b,angle_b);
//        dp_delay(Vc1,t1,sample,f,phasor_uc,rms_c,angle_c);
        return getSxbph(phasorUa, phasorUb, phasorUc, v);
    }

    private Map<String, Double> getSxbph(Compx[] phasorUa, Compx[] phasorUb, Compx[] phasorUc, double v) {
        //旋转因子
        Compx a = new Compx();
        a.setFReal(Math.cos(Math.PI * 120 / 180));
        a.setFImag(Math.sin(Math.PI * 120 / 180));
        Compx a2 = new Compx();
        a.setFReal(Math.cos(Math.PI * 240 / 180));
        a.setFImag(Math.sin(Math.PI * 240 / 180));
        int n = phasorUa.length;
        double uoSum = 0;
        double unSum = 0;
        double upSum = 0;
        for (int i = 0; i < n; i++) {
            //正序电压
            double phasorUpR = phasorUa[i].fReal + (phasorUb[i].fReal * a.fReal - phasorUb[i].fImag * a.fImag) +
                    (phasorUc[i].fReal * a2.fReal - phasorUc[i].fImag * a2.fImag);
            double phasorUpI = phasorUa[i].fImag + (phasorUb[i].fReal * a.getFImag() + phasorUb[i].fImag * a.fReal) +
                    (phasorUc[i].fReal * a2.fImag + phasorUc[i].fImag * a2.fReal);

            double phasorUp = Math.abs(Math.sqrt(Math.pow(phasorUpR / 3, 2) + Math.pow(phasorUpI / 3, 2)));
            upSum = upSum + phasorUp;
            //负序电压
            double phasorUnR = phasorUa[i].fReal + (phasorUb[i].fReal * a2.fReal - phasorUb[i].fImag * a2.fImag) +
                    (phasorUc[i].fReal * a.fReal - phasorUc[i].fImag * a.fImag);
            double phasorUnI = phasorUa[i].fImag + (phasorUb[i].fReal * a2.fImag + phasorUb[i].fImag * a2.fReal) +
                    (phasorUc[i].fReal * a.fImag + phasorUc[i].fImag * a.fReal);
            double phasorUn = Math.abs(Math.sqrt(Math.pow(phasorUnR / 3, 2) + Math.pow(phasorUnI / 3, 2)));
            unSum = unSum + phasorUn;
            //零序电压
            double phasorU0R = phasorUa[i].fReal + phasorUb[i].fReal + phasorUc[i].fReal;
            double phasorU0I = phasorUa[i].fImag + phasorUb[i].fImag + phasorUc[i].fImag;
            double phasorU0 = Math.abs(Math.sqrt(Math.pow(phasorU0R / 3, 2) + Math.pow(phasorU0I / 3, 2)));
            uoSum = uoSum + phasorU0;
        }
        double uo = uoSum / n;
        double un = unSum / n;
        double up = upSum / n;
        double un0V = (uo / Math.sqrt(2)) / v;
        double unNV = (un / Math.sqrt(2)) / v;
        double unPV = (up / Math.sqrt(2)) / v;
        double unNp = 0d;
        if(unPV > 0 ){
            unNp = unNV / unPV;
        }
        if(unPV < 0 ){
            unNp = unNV / unPV;
        }
        Map<String, Double> map = new HashMap<String, Double>();
        map.put("UN_0V", un0V);
        map.put("UN_NV", unNV);
        map.put("UN_PV", unPV);
        map.put("UN_NP", unNp);
        return map;
    }

    public Compx[] dpDelayPhasor(Double[] va1, Double[] t1, int sample, double f) {
        int n = va1.length;
        Compx[] phasorUa = new Compx[n];
        Double[] upd = new Double[n];
        Double[] upq = new Double[n];
        this.dd(va1, t1, sample, f, upd, upq);
        //求正序幅值，即Va幅值
        for (int i = 0; i < n; i++) {
            Compx compx = new Compx();
            compx.fReal = upd[i] * (Math.sqrt(2) / Math.sqrt(3));
            compx.fImag = upq[i] * (Math.sqrt(2) / Math.sqrt(3));
            phasorUa[i] = compx;

        }
        return phasorUa;

    }

    public void dd(Double[] va1, Double[] t1, int sample, double f, Double[] upd, Double[] upq) {
        int n = va1.length;
        double w = 2 * Math.PI * f;
        int delay = Math.toIntExact(Math.round((double) sample / 6));
        for (int i = delay; i < n; i++) {
            double vc = -va1[i - delay];
            double vb = -va1[i] - vc;
            upd[i] = (Math.sqrt(2) / Math.sqrt(3)) * (Math.cos(2 * Math.PI * f * t1[i]) * va1[i] + Math.cos(2 * Math.PI * f * t1[i] - 2 * Math.PI / 3) * vb +
                    Math.cos(2 * Math.PI * f * t1[i] + 2 * Math.PI / 3) * vc);

            upq[i] = (Math.sqrt(2) / Math.sqrt(3)) * (Math.sin(2 * Math.PI * f * t1[i]) * va1[i] + Math.sin(2 * Math.PI * f * t1[i] - 2 * Math.PI / 3) * vb +
                    Math.sin(2 * Math.PI * f * t1[i] + 2 * Math.PI / 3) * vc);
        }
        for (int i = 0; i < delay; i++) {
            upd[i] = upd[delay];
            upq[i] = upq[delay];
        }
        lvbo(sample, upd);
        lvbo(sample, upq);
    }

    private void lvbo(int sample, Double[] upd) {
        //定义结构元素g
//        Double[] g = new Double[Math.round(sample)+1];
        int g = Math.round(sample) + 1;
        int n = upd.length;
        //首先腐蚀算法
        List<Double> eroder = Arrays.asList(upd);
        List<Double> dilata = Arrays.asList(upd);
        //腐蚀
        this.fs(eroder, g);
        //开运算——先腐蚀后膨胀
        this.pz(eroder, g);
        this.pz(eroder, g);
        this.fs(eroder, g);
        //膨胀
        this.pz(dilata, g);
        //闭运算——先膨胀后腐蚀
        this.fs(dilata, g);
        //开闭
        this.fs(dilata, g);
        this.pz(dilata, g);
//        Double[] OC=fs(pz(eroder,g),g);
//        Double[] CO=pz(fs(dilata,g),g);
        for (int i = 0; i < n; i++) {
            upd[i] = (eroder.get(i) + eroder.get(i)) / 2;
        }

    }

    private void pz(List<Double> upd1, int n) {
        int m = upd1.size();
        Double[][] a = new Double[n][m + n];
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < m; j++) {
                a[i][j + i] = upd1.get(j);
            }
        }
        Double[] b = new Double[m + n];

        for (int i = 0; i < (m + n); i++) {
            if (a[0][i] == null) {
                a[0][i] = -99999999.9;
            }
            double temp = a[0][i];
            for (int j = 1; j < n; j++) {
                if (a[j][i] == null) {
                    continue;
                }
                if (a[j][i] > temp) {
                    temp = a[j][i];
                }
            }

            b[i] = temp;
        }
//        Double[] E = new Double[m];
        for (int i = 0; i < m; i++) {
//            E[i] = B[i+Math.round((n-1)/2)];
            upd1.set(i, b[Math.toIntExact(i + Math.round((double) (n - 1) / 2))]);
        }
    }

    private void fs(List<Double> upd1, int n) {
        int m = upd1.size();
        Double[][] a = new Double[n][m + n];
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < m; j++) {
                a[i][j - i + n - 1] = upd1.get(j);
            }
        }
        Double[] b = new Double[n + m];
        for (int i = 0; i < (m + n); i++) {
            if (a[0][i] == null) {
                a[0][i] = 99999999.9;
            }
            double temp = a[0][i];
            for (int j = 1; j < n; j++) {
                if (a[j][i] == null) {
                    continue;
                }
                if (a[j][i] < temp) {
                    temp = a[j][i];
                }
            }
            b[i] = temp;
        }
//        Double[] E = new Double[m];
        for (int i = 0; i < m; i++) {
            upd1.set(i, b[Math.toIntExact(i + Math.round((double) (n - 1) / 2))]);
        }

    }

    private int getTs(Double[] va, double ut) {
        int n = va.length;
        int ts = 0;
        for (int i = 1; i < n; i++) {
            if (va[i] < ut && va[i - 1] >= ut) {
                ts = i;
                break;
            }
        }
        return ts;
    }

    public Double[] rms(Double[] va, int sample) {
        int n = va.length;
        Double[] vaRms = new Double[va.length];
        for (int i = sample - 1; i <= n - 1; i++) {
            double rms2 = 0;
            for (int j = sample - 1; j >= 0; j--) {
                rms2 = rms2 + Math.pow(va[i - j], 2);
            }
            double rms = Math.sqrt(rms2) / Math.sqrt(sample);
            vaRms[i] = rms;
        }
        for (int i = 0; i < sample - 1; i++) {
            vaRms[i] = vaRms[sample - 1];
        }

        return vaRms;
    }

    public static int calSamplePerCycle(WaveDataInfo waveDataInfo) {
        Float frequency = waveDataInfo.getFrequency();
        List<SampInfo> sampInfoList = waveDataInfo.getSampInfoList();
        SampInfo sampInfo = sampInfoList.get(0);
        int begin = sampInfo.getBeginSamp();
        int end = Math.min(sampInfo.getEndSamp(), waveDataInfo.getChannelDetailInfoList().get(0).getChannelData().size());
        int samplePerCycle = (int) Math.pow(2, Math.round(Math.log(sampInfo.getSamp() / frequency) / Math.log(2)));
        return samplePerCycle;
    }

    public Double[] getChannelData(WaveDataInfo waveDataInfo, Integer i) {
        List<Float> collect = waveDataInfo.getChannelDetailInfoList().get(i).getChannelData().stream().map(KeyValuePair::getYField).collect(Collectors.toList());
        List<Double> collect1 = collect.stream().map(x -> Double.parseDouble(String.valueOf(x))).collect(Collectors.toList());
        Double[] ua = collect1.toArray(new Double[0]);
        return ua;
    }

    private ResultWithTotal<List<Map<String, Object>>> getMeterIds(List<Long> ids) {
        ConditionBlock idCondition = new ConditionBlock("monitoredid", ConditionBlock.OPERATOR_IN, ids);
        List<ConditionBlock> filters = new ArrayList<>();
        filters.add(idCondition);
        ConditionBlock labelCondition = new ConditionBlock("monitoredlabel", ConditionBlock.OPERATOR_EQ, "line");
        filters.add(labelCondition);
        return ModelServiceUtils.querySingleModel(null, "measuredby", filters, null, null);
    }


    private boolean calcDifference(PqEventVariation event) {
        Double v1 = event.getV1magnitude();
        Double v2 = event.getV2magnitude();
        Double v3 = event.getV3magnitude();

        // 收集不为null的幅值
        List<Double> values = Arrays.asList(v1, v2, v3)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (values.isEmpty()) {
            // 全部为null，无法判断
            return false;
        }

        double max = Collections.max(values);
        double min = Collections.min(values);

        // 差值小于5
        return Math.abs(max - min) < 5;
    }

    /**
     * @Description: 匹配电压变动事件特征值
     **/
    private void convertPqTransient(PqEventVariation pqEvent, List<PqEventData> pqData17) {
        List<PqEventData> pqEventDataList = pqData17.stream().filter(t -> pqEvent.getDeviceid().equals(t.getDeviceId()) && pqEvent.getEventtime().equals(t.getDataTime())).collect(Collectors.toList());
        Integer pqvariationeventtype = 1;
        Double magnitude = 0.0;
        Long duration = 0L;
        if (!CollectionUtils.isEmpty(pqEventDataList)) {
            PqEventData pqEventData = pqEventDataList.get(0);
            Double nominalvoltage = pqEventData.getVolt();
            Long v1duration = pqEventData.getV1duration();
            Double v1magnitude = pqEventData.getV1mag();
            Long v2duration = pqEventData.getV2duration();
            Double v2magnitude = pqEventData.getV2mag();
            Long v3duration = pqEventData.getV3duration();
            Double v3magnitude = pqEventData.getV3mag();
            if (pqEventData.getV1mag() != null) {
                magnitude = pqEventData.getV1mag();
                duration = pqEventData.getV1duration();
            }
            if (Math.abs(pqEventData.getV2mag() - 100) > Math.abs(magnitude - 100)) {
                magnitude = pqEventData.getV2mag();
                duration = pqEventData.getV2duration();
            }
            if (Math.abs(pqEventData.getV3mag() - 100) > Math.abs(magnitude - 100)) {
                magnitude = pqEventData.getV3mag();
                duration = pqEventData.getV3duration();
            }
            pqEvent.setV1magnitude(v1magnitude);
            pqEvent.setV2magnitude(v2magnitude);
            pqEvent.setV3magnitude(v3magnitude);
            pqEvent.setV1duration(v1duration);
            pqEvent.setV2duration(v2duration);
            pqEvent.setV3duration(v3duration);
        } else {
            if (pqEvent.getV1magnitude() != null) {
                magnitude = pqEvent.getV1magnitude();
                duration = pqEvent.getDuration();
            }
            if (Math.abs(pqEvent.getV2magnitude() - 100) > Math.abs(magnitude - 100)) {
                magnitude = pqEvent.getV2magnitude();
                duration = pqEvent.getV2duration();
            }
            if (Math.abs(pqEvent.getV3magnitude() - 100) > Math.abs(magnitude - 100)) {
                magnitude = pqEvent.getV3magnitude();
                duration = pqEvent.getV3duration();
            }
        }
        pqEvent.setMagnitude(magnitude);
        pqEvent.setDuration(duration);
        pqEvent.setPqvariationeventtype(pqvariationeventtype);
    }

    private List<PqEventData> queryPqEventSpeed(PqEventVariation pqEventTransient) {
        Long startTime = pqEventTransient.getEventtime();
        Long endTime = pqEventTransient.getEventtime() + 6000;
        TrendSearchListVo trendSearchListVo = new TrendSearchListVo();
        List<TrendSearchVo> trendSearchVos = new ArrayList<>();
        for (Long dataId = 3000021L; dataId <= 3000027; dataId++) {
            TrendSearchVo trendSearchVo = new TrendSearchVo();
            trendSearchVo.setDataId(dataId);
            trendSearchVo.setLogicalId(1);
            trendSearchVo.setDataTypeId(1);
            trendSearchVo.setDeviceId(pqEventTransient.getDeviceid());
            trendSearchVos.add(trendSearchVo);
        }
        trendSearchListVo.setStartTime(DateUtil.formatDate(startTime, DateUtil.DATE_TO_STRING_DETAIAL_PATTERN));
        trendSearchListVo.setEndTime(DateUtil.formatDate(endTime, DateUtil.DATE_TO_STRING_DETAIAL_PATTERN));
        trendSearchListVo.setInterval(1);
        ResultWithTotal<Object> highSpeedData = deviceDataService.highSpeedData(trendSearchListVo);
        List<PqEventData> pqEventDataList = new ArrayList<>();
        if (highSpeedData.getCode() == Result.SUCCESS_CODE) {
            List<PqEventSpeed> pqEventSpeeds = ParseDataUtil.parseList(highSpeedData.getData());
            Map<String, List<PqEventSpeed>> pqEventSpeedMap = pqEventSpeeds.stream().collect(Collectors.groupingBy(t -> t.getDataId() + "#" + t.getUnixTime()));
            pqEventSpeedMap.forEach((k, v) -> {
                PqEventData pqEventData = new PqEventData();
                String[] split = k.split("#");
                Long deviceId = ParseDataUtil.parseLong(split[0]);
                Long dataTime = ParseDataUtil.parseLong(split[1]) * 1000;
                AtomicReference<Integer> dataTypeId = new AtomicReference<>(0);
                AtomicReference<Integer> logicalId = new AtomicReference<>(0);
                pqEventData.setDeviceId(deviceId);
                pqEventData.setDataTime(dataTime);
                v.forEach(pqEventSpeed -> {
                    dataTypeId.set(pqEventSpeed.getDataTypeId());
                    logicalId.set(pqEventSpeed.getLogicalId());
                    Long dataId = pqEventSpeed.getDataId();
                    if (dataId == 3000021) {
                        pqEventData.setNominalvolt(pqEventSpeed.getValue());
                    } else if (dataId == 3000022) {
                        pqEventData.setV1duration(ParseDataUtil.parseLong(pqEventSpeed.getValue()));
                    } else if (dataId == 3000023) {
                        pqEventData.setV1mag(pqEventSpeed.getValue());
                    } else if (dataId == 3000024) {
                        pqEventData.setV2duration(ParseDataUtil.parseLong(pqEventSpeed.getValue()));
                    } else if (dataId == 3000025) {
                        pqEventData.setV2mag(pqEventSpeed.getValue());
                    } else if (dataId == 3000026) {
                        pqEventData.setV3duration(ParseDataUtil.parseLong(pqEventSpeed.getValue()));
                    } else if (dataId == 3000027) {
                        pqEventData.setV3mag(pqEventSpeed.getValue());
                    }
                });
                pqEventData.setLogicalId(logicalId.get());
                pqEventData.setDataTypeId(dataTypeId.get());
                pqEventDataList.add(pqEventData);
            });
        }
        return pqEventDataList;
    }

    /**
     * @Description: 读取暂态事件
     **/
    private List<EventVo> readEventVo(Integer year) {
        List<EventVo> eventVos = new ArrayList<>();
        Long startId = pqEventMapper.queryEventStartId(year);
        EventCondition condition = new EventCondition();
        List<Integer> eventTypes = Arrays.asList(17, 18);
        condition.setEventTypes(eventTypes);
        //每次处理20000个暂态事件
        ResultWithTotal<List<EventVo>> eventResult = deviceDataService.queryEventById(startId, null, 0, 20000, year, condition);
        if (eventResult.getCode() == 0 || !CollectionUtils.isEmpty(eventResult.getData())) {
            List<EventVo> eventLogVos = eventResult.getData();
            String tableName = "PD_TB_09_" + year;
            List<WaveData> waveDataList = dataMapper.queryWave(eventLogVos, tableName);
            AtomicInteger count = new AtomicInteger(0);
            eventLogVos.forEach(eventVo -> {
                eventVos.add(eventVo);
                waveDataList.forEach(waveData -> {
                    long eventLogtime = eventVo.getEventTime() + eventVo.getMsec();
                    long waveLogtime = waveData.getLogTime().getTime() + waveData.getMsec();
                    //事件时间 -500ms < 录波文件时间 < 事件时间+500ms
                    if (eventVo.getDeviceId().equals(waveData.getDeviceId()) && waveLogtime > (eventLogtime - 500) && waveLogtime < (eventLogtime + 500)) {
                        eventVo.setWaveTime(waveLogtime);
                        count.getAndIncrement();
                    }
                });
            });
            log.info("read event and match wave count {}.", count.get());
        }
        return eventVos;
    }

    /**
     * 处理风电暂态事件脱网状态
     *
     * @param value
     */
    private void dealWindEscapeEvent(PqEventVariation value) {
        // 获取总时间
        Long duration = value.getDuration();
        // 获取总特征幅值
        Double magnitude = value.getMagnitude();
        //脱网状态：0:未脱网;1:低压脱网;2:高压脱网;
        Integer escapestatus = 0;
        if (magnitude >= 0 && magnitude < 20) {
            escapestatus = 1;
        } else if (magnitude == 20 && duration == 625000) {
            escapestatus = 1;
        } else if (magnitude > 20 && magnitude < 90 && duration > 2000000) {
            escapestatus = 1;
        } else if (magnitude > 110 && magnitude <= 120 && duration == 10000000) {
            escapestatus = 2;
        } else if (magnitude > 120 && magnitude <= 125 && duration == 1000000) {
            escapestatus = 2;
        } else if (magnitude > 125 && magnitude <= 130 && duration == 500000) {
            escapestatus = 2;
        } else if (magnitude > 130) {
            escapestatus = 2;
        }
        value.setEscapestatus(escapestatus);

    }

    /**
     * 处理光伏(10kV，35kV及以上)暂态事件脱网状态
     *
     * @param value
     */
    private void dealPhotoEscapeEvent(PqEventVariation value) {
        // 获取总时间
        Long duration = value.getDuration();
        // 获取总特征幅值
        Double magnitude = value.getMagnitude();
        //脱网状态：0:未脱网;1:低压脱网;2:高压脱网;
        Integer escapestatus = 0;
        if (magnitude >= 0 && magnitude < 20) {
            escapestatus = 1;
        } else if (magnitude == 20 && duration == 625000) {
            escapestatus = 1;
        } else if (magnitude > 20 && magnitude < 90 && duration > 2000000) {
            escapestatus = 1;
        } else if (magnitude > 110 && magnitude <= 120 && duration == 10000000) {
            escapestatus = 2;
        } else if (magnitude >= 120 && magnitude <= 130 && duration == 500000) {
            escapestatus = 2;
        } else if (magnitude > 130) {
            escapestatus = 2;
        }
        value.setEscapestatus(escapestatus);

    }

    /**
     * 处理光伏暂态事件脱网状态(380V、10Kv及以下)
     *
     * @param value
     */
    private void dealPhotoEscapeEvent380V(PqEventVariation value) {
        // 获取总时间
        Long duration = value.getDuration();
        // 获取总特征幅值
        Double magnitude = value.getMagnitude();
        //脱网状态：0:未脱网;1:低压脱网;2:高压脱网;
        Integer escapestatus = 0;
        if (magnitude < 50 && duration > 200000) {
            escapestatus = 1;
        } else if (magnitude >= 50 && magnitude < 85 && duration > 2000000) {
            escapestatus = 1;
        } else if (magnitude >= 135 && duration > 200000) {
            escapestatus = 2;
        } else if (magnitude > 115 && magnitude < 135 && duration == 2000000) {
            escapestatus = 2;
        }
        value.setEscapestatus(escapestatus);

    }


}
