package com.cet.pqscheduleservice.service.impl;

import com.cet.pqscheduleservice.mapper.config.ConfigMapper;
import com.cet.pqscheduleservice.mapper.matterhorn.MatterhornMapper;
import com.cet.pqscheduleservice.mapper.matterhorn.PqHistoryStatusMapper;
import com.cet.pqscheduleservice.model.common.Constants;
import com.cet.pqscheduleservice.model.common.Result;
import com.cet.pqscheduleservice.model.config.PecConfig;
import com.cet.pqscheduleservice.model.config.StationConfig;
import com.cet.pqscheduleservice.model.datalog.DataLogResult;
import com.cet.pqscheduleservice.model.datalog.DataLogUpdateParam;
import com.cet.pqscheduleservice.model.datalog.DataLogUpdateResult;
import com.cet.pqscheduleservice.model.enums.MonitorStatusEnum;
import com.cet.pqscheduleservice.model.event.Measuredby;
import com.cet.pqscheduleservice.model.line.LineVO;
import com.cet.pqscheduleservice.model.status.PqHistoryStatus;
import com.cet.pqscheduleservice.model.status.PqMonitorStatus;
import com.cet.pqscheduleservice.model.status.PqStatus;
import com.cet.pqscheduleservice.service.PqHistoryStatusService;
import com.cet.pqscheduleservice.utils.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/29 13:17
 * @description
 */
@Slf4j
@Service
public class PqHistoryStatusServiceImpl implements PqHistoryStatusService {

    @Autowired
    private PqHistoryStatusMapper pqHistoryStatusMapper;

    @Value("${pq.dataId}")
    private Long dataId;

    @Value("${pq.version}")
    private Float version;

    @Autowired
    private ConfigMapper configMapper;

    @Autowired
    private MatterhornMapper matterhornMapper;

    @Override
    public Integer dealHistoryStatus(Long logTime) {
//        //读取需要更新的状态
//        List<PqStatus> pqStatuses = pqHistoryStatusMapper.queryPqStatus(dataId);
//        if (CollectionUtils.isEmpty(pqStatuses)) {
//            //判断是否为首次统计
//            Long count = pqHistoryStatusMapper.queryHistoryStatusCount();
//            if (count == 0) {
//                pqStatuses = pqHistoryStatusMapper.queryAllPqStatus(dataId);
//            } else {
//                log.info("no status update.");
//                return 0;
//            }
//        }
        //节点树需要隐藏展示的监测点
        Set<Long> hiddenIdList = new HashSet<>();
        //读取本地缓存最新时间
        List<PqStatus> pqStatuses = dealDataLogCache(logTime, hiddenIdList);
        if (CollectionUtils.isEmpty(pqStatuses)) {
            return 0;
        }

        List<PqHistoryStatus> pqHistoryStatuses = matchFaultPqHistoryStatus(pqStatuses);
        //添加监测点状态表
        pqHistoryStatusMapper.insertPqHistoryStatus(pqHistoryStatuses);
        log.info("update pqhistorystatus count {}.", pqHistoryStatuses.size());
        //更新监测点状态
        List<PqHistoryStatus> lineStatuses = pqHistoryStatuses.stream().filter(t -> t.getAggregationcycle() == 12).collect(Collectors.toList());
        pqHistoryStatusMapper.updateLineStatus(lineStatuses);
        log.info("update line count {}.", lineStatuses.size());
        //更新隐藏监测点状态
        if(Objects.equals(version, 4.2f)){
            hiddenStatusJudge(new ArrayList<>(hiddenIdList));
        }
        return pqHistoryStatuses.size();
    }

    private void hiddenStatusJudge(List<Long> hiddenIdList) {
        //调度和计量点根据当前状态去隐藏   隐藏状态为6
        List<LineVO> lineList = pqHistoryStatusMapper.queryMeterInfo();
        List<Long> idList = lineList.stream().map(LineVO::getId).collect(Collectors.toList());
        List<Long> needHiddenIdList = hiddenIdList.stream().filter(idList::contains).collect(Collectors.toList());
        // 设置为隐藏状态6
        batchUpdateStatus(needHiddenIdList,6);
        // 其他监测点置为在线
        List<Long> onlineIdList = idList.stream().filter(t -> !needHiddenIdList.contains(t)).collect(Collectors.toList());
        // 设置为在线
        batchUpdateStatus(onlineIdList,MonitorStatusEnum.run.getValue());
        log.info("update hidden line count {}.", needHiddenIdList.size());
        log.info("update online line count {}.", onlineIdList.size());
    }

    @Transactional
    public void batchUpdateStatus(List<Long> idList, Integer status) {
        int batchSize = 5000;
        List<List<Long>> batches = Lists.partition(idList, batchSize);
        batches.forEach(batch -> {
            pqHistoryStatusMapper.updatePqHistoryStatus(batch, status);
            // 每批完成后短暂延迟
            try { Thread.sleep(50); } catch (InterruptedException e) {}
        });
    }


    /**
     * @Description: 读取本地缓存最新时间
     **/
    private List<PqStatus> dealDataLogCache(Long logTime, Set<Long> needHiddenIdList) {
        // 读取缓存的网络配置
        List<PecConfig> pecConfigs = configMapper.queryAllSystemNetwork();
        //获取场站id和通道id
        List<Long> stationIds = configMapper.queryStationId();
        List<PecConfig> configList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(stationIds)) {
            configList = configMapper.queryChannelId(stationIds);
        }
        if (CollectionUtils.isEmpty(pecConfigs) || CollectionUtils.isEmpty(stationIds) || CollectionUtils.isEmpty(configList)) {
            return new ArrayList<>();
        }
        //组装channel和station的关系
        Map<Long, Set<Long>> channelMap = new HashMap<>();
        configList.forEach(config -> {
            Long stationId = config.getParentNodeId();
            Long channelId = config.getNodeId();
            if (channelMap.containsKey(stationId)) {
                Set<Long> channelIds = channelMap.get(stationId);
                channelIds.add(channelId);
            } else {
                Set<Long> channelIds = new TreeSet<>(Collections.singleton(channelId));
                channelMap.put(stationId, channelIds);
            }
        });
        //获取前台ip
        Map<Integer, String> frontIpMap = new HashMap<>(16);
        pecConfigs.forEach(config -> {
            StationConfig stationIPByData = getStationIPByData(config);
            frontIpMap.put(stationIPByData.getStationId(), stationIPByData.getStationIP().trim());
        });
        //查询监测点设备关系
        List<Measuredby> measuredbies = matterhornMapper.queryLineDevice();
        Map<Long, Long> lineDeviceMap = measuredbies.stream().collect(Collectors.toMap(Measuredby::getMeasuredBy, Measuredby::getMonitoredId));
        //读取缓存数据时间
        return readDataLogCacheTime(channelMap, lineDeviceMap, frontIpMap, logTime, needHiddenIdList);
    }

    /**
     * @Description: 读取DataCacheLibServer获取缓存数据时间
     **/
    private List<PqStatus> readDataLogCacheTime(Map<Long, Set<Long>> channelMap, Map<Long, Long> lineDeviceMap, Map<Integer, String> frontIPMap, Long logTime, Set<Long> needHiddenIdList) {
        List<PqStatus> pqStatuses = new ArrayList<>();
        frontIPMap.forEach((stationId, frontIP) -> {
            if (stationId == null || stationId == 0) {
                return;
            }
            DataLogUpdateParam dataLogUpdateParam = new DataLogUpdateParam();
            dataLogUpdateParam.setResultType(2);
            dataLogUpdateParam.setHistoryDataType(7);
            dataLogUpdateParam.setStaID(stationId);
            Set<Long> channelIds = channelMap.get(ParseDataUtil.parseLong(stationId));
            if (channelIds == null) {
                return;
            }
            dataLogUpdateParam.setChnIDsCnt(channelIds.size());
            dataLogUpdateParam.setChnIDs(new ArrayList<>(channelIds));
            //调用DataCacheLibServer服务，获取缓存时间
            String url = "http://" + frontIP + ":4500" + "/Cet/DataCache/QueryDataLogUpdateTime";
            String dataLogResult = HttpUtils.post(url, "application/json", JsonTransferUtils.toJsonString(dataLogUpdateParam), "utf-8");
            // 处理数缓存数据
            processDataLogResponse(lineDeviceMap, pqStatuses, logTime, needHiddenIdList, dataLogResult);
        });
        return pqStatuses;
    }

    /**
     * 处理数缓存数据
     */
    private void processDataLogResponse(Map<Long, Long> lineDeviceMap,
                                        List<PqStatus> pqStatuses,
                                        Long logTime,
                                        Set<Long> needHiddenIdList,
                                        String dataLogResult) {
        if (!StringUtils.isEmpty(dataLogResult)) {
            try {
                DataLogResult result = JsonTransferUtils.parseObject(dataLogResult, DataLogResult.class);
                if (Result.SUCCESS_CODE == result.getCode() && Objects.nonNull(result.getResult())) {
                    List<DataLogUpdateResult> dataLogUpdateResults = JsonTransferUtils.transferJsonString(JsonTransferUtils.toJsonString(result.getResult()), DataLogUpdateResult.class);
                    if(CollectionUtils.isEmpty(dataLogUpdateResults)){
                        return;
                    }
                    dealDataLogUpdateResult(lineDeviceMap, pqStatuses, dataLogUpdateResults, logTime, needHiddenIdList);
                } else {
                    log.error("request /Cet/DataCache/QueryDataLogUpdateTime error, code {} message {}.", result.getCode(), result.getMessage());
                }
            } catch (Exception e) {
                //兼容R4版本
                List<DataLogUpdateResult> dataLogUpdateResults = JsonTransferUtils.transferJsonString(dataLogResult, DataLogUpdateResult.class);
                if(CollectionUtils.isEmpty(dataLogUpdateResults)){
                    return;
                }
                dealDataLogUpdateResult(lineDeviceMap, pqStatuses, dataLogUpdateResults, logTime, needHiddenIdList);
            }
        }
    }

    private void dealDataLogUpdateResult(Map<Long, Long> lineDeviceMap, List<PqStatus> pqStatuses, List<DataLogUpdateResult> dataLogUpdateResults, Long logTime, Set<Long> needHiddenIdList) {
        for (DataLogUpdateResult dataLogUpdateResult : dataLogUpdateResults) {
            if(Objects.isNull(dataLogUpdateResult) || (Objects.isNull(dataLogUpdateResult.getDatas()) && Objects.isNull(dataLogUpdateResult.getResult()))){
                continue;
            }
            List<DataLogUpdateResult.Data> datas = dataLogUpdateResult.getDatas();
            List<DataLogUpdateResult.Data> updateResult = dataLogUpdateResult.getResult();
            if (!CollectionUtils.isEmpty(datas)) {
                handlePqStatus(lineDeviceMap, pqStatuses, datas,logTime, needHiddenIdList, this::getTimeFromData);
            } else if (!CollectionUtils.isEmpty(updateResult)) {
                handlePqStatus(lineDeviceMap, pqStatuses, updateResult,logTime, needHiddenIdList, this::getUpdateTimeFromData);
            }
        }
    }

    /**
     * 从data对象获取时间
     */
    private Double getTimeFromData(DataLogUpdateResult.Data data) {
        return data.getTime();
    }

    /**
     * 从data对象获取更新时间
     */
    private Double getUpdateTimeFromData(DataLogUpdateResult.Data data) {
        return data.getUpdateTime();
    }

    private void handlePqStatus(Map<Long, Long> lineDeviceMap, List<PqStatus> pqStatuses, List<DataLogUpdateResult.Data> datas, Long logTime, Set<Long> needHiddenIdList, Function<DataLogUpdateResult.Data, Double> timeExtractor) {

        datas.forEach(data -> {
            Double timeValue = timeExtractor.apply(data);
            if (timeValue == null || lineDeviceMap.get(data.getDevID()) == null) {
                return;
            }
            Long dayTime = delphiDateTimeToSecondDate(timeValue).getTime();
            //判断最新时间是否在5天内
            handleLineVisibility(lineDeviceMap.get(data.getDevID()), dayTime, needHiddenIdList);
            //时间大于昨天才为在运,支持历史状态
            if (logTime != null) {
                if (dayTime >= logTime) {
                    PqStatus pqStatus = new PqStatus(DateUtil.getFirstTimeOfDay(logTime),
                            data.getDevID(), lineDeviceMap.get(data.getDevID()));
                    pqStatuses.add(pqStatus);
                }
            } else {
                if (dayTime >= DateUtil.getPreFirstTimeOfDay()) {
                    PqStatus pqStatus = new PqStatus(DateUtil.getFirstTimeOfDay(System.currentTimeMillis()),
                            data.getDevID(), lineDeviceMap.get(data.getDevID()));
                    pqStatuses.add(pqStatus);
                }
            }
        });
    }

    /**
     * 处理设备可见性（隐藏超过5天未更新的设备）
     */
    private void handleLineVisibility(Long monitoredId, long dayTime, Set<Long> needHiddenIdList) {
        if (!isWithinLastFiveDays(dayTime) && Objects.equals(version, 4.2f)) {
            needHiddenIdList.add(monitoredId);
            log.debug("lineId {} added to hidden list due to no updates within 5 days", monitoredId);
        }
    }

    public static boolean isWithinLastFiveDays(Long timestamp) {
        // 获取当前时间的0点0分
        LocalDateTime now = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        // 将时间戳转换为LocalDateTime
        LocalDateTime timestampDateTime = Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        // 计算当前时间0点0分与时间戳之间的天数差
        long daysDifference = ChronoUnit.DAYS.between(timestampDateTime, now);
        // 判断是否在过去5天内
        return daysDifference >= 0 && daysDifference <= 5;
    }


    public static Date delphiDateTimeToSecondDate(double dateTime) {
        Calendar calendar = Calendar.getInstance();
        int gmtOffset = calendar.get(Calendar.ZONE_OFFSET) + calendar.get(Calendar.DST_OFFSET);
        long timeLong = (long) (dateTime * 86400000L) - (25569 * 86400000L) - gmtOffset;
        long millis = timeLong % 1000;
        if (millis != 0) {
            // 进位值，大于等于补齐到1秒，小于则舍去毫秒
            int carryValue = 500;
            if (millis >= carryValue) {
                timeLong = timeLong - millis + 1000;
            } else {
                timeLong = timeLong - millis;
            }
        }
        return new Date(timeLong);
    }

    private StationConfig getStationIPByData(PecConfig config) {
        DataInputStream dis = null;
        StationConfig stationConfig = new StationConfig();
        try {
            dis = new DataInputStream(new ByteArrayInputStream(config.getData()));
            LibSerializeHelper.readString(dis, 128);
            LibSerializeHelper.readShort(dis);
            stationConfig.setStationId(LibSerializeHelper.readInt(dis));
            stationConfig.setStationIP(LibSerializeHelper.readString(dis, 128));
        } catch (Exception e) {
            log.error("query station id error.", e);
        } finally {
            IOUtils.closeQuietly(dis);
        }
        return stationConfig;
    }

    /**
     * @Description: 组合故障监测点状态
     **/
    private List<PqHistoryStatus> matchFaultPqHistoryStatus(List<PqStatus> pqStatuses) {
        List<PqHistoryStatus> pqHistoryStatuses = new ArrayList<>();
        //处理非运行状态的监测点
        List<PqMonitorStatus> pqMonitorStatuses = pqHistoryStatusMapper.queryMonitorStatus();
        //过滤停运，检修，调试的监测点, 排除这部分监测点
        List<Long> repairMonitorIds = pqMonitorStatuses.stream().filter(t -> t.getStatus() == 2 || t.getStatus() == 3 || t.getStatus() == 4).map(PqMonitorStatus::getMonitoredId).collect(Collectors.toList());
        pqStatuses = pqStatuses.stream().filter(t -> !repairMonitorIds.contains(t.getMonitoredId())).collect(Collectors.toList());
        //处理状态信息
        Long logtime = pqStatuses.stream().map(PqStatus::getLogtime).max(Long::compareTo).get();
        //添加运行的监测点数
        matchRunPqHistoryStatus(pqStatuses, pqHistoryStatuses);
        //过滤非运行的监测点
        List<Long> allIds = pqMonitorStatuses.stream().map(PqMonitorStatus::getMonitoredId).collect(Collectors.toList());
        Map<Long, Integer> lineStatusMap = pqMonitorStatuses.stream().collect(Collectors.toMap(PqMonitorStatus::getMonitoredId, PqMonitorStatus::getStatus));
        List<Long> runMonitorIds = pqHistoryStatuses.stream().map(PqHistoryStatus::getPqmonitor_id).collect(Collectors.toList());
        Set<Long> faultMonitorIds = allIds.stream().filter(t -> !runMonitorIds.contains(t)).collect(Collectors.toSet());
        //月季年的状态逻辑调整，当月有一天为在线，则当月为在线，当季当年也为在线
        //查询故障监测点的历史月季年状态
        List<PqHistoryStatus> pqHistoryStatusesList = getPqHistoryStatuses(logtime, faultMonitorIds);
        for (Long pqmonitorId : faultMonitorIds) {
            for (int i = 0; i < Constants.PERIOD_ARRAY.length; ++i) {
                Integer status = lineStatusMap.get(pqmonitorId);
                if (MonitorStatusEnum.run.getValue().equals(status)) {
                    status = MonitorStatusEnum.fault.getValue();
                }
                int aggregationCycle = Constants.PERIOD_ARRAY[i];
                Long logTime = getLogTIme(aggregationCycle, logtime);
                //月季年的状态逻辑调整，当月有一天为在线，则当月为在线，当季当年也为在线
                if (!CollectionUtils.isEmpty(pqHistoryStatusesList)) {
                    Long finalLogTime = logTime;
                    List<PqHistoryStatus> historyStatuses = pqHistoryStatusesList.stream().filter(t -> MonitorStatusEnum.run.getValue().equals(t.getMonitorstatus())
                            && t.getPqmonitor_id().equals(pqmonitorId) && aggregationCycle == t.getAggregationcycle() && t.getLogtime().equals(finalLogTime)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(historyStatuses)) {
                        continue;
                    }
                }
                PqHistoryStatus pqHistoryStatus = new PqHistoryStatus();
                pqHistoryStatus.setAggregationcycle(aggregationCycle);
                pqHistoryStatus.setLogtime(logTime);
                pqHistoryStatus.setMonitorstatus(status);
                pqHistoryStatus.setRealtimestatus(status);
                pqHistoryStatus.setPqmonitor_id(ParseDataUtil.parseLong(pqmonitorId));
                pqHistoryStatus.setUpdatetime(System.currentTimeMillis());
                pqHistoryStatuses.add(pqHistoryStatus);
            }
        }
        return pqHistoryStatuses;
    }

    private static Long getLogTIme(int aggregationCycle, Long logtime) {
        Long logTime = 0L;
        if (aggregationCycle == Constants.PERIOD_DAY) {
            logTime = DateUtil.getFirstTimeOfDay(new Date(logtime)).getTime();
        } else if (aggregationCycle == Constants.PERIOD_MONTH) {
            logTime = DateUtil.getFirstDayOfMonth(new Date(logtime)).getTime();
        } else if (aggregationCycle == Constants.PERIOD_QUARTER) {
            logTime = DateUtil.getCurrentQuarterStartTime(new Date(logtime)).getTime();
        } else if (aggregationCycle == Constants.PERIOD_YEAR) {
            logTime = DateUtil.getFirstDayOfYear(new Date(logtime));
        }
        return logTime;
    }

    /**
     * @Description: 查询监测点当月当季当年的状态
     **/
    private List<PqHistoryStatus> getPqHistoryStatuses(Long logtime, Set<Long> faultMonitorIds) {
        List<PqHistoryStatus> pqHistoryStatusList = new ArrayList<>();
        faultMonitorIds.forEach(pqmonitorId -> {
            pqHistoryStatusList.add(new PqHistoryStatus(pqmonitorId, Constants.PERIOD_MONTH, DateUtil.getFirstDayOfMonth(new Date(logtime)).getTime()));
            pqHistoryStatusList.add(new PqHistoryStatus(pqmonitorId, Constants.PERIOD_QUARTER, DateUtil.getCurrentQuarterStartTime(new Date(logtime)).getTime()));
            pqHistoryStatusList.add(new PqHistoryStatus(pqmonitorId, Constants.PERIOD_YEAR, DateUtil.getFirstDayOfYear(new Date(logtime))));
        });
        return pqHistoryStatusMapper.queryHistoryStatus(pqHistoryStatusList);
    }

    /**
     * @Description: 组合运行监测点状态
     **/
    private void matchRunPqHistoryStatus(List<PqStatus> pqStatuses, List<PqHistoryStatus> pqHistoryStatuses) {
        pqStatuses.forEach(pqStatus -> {
            for (int i = 0; i < Constants.PERIOD_ARRAY.length; ++i) {
                int aggregationCycle = Constants.PERIOD_ARRAY[i];
                if (aggregationCycle == Constants.PERIOD_DAY) {
                    PqHistoryStatus pqHistoryStatus = new PqHistoryStatus();
                    pqHistoryStatus.setAggregationcycle(Constants.PERIOD_DAY);
                    pqHistoryStatus.setLogtime(DateUtil.getFirstTimeOfDay(new Date(pqStatus.getLogtime())).getTime());
                    pqHistoryStatus.setMonitorstatus(MonitorStatusEnum.run.getValue());
                    pqHistoryStatus.setRealtimestatus(MonitorStatusEnum.run.getValue());
                    pqHistoryStatus.setPqmonitor_id(ParseDataUtil.parseLong(pqStatus.getMonitoredId()));
                    pqHistoryStatus.setUpdatetime(System.currentTimeMillis());
                    pqHistoryStatuses.add(pqHistoryStatus);
                } else if (aggregationCycle == Constants.PERIOD_MONTH) {
                    PqHistoryStatus pqHistoryStatus = new PqHistoryStatus();
                    pqHistoryStatus.setAggregationcycle(Constants.PERIOD_MONTH);
                    pqHistoryStatus.setLogtime(DateUtil.getFirstDayOfMonth(new Date(pqStatus.getLogtime())).getTime());
                    pqHistoryStatus.setMonitorstatus(MonitorStatusEnum.run.getValue());
                    pqHistoryStatus.setRealtimestatus(MonitorStatusEnum.run.getValue());
                    pqHistoryStatus.setPqmonitor_id(ParseDataUtil.parseLong(pqStatus.getMonitoredId()));
                    pqHistoryStatus.setUpdatetime(System.currentTimeMillis());
                    pqHistoryStatuses.add(pqHistoryStatus);
                } else if (aggregationCycle == Constants.PERIOD_QUARTER) {
                    PqHistoryStatus pqHistoryStatus = new PqHistoryStatus();
                    pqHistoryStatus.setAggregationcycle(Constants.PERIOD_QUARTER);
                    pqHistoryStatus.setLogtime(DateUtil.getCurrentQuarterStartTime(new Date(pqStatus.getLogtime())).getTime());
                    pqHistoryStatus.setMonitorstatus(MonitorStatusEnum.run.getValue());
                    pqHistoryStatus.setRealtimestatus(MonitorStatusEnum.run.getValue());
                    pqHistoryStatus.setPqmonitor_id(ParseDataUtil.parseLong(pqStatus.getMonitoredId()));
                    pqHistoryStatus.setUpdatetime(System.currentTimeMillis());
                    pqHistoryStatuses.add(pqHistoryStatus);
                } else if (aggregationCycle == Constants.PERIOD_YEAR) {
                    PqHistoryStatus pqHistoryStatus = new PqHistoryStatus();
                    pqHistoryStatus.setAggregationcycle(Constants.PERIOD_YEAR);
                    pqHistoryStatus.setLogtime(DateUtil.getFirstDayOfYear(new Date(pqStatus.getLogtime())));
                    pqHistoryStatus.setMonitorstatus(MonitorStatusEnum.run.getValue());
                    pqHistoryStatus.setRealtimestatus(MonitorStatusEnum.run.getValue());
                    pqHistoryStatus.setPqmonitor_id(ParseDataUtil.parseLong(pqStatus.getMonitoredId()));
                    pqHistoryStatus.setUpdatetime(System.currentTimeMillis());
                    pqHistoryStatuses.add(pqHistoryStatus);
                }
            }
        });
    }
}
