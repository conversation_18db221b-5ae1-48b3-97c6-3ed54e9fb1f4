package com.cet.pqscheduleservice.controller;

import com.cet.pqscheduleservice.model.common.Result;
import com.cet.pqscheduleservice.service.PqEventService;
import com.cet.pqscheduleservice.service.PqHistoryStatusService;
import com.cet.pqscheduleservice.service.PqToleranceService;
import com.cet.pqscheduleservice.service.ProcedureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @ClassName ：TestController
 * @date ：Created in 2021/2/20 16:40
 * @description： 历史数据接口
 */
@Api(value="DataLogController",tags={"DataLogController"})
@RestController
@RequestMapping("/pq/schedule")
@Slf4j
public class ProcedureController {

    @Autowired
    @Qualifier("taskExecutor")
    private TaskExecutor taskExecutor;

    @Autowired
    private ProcedureService procedureService;

    @Autowired
    private PqHistoryStatusService pqHistoryStatusService;

    @Autowired
    private PqEventService pqEventService;

    @Autowired
    private PqToleranceService pqToleranceService;

    @ApiOperation(value = "执行每日存储过程")
    @GetMapping(value = "/callPqStable", produces = "application/json")
    public Result<String> callPqStable(){
        taskExecutor.execute(() -> procedureService.callPqStable());
        return Result.success("执行成功，请查看日志观察执行情况");
    }


    @ApiOperation(value = "执行上月存储过程")
    @GetMapping(value = "/callPqLastMonthStable", produces = "application/json")
    public Result<String> callPqLastMonthStable(){
        taskExecutor.execute(() -> procedureService.callMonthPqStable());
        return Result.success("执行成功，请查看日志观察执行情况");
    }


    @ApiOperation(value = "执行监测点状态统计")
    @GetMapping(value = "/dealStatus", produces = "application/json")
    public Result<Integer> dealStatus(@RequestParam(required = false, value = "logTime") Long logTime){
        taskExecutor.execute(() -> pqHistoryStatusService.dealHistoryStatus(logTime));
        return Result.success("执行成功，请查看日志观察执行情况");
    }

    @ApiOperation(value = "执行暂态事件统计")
    @GetMapping(value = "/dealPqEvent", produces = "application/json")
    public Result<Integer> dealPqEvent(){
        taskExecutor.execute(() -> pqEventService.dealPqEvent());
        return Result.success("执行成功，请查看日志观察执行情况");
    }

    @ApiOperation(value = "执行广东治理设备暂态事件统计")
    @GetMapping(value = "/dealDeviceEvent", produces = "application/json")
    public Result<Integer> dealDeviceEvent(@RequestParam(required = false) Integer year){
        return Result.success(pqEventService.dealDeviceEvent(year));
    }

    @ApiOperation(value = "执行semi和itic统计")
    @GetMapping(value = "/dealToleranceEvent", produces = "application/json")
    public Result<Integer> dealToleranceEvent(){
        taskExecutor.execute(() -> pqToleranceService.dealToleranceEvent());
        return Result.success("执行成功，请查看日志观察执行情况");
    }

    @ApiOperation(value = "执行暂态事件原因分析判断")
    @GetMapping(value = "/dealEventCauseAnalysis", produces = "application/json")
    public Result<Integer> dealEventCauseAnalysis(@RequestParam(required = false, value = "logTime") Long logTime){
        taskExecutor.execute(() -> pqEventService.dealEventCauseAnalysis(logTime));
        return Result.success("执行成功，请查看日志观察执行情况");
    }
}
