package com.cet.pqscheduleservice.task;

import com.cet.pqscheduleservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName ：ProcedureTask
 * @date ：Created in 2022/3/2 10:36
 * @description： 存储过程定时任务
 */
@Component
@Slf4j
public class ProcedureTask {

    @Autowired
    private ProcedureService procedureService;

    @Autowired
    private PqHistoryStatusService pqHistoryStatusService;

    @Autowired
    private PqEventService pqEventService;

    @Autowired
    private PqToleranceService pqToleranceService;

    @Autowired
    @Qualifier("taskExecutor")
    private TaskExecutor taskExecutor;


    @Value("${deviceEvent.enabled}")
    private Boolean deviceEventEnabled;

    @Value("${procedure.event.enabled}")
    private Boolean pqEventEnabled;

    @Value("${procedure.cause.enabled}")
    private Boolean eventCauseEnabled;

    @Scheduled(cron = "${procedure.daily.cron}")
    public void procedureTask() {
        log.info("start exec procedure.");
        taskExecutor.execute(() -> procedureService.callPqStable());
        log.info("end exec procedure.");
    }

    @Scheduled(cron = "${procedure.month.cron}")
    public void procedureMonthTask() {
        log.info("start exec month procedure.");
        taskExecutor.execute(() -> procedureService.callMonthPqStable());
        log.info("end exec month procedure.");
    }

    @Scheduled(cron = "${procedure.status.cron}")
    public void procedurePqHistoryStatus() {
        try {
            log.info("start deal pqhistory status.");
            taskExecutor.execute(() -> pqHistoryStatusService.dealHistoryStatus(null));
            log.info("end deal pqhistory status.");
        } catch (Exception e) {
            log.error("deal pqhistory error.", e);
        }
    }

    @Scheduled(cron = "${procedure.event.cron}")
    public void procedurePqEvent() {
        if (!Boolean.TRUE.equals(pqEventEnabled)) {
            return;
        }
        try {
            log.info("start deal pqEvent.");
            taskExecutor.execute(() -> pqEventService.dealPqEvent());
            log.info("end deal pqEvent.");
        } catch (Exception e) {
            log.error("deal pqEvent error.", e);
        }
    }

    @Scheduled(cron = "${procedure.tolerance.cron}")
    public void procedurePqTolerance() {
        if (!Boolean.TRUE.equals(pqEventEnabled)) {
            return;
        }
        try {
            log.info("start deal pq tolerance.");
            taskExecutor.execute(() -> pqToleranceService.dealToleranceEvent());
            log.info("end deal pqEvent.");
        } catch (Exception e) {
            log.error("deal pq tolerance error.", e);
        }
    }

    @Scheduled(cron = "${deviceEvent.cron}")
    public void deviceEventDeal() {
        if (!Boolean.TRUE.equals(deviceEventEnabled)) {
            return;
        }
        try {
            log.info("start deal deviceEvent.");
            taskExecutor.execute(() -> pqEventService.dealDeviceEvent(null));
            log.info("end deal deviceEvent.");
        } catch (Exception e) {
            log.error("deal deviceEvent error.", e);
        }
    }

    @Scheduled(cron = "${procedure.cause.cron}")
    public void procedureEventCauseAnalysis() {
        if (!Boolean.TRUE.equals(eventCauseEnabled)) {
            return;
        }
        try {
            log.info("start deal event cause analysis.");
            taskExecutor.execute(() -> pqEventService.dealEventCauseAnalysis(null));
            log.info("end deal event cause analysis.");
        } catch (Exception e) {
            log.error("deal event cause analysis error.", e);
        }
    }

}
