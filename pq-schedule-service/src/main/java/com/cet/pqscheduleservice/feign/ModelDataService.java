package com.cet.pqscheduleservice.feign;

import com.cet.pqscheduleservice.model.common.Result;
import com.cet.pqscheduleservice.model.common.ResultWithTotal;
import com.cet.pqscheduleservice.model.query.FlatWriteData;
import com.cet.pqscheduleservice.model.query.QueryCondition;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 模型服务的数据接口
 * 
 * <AUTHOR>
 */
@FeignClient("model-service")
public interface ModelDataService {
	/**
	 * 表计
	 */
	static final String METER = "meter";
	/**
	 * 模型实例和Peccore节点的映射
	 */
	static final String MODEL_INSTANCE_AND_PECNODE_MAP = "modelinstanceandpecnodemap";
	/**
	 * 测点
	 */
	static final String MEASURE_ITEM = "measureitem";

	/**
	 * 通用查询接口，支持单模型列表、层次结构及树型结构返回 接口对查询数据的条数有做限制，每一层级返回条数不超过1万条，最多不超过5层
	 * 
	 * @param condition
	 * @return
	 */
	@PostMapping("/model-service/model/v1/query")
		//@ValidModelAuth
	ResultWithTotal<List<Map<String, Object>>> query(@RequestBody QueryCondition condition);

	@PostMapping("/model-service/model/v1/query")
	ResultWithTotal<List<Map<String, Object>>> queryQuantityAggData(@RequestBody QueryCondition condition);

	/**
	 * 通用查询接口，支持单模型列表、层次结构及树型结构返回 接口对查询数据的条数有做限制，每一层级返回条数不超过1万条，最多不超过5层
	 *
	 * @param condition
	 * @return
	 */
	@PostMapping("/model-service/model/v1/query")
	//@ValidModelAuth
	<T> ResultWithTotal<List<T>> queryObj(@RequestBody QueryCondition condition);


	/**
	 * 根据Id删除数据
	 *
	 * @param modelLabel
	 * @param idRange
	 * @return
	 */
	@DeleteMapping("/model-service/model/v1/{modelLabel}")
	//@ValidModelAuth
	Result<Object> deleteById(@PathVariable("modelLabel") String modelLabel, @RequestBody List<Integer> idRange);

	/**
	 * 批量写入层次模型数据
	 *
	 * @param data
	 * @return
	 */
	@PostMapping("/model-service/model/v1/write/hierachy")
	//@ValidModelAuth
	Result<Object> write(@RequestBody Object data, @RequestParam("ignoreDuplicate") Boolean ignoreDuplicate, @RequestParam("returnData") Boolean returnData);

	/**
	 * 批量写入层次模型数据
	 *
	 * @param data
	 * @return
	 */
	@PostMapping("/model-service/model/v1/write/hierachy")
	//@ValidModelAuth
	Result<Object> write(@RequestBody Object data);

	/**
	 * 批量写入层次模型数据
	 *
	 * @param flatWriteData
	 * @return
	 */
	@PostMapping("/model/v1/write/flat")
	//@ValidModelAuth
	Result<Object> flatWrite(@RequestBody FlatWriteData flatWriteData);
	
}
