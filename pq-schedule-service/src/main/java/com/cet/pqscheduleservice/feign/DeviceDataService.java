package com.cet.pqscheduleservice.feign;

import com.cet.pqscheduleservice.model.common.ResultWithTotal;
import com.cet.pqscheduleservice.model.datalog.EventCondition;
import com.cet.pqscheduleservice.model.datalog.TrendSearchListVo;
import com.cet.pqscheduleservice.model.event.EventVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Description 设备数据服务
 * @Data Created in ${Date}
 */
@FeignClient(value = "${device.data.name}")
public interface DeviceDataService {

    @PostMapping("/device-data-service/api/v1/highspeed/datalog/span")
    ResultWithTotal<Object> highSpeedData(@RequestBody TrendSearchListVo searchVo);



    /**
     * 查询指定范围内的事件，仅支持分页查询
     *
     * @param startId
     * @param endId
     * @param index
     * @param limit
     * @param year
     * @param condition
     * @return
     */
    @PostMapping("/device-data-service/api/event/v1/data/idrange/{year}")
    public ResultWithTotal<List<EventVo>> queryEventById(@RequestParam("startId") Long startId,
                                                         @RequestParam("endId") Long endId, @RequestParam("index") Integer index,
                                                         @RequestParam("limit") Integer limit, @PathVariable("year") Integer year, EventCondition condition);


    /**
     * 查询指定设备id，制定触发时间的波形
     *
     * @param deviceId
     * @param waveTime
     * @return
     */
    @GetMapping("/device-data-service/api/wave/v1/data/moment/{deviceId}")
    ResultWithTotal<List<String>> queryWaveByDeviceId(@RequestParam("deviceId") Long deviceId, @RequestParam("waveTime") Long waveTime);

}
