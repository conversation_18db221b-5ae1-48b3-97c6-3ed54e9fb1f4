package com.cet.pqscheduleservice.model.datalog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="DataLogResult",description="服务接口返回结果")
@SuppressWarnings({ "rawtypes", "unchecked" })
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataLogResult<T> implements Serializable {

	public static final int SUCCESS_CODE = 0;

	private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="错误码",name="code",example="1")
    private Integer code;

    @ApiModelProperty(value="错误信息",name="msg",example="null")
    private String message;

    @ApiModelProperty(value="数据",name="data",example="xxx")
    private T result;

	public static DataLogResult success() {
		return new DataLogResult(0,"操作成功",null);
    }
    
    public static <T> DataLogResult<T> success(T t){
    	return new DataLogResult<T>(0, "success", t);
    }

    public static <T> DataLogResult<T> success(String msg){
        return new DataLogResult<T>(0, msg, null);
    }
    
    public static DataLogResult error(String msg) {
		return new DataLogResult(500,msg,null);
    }
    public static <T> DataLogResult<T> error(T t) {
		return new DataLogResult(500,"error",t);
    }
}
