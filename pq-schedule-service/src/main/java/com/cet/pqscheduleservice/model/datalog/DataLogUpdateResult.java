package com.cet.pqscheduleservice.model.datalog;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/2 8:56
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataLogUpdateResult {
    private Long chnID;
    private Long count;
    private List<Data> datas;
    private List<Data> result;

    @AllArgsConstructor
    @NoArgsConstructor
    @lombok.Data
    public static class Data {
        private Long devID;
        private Double time;
        private Double updateTime;
    }
}
