package com.cet.pqscheduleservice.model.event;

import lombok.Data;

/**
 * @Title: DeviceEvent
 * @Package: com.cet.pqscheduleservice.model.event
 * @Description:
 * @Author: zhangyifu
 * @Date: 2025/6/9 9:31
 * @Version:1.0
 */
@Data
public class DeviceEvent {
    private String modelLabel = "deviceevent";

    // 监测节点 所属电站 事件类型 发生时间 持续时间(ms) 特征幅值(%) A相最大值 A相最小值 B相最大值 B相最小值 C相最大值 C相最小值 波形
    private Long id;

    private Long deviceid;

    private String monitoredlabel;

    private Long monitoredid;

    private Integer stationid; // 所属电站

    private String eventtypename; //  事件类型

    private Long eventtime; // 发生时间

    private Long duration; // 持续时间

    private Double magnitude; // 特征幅值(%)

    private Double maxa;

    private Double maxb;

    private Double maxc;

    private Double mina;

    private Double minb;

    private Double minc;

    private String description; // 事件描述

    private String phase; // 相别

    private Integer toleranceband; // 容忍区

    private Long updatetime;
}
