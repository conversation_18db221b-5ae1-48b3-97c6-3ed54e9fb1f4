package com.cet.pqscheduleservice.model.enums;
/**
 * <AUTHOR>
 * @Date 2021/12/22 9:11
 * @Description
 */
public enum  ToleranceBandEnum {

    NONE("无", 1),
    TOLERANCE("设备容忍区", 2),
    PROHIBITED("设备损坏区", 3),
    NODAMAGE("设备无损区", 4);

    private String name;
    private Integer value;

    private ToleranceBandEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }


    public Integer getValue() {
        return value;
    }


    public static Integer getToleranceBandValue(String name) {
        for (ToleranceBandEnum toleranceBandEnum : ToleranceBandEnum.values()) {
            if (name.trim().equals(toleranceBandEnum.getName())) {
                return toleranceBandEnum.getValue();
            }
        }
        throw new RuntimeException("容忍度无效");
    }
}
