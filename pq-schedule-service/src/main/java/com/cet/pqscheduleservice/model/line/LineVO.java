package com.cet.pqscheduleservice.model.line;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/5 15:09
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LineVO {
    /**
     * 监测点id
     */
    private Long id;

    @JsonProperty("monitortype")
    private Integer monitortype;

    @ApiModelProperty("物联网通信自定义配置")
    @JsonProperty("lotconfig")
    private String lotconfig;

}
