package com.cet.pqscheduleservice.model.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 分页
 * 
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Page {
	private Integer index;
	private Integer limit;

	@Override
	public String toString() {
		return "Page{" +
				"index=" + index +
				", limit=" + limit +
				'}';
	}

	public Page(Long index, Long limit) {
		this.index = new BigDecimal(index).intValue();
		this.limit = new BigDecimal(limit).intValue();
	}
}
