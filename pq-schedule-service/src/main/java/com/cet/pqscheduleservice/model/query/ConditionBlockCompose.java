package com.cet.pqscheduleservice.model.query;

import java.util.List;

/**
 * 条件组合
 * <AUTHOR>
 */
public class ConditionBlockCompose {
    /**
     * 表达式列表
     */
    private List<ConditionBlock> expressions;
    /**
     * 查询条件composemethod为false(默认)使用先或后与(a or b or …) and (c or d or …) and …格式,
     * 通过tagid(>0)指定分组, tagid<=0 无分组.composemethod为true则先与后或
     */
    private boolean composemethod;

    public ConditionBlockCompose(List<ConditionBlock> filters) {
        this.expressions = filters;
    }

    public ConditionBlockCompose() {

    }

    public List<ConditionBlock> getExpressions() {
        return expressions;
    }

    public void setExpressions(List<ConditionBlock> expressions) {
        this.expressions = expressions;
    }

    public boolean isComposemethod() {
        return composemethod;
    }

    public void setComposemethod(boolean composemethod) {
        this.composemethod = composemethod;
    }

    @Override
    public String toString() {
        return "ConditionBlockCompose{" +
                "expressions=" + expressions +
                '}';
    }
}
