package com.cet.pqscheduleservice.model.fault;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/23 15:21
 * @Description
 */
@Data
public class RmsResult {


    /**
     * 额定电流
     */
    private Double ratedCurrent;
    /**
     * 置信度
     */
    private  Double confidenceLevel;
    /**
     * 是否故障
     */
    private Boolean hasFault;
    /**
     * 正常波形结束下标
     */
    private Integer normalEndIndex;
    /**
     * 正常波形开始下标
     */
    private Integer normalStartIndex;
    /**
     * 异常波形结束下标
     */
    private Integer abnormalStartIndex;
    /**
     * 异常波形开始下标
     */
    private Integer abnormalEndIndex;
    /**
     * 三相电压值
     */
    private List<Float> aVoltage;

    private List<Float> bVoltage;

    private List<Float> cVoltage;

    /**
     * 三相电流值
     */
    private List<Float> aCurrent;

    private List<Float> bCurrent;

    private List<Float> cCurrent;


    /**
     * aka Ian
     */
    private Double aCurrentNormalRms;
    /**
     * aka Ibn
     */
    private Double bCurrentNormalRms;
    /**
     * aka Icn
     */
    private Double cCurrentNormalRms;
    /**
     * aka Ia(u)
     */
    private Double aCurrentAbnormalRms;
    /**
     * aka Ib(u)
     */
    private Double bCurrentAbnormalRms;
    /**
     * aka Ic(u)
     */
    private Double cCurrentAbnormalRms;
    /**
     * aka Da
     */
    private Double aCurrentRmsDifferenceAbs;
    /**
     * aka Db
     */
    private Double bCurrentRmsDifferenceAbs;
    /**
     * aka Dc
     */
    private Double cCurrentRmsDifferenceAbs;
    /**
     * aka D0
     */
    private Double zeroCurrentRmsDifferenceAbs;
}
