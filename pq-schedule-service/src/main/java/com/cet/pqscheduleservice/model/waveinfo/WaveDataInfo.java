package com.cet.pqscheduleservice.model.waveinfo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: cfg文件解析结果信息
 * @date 2021/6/17 9:12
 */
@Data
public class WaveDataInfo {
    /**
     * 厂站名
     */
    private String stationName;
    /**
     * 设备名
     */
    private String deviceName;
    /**
     * 版本
     */
    private String version;
    /**
     * 通道总数
     */
    private Integer totalChannelNum;
    /**
     * 模拟通道数
     */
    private Integer aChannelNum;
    /**
     * 开关量通道数
     */
    private Integer dChannelNum;
    /**
     * 通道详细信息
     */
    private List<ChannelDetailInfo> channelDetailInfoList;
    /**
     * 采样频率
     */
    private Float frequency;
    /**
     * 采样率个数
     */
    private Integer ratesNum;
    /**
     * 采样率数
     */
    private Integer cfgRatesNum;
    /**
     * 采样率信息
     */
    private List<SampInfo> sampInfoList;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 触发时间
     */
    private String triggerTime;
    /**
     * dat文件类型
     */
    private String datType;
    /**
     * 时间系数
     */
    private Float timeMult;
}
