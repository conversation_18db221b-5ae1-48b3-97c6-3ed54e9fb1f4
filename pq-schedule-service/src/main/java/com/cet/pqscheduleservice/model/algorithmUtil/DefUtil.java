package com.cet.pqscheduleservice.model.algorithmUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DefUtil {

    public Double[] def(Double[] V,int n,String waveName){
        int length = V.length;
        Integer[] l = new Integer[n+2];
        l[n+1] = length;

        Double[] X = V.clone();
        Double[] Lo_D ={-0.010597402,0.032883012,0.030841382,-0.187034812,-0.027983769,0.630880768,0.714846571,0.230377813};
        Double[] Hi_D ={-0.230377813,0.714846571,-0.630880768,-0.027983769,0.187034812,0.030841382,-0.032883012,-0.010597402};
        List<List<Double>> temp = new ArrayList<List<Double>>();
        List<Double[]> ad = new ArrayList<Double[]>();
        for(int i=0;i<n;i++){
            if(i==0){
                ad = this.dwt(X,<PERSON>_D,Hi_D);
                Double[] d=ad.get(1);
                l[n-i] = d.length;
                temp.add(Arrays.asList(d));
            }else{
                Double[] a = new Double[ad.get(1).length];
                a= ad.get(0).clone();
                ad.clear();
                ad = this.dwt(a,Lo_D,Hi_D);
                Double[] d=ad.get(1);
                l[n-i] = d.length;
                temp.add(Arrays.asList(d));
            }

        }
        l[0] =ad.get(0).length;
        List<Double> c = new ArrayList<Double>();
        c.addAll(Arrays.asList(ad.get(0)));
        for(int i=n-1;i>=0;i--){
            c.addAll(temp.get(i));
        }

        Double[] A6 = wrcoef("a",c,l,6);
        Double[] D1 = wrcoef("d",c,l,1);
        Double[] D2 = wrcoef("d",c,l,2);
        Double[] D3 = wrcoef("d",c,l,3);
        Double[] D4 = wrcoef("d",c,l,4);
        Double[] D5 = wrcoef("d",c,l,5);
        Double[] D6 = wrcoef("d",c,l,6);

        Double[] pc = this.getPC(A6,D1,D2,D3,D4,D5,D6);
        return pc;
    }

    private Double[] getPC(Double[] a6, Double[] d1, Double[] d2, Double[] d3, Double[] d4, Double[] d5, Double[] d6) {
        double sumA6 = this.getSum(a6);
        double sumD1 = this.getSum(d1);
        double sumD2 = this.getSum(d2);
        double sumD3 = this.getSum(d3);
        double sumD4 = this.getSum(d4);
        double sumD5 = this.getSum(d5);
        double sumD6 = this.getSum(d6);
        double sumc = sumD1+sumD2+sumD3+sumD4+sumD5+sumD6;
        double pc1 = 0d;
        double pc2 = 0d;
        if(sumc != 0.0){
            pc1 = sumD1/sumc;
            pc2 = sumD2/sumc;
        }
        Double[] pc = new Double[2];
        pc[0] = pc1;
        pc[1] = pc2;
        return pc;
    }

    private double getSum(Double[] a6) {
        int n = a6.length;
        double sum = 0;
        for(int i=0;i<n;i++){
            sum = sum + a6[i];
        }
        return sum;
    }

    private Double[] wrcoef(String o, List<Double> c, Integer[] l, int n) {
        Double[] Lo_R = {0.230377813308855,0.714846570552542,0.630880767929590,-0.0279837694169839,-0.187034811718881,0.0308413818359870,0.0328830116669829,-0.0105974017849973};
        Double[] Hi_R = {-0.0105974017849973,-0.0328830116669829,0.0308413818359870,0.187034811718881,-0.0279837694169839,-0.630880767929590,0.714846570552542,-0.230377813308855};
        int rmax = l.length;
        int nmax = rmax-2;
        int nmin =0;
        if(o.equals("a")){
            nmin = 0;
        }else{
            nmin = 1;
        }
        String dwtATTR = "sym";
        List<Double> x = new ArrayList<Double>();
        Double[] F1;
        if(o.equals("a")){
            x = this.appcoef(c,l);
            F1 = Lo_R;
        }else{
            x = this.detcoef(c,l,n);
            F1 = Hi_R;
        }
        int imin = rmax-n;
        x = upsconv1(x,F1,l[imin],dwtATTR);
        for(int k =1;k<n;k++){
            x = this.upsconv1(x,Lo_R,l[imin+k],dwtATTR);
        }
        Double[] a = new Double[x.size()];
        for(int i =0;i<x.size();i++){
            a[i] = x.get(i);
        }
        return a;
    }

    private List<Double> upsconv1(List<Double> x, Double[] f, Integer s, String dwtARG1) {
        int lx = 2*x.size();
        int lf = f.length;
        int perFLAG=1;
        if(dwtARG1.equals("per")){
            perFLAG=1;
        }else{
            perFLAG=0;
        }
        int dwtSHIFT=0;
        Double[] y = new Double[x.size()];
        for(int i=0;i<x.size();i++){
            y[i] = x.get(i);
        }
        if(s==0){
            if(perFLAG ==0){
                s = lx-lf+2;
            }else{
                s = lx;
            }
        }
        List<Double> result = new ArrayList<Double>();
        if(perFLAG ==0){
            y = wconv1(dyadup(y,0),f);
            y = wkeep1(y,s,"c",dwtSHIFT);
            for(int i=0;i<y.length;i++){
                result.add(y[i]);
            }
        }else{
            y = dyadup(y,0);
            y = wextend(y,lf/2,"none");
            y = wconv1(y,f);
            for(int i=0;i<s;i++){
                result.add(y[lf+i]);
            }
        }
        return result;
    }

    private Double[] wkeep1(Double[] x, Integer leng, String OPT, int dwtSHIFT) {
        Double[] y = x;
        int sx = x.length;
        int first = 0;
        int last = 0;
        if(OPT.equals("c")){
            double d =(double) (sx-leng)/2;
            int side = dwtSHIFT;
            if(side ==0){
                first = (int)Math.floor(d);
                last = (int)(sx-Math.ceil(d));
            }else{
                first = (int)Math.ceil(d);
                last = (int)(sx-Math.floor(d));
            }

        }else if(OPT.equals("l") || OPT.equals("u")){
            first = 0;
            last = leng-1;
        }else{
            first = sx-leng-1;
            last = sx;
        }
        Double[] temp = Arrays.copyOfRange(y,first,last);
        return temp;
    }

    private Double[] wconv1(Double[] dyadup, Double[] f) {
        Double[] y = this.convolve(dyadup,f,"full");
        return y;
    }

    private Double[] convolve(Double[] dyadup, Double[] f, String type) {
        int lf = dyadup.length;
        int yl = f.length;

        // 直接获取较小和较大的长度
        int minSize = Math.min(lf, yl);
        int maxSize = Math.max(lf, yl);
        int hs =0;
        // 根据类型计算结果大小
        if (type.equals("full")) {
            hs = maxSize + minSize - 1;
        } else {
            hs = maxSize - minSize + 1;
        }
        int szie = maxSize + minSize - 1;

        // 初始化结果数组和中间矩阵
        Double[] result = new Double[szie];
        Double[][] rs = new Double[minSize][szie];

        // 初始化结果数组为0.0
        Arrays.fill(result, 0.0);

        // 初始化中间矩阵为0.0（可以合并到上面的初始化）
        for (int i = 0; i < minSize; i++) {
            Arrays.fill(rs[i], 0.0);
        }

        // 计算卷积并直接存入结果数组，无需使用中间矩阵rs
        for (int i = 0; i < minSize; i++) {
            for (int j = 0; j < maxSize; j++) {
                double valueF = f[j % maxSize]; // 避免访问null，对f进行循环取值
                double valueDyadup = dyadup[i % minSize]; // 同理对dyadup也进行循环取值
                result[i + j] += valueF * valueDyadup;
            }
        }

        // 根据类型截取结果数组
        int start = (szie - hs) / 2;
        int end = szie - start;
        return Arrays.copyOfRange(result, start, end);
    }

    private Double[] dyadup(Double[] y, int type) {
        int len = y.length*2-1;
        Double[] yy = new Double[len];
        for(int i=0;i<y.length-1;i++){
            yy[i] = 0.0;
            if(type ==0){
                yy[2*i] = y[i];
            }else{
                yy[2*i+1] = y[i];
            }

        }
        return yy;
    }

    private List<Double> detcoef(List<Double> coefs, Integer[] longs, int levels) {
        int length = longs.length;
        Integer[] first = new Integer[length];
        int zs =0;
        for(int i=0;i<length;i++){
            zs = zs+longs[i];
            first[i] =zs+1;
        }
        Integer[]last = new Integer[length-2];
        Integer[]first1 = new Integer[length-2];
        Integer[]longs1 = new Integer[length-2];
        for(int i=0;i<=length-3;i++){
            first1[i] = first[length-3-i];
            longs1[i] = longs[length-2-i];
        }
        for(int i = 0;i<=length-3;i++){
            last[i] = first1[i]+longs1[i]-1;
        }

        int k = levels-1;
        int start = first1[k]-1;
        int end = last[k];
        List<Double>tmp = new ArrayList<Double>();
        for(int i=start;i<end;i++){
            tmp.add(coefs.get(i));
        }
        return tmp;
    }

    private List<Double> appcoef(List<Double> c, Integer[] l) {
        int n = l.length-2;
        int next0 = 3;
        List<Double> a = new ArrayList<Double>();
        for(int i=0;i<l[0];i++){
            a.add(c.get(i));
        }
        return a;
    }

    private List<Double[]> dwt(Double[] x, Double[] Lo_D, Double[] Hi_D) {
        int lf = Lo_D.length;
        int sx = x.length;
        int first =1;
        int sizeEXT = lf-1;
        int last = sx+lf-1;
        int hl = Hi_D.length;

        Double[] y = this.wextend(x,sizeEXT,"none");
        Double[] zL = this.convolve(Lo_D,y,"valid");
        Double[] zH = this.convolve(Hi_D,y,"valid");
        Double[] a = this.getArange(zL,first,last);
        Double[] d = this.getArange(zH,first,last);
        List<Double[]> result = new ArrayList<Double[]>();
        result.add(a);
        result.add(d);
        return result;
    }

    private Double[] getArange(Double[] zL, int first, int last) {
        //生成从first~last之间，步长为2的数组
        List<Double> a = new ArrayList<Double>();
        for(int i=first;i<last;i=i+2){
            a.add(zL[i]);
        }
        Double[] d = new Double[a.size()];
        for(int i=0;i<a.size();i++){
            d[i] = a.get(i);
        }
        return d;
    }


    private Double[] wextend(Double[] x, int lf, String location) {
        String loc ="";
        if(location.equals("none")){
            loc = "b";
        }else{
            loc=location;
        }
        int sx =x.length;
        Integer[] I = this.getSymIndices(sx,lf);
        Double[] XN =new Double[I.length];
        for(int i=0;i<I.length;i++){
            XN[i] = x[I[i]];
        }
        return XN;
    }

    private Integer[] getSymIndices(int sx, int lf) {
        List<Integer> a = new ArrayList<>();

        // 一次性添加所有对称索引
        for (int i = -lf + 1; i <= lf - 1; ++i) {
            int symIndex = sx + i;
            if (symIndex < 0) {
                symIndex = -symIndex;
            } else if (symIndex > sx) {
                symIndex = 2 * sx + 1 - symIndex;
            }
            a.add(symIndex);
        }

        // 转换为Integer数组并返回
        Integer[] I = new Integer[a.size()];
        for (int i = 0; i < a.size(); ++i) {
            I[i] = a.get(i);
        }
        return I;
    }
}
