package com.cet.pqscheduleservice.model.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 监测点状态
 *
 * <AUTHOR>
 */
public enum MonitorStatusEnum {

    run("运行", 1), repair("检修", 2), debug("调试", 3), stop("停运", 4), fault("故障", 5);

    private String name;
    private Integer value;

    private MonitorStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }


    public Integer getValue() {
        return value;
    }


    public static Integer getMonitorStatusValue(String name) {
        for (MonitorStatusEnum monitorStatusEnum : MonitorStatusEnum.values()) {
            if (name.trim().equals(monitorStatusEnum.getName())) {
                return monitorStatusEnum.getValue();
            }
        }
        throw new RuntimeException("监测点状态值无效");
    }

    public static Integer getMonitorStatusValueNoThrows(String name) {
        for (MonitorStatusEnum monitorStatusEnum : MonitorStatusEnum.values()) {
            if (name.trim().equals(monitorStatusEnum.getName())) {
                return monitorStatusEnum.getValue();
            }
        }
        return 0;
    }

    public static String getMonitorStatusNameNoThrows(Integer value) {
        for (MonitorStatusEnum monitorStatusEnum : MonitorStatusEnum.values()) {
            if (value.equals(monitorStatusEnum.getValue())) {
                return monitorStatusEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }

}
