package com.cet.pqscheduleservice.model.datalog;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019年10月29日
 * SOE事件模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventLogVo {

	private Long channelId;

	private Integer code1;

	private Integer code2;

	private String description;

	private Long deviceId;

	private String eveStr1;

	private String eveStr2;

	private Integer eventByte;

	private Integer eventClass;

	private Long eventTime;

	private Integer eventType;

	private Long id;

	private Integer msec;

	private Integer stationFlag;

	private Integer stationId;

	private Long waveTime;

	private Boolean hasWave;

	private Integer confirmeventstatus;

	private String remark;

	private Long confirmTime;

	private Long operator;

	private Long confirmId;

	private String pecName;

	private Long pecEventId;
}
