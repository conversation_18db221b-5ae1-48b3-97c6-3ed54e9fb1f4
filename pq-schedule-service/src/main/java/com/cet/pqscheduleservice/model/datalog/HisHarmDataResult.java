package com.cet.pqscheduleservice.model.datalog;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/8/8 15:30
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HisHarmDataResult {
    private String name;
    private String unit;
    private Integer logicalId;
    private Long dataId;
    private Double value;
    private Long logtime;
    private Double uplimit;
    private Double lowlimit;
    private Integer aggregationtype;
    private Integer wiredType;

    public HisHarmDataResult(Long dataId, Integer logicalId, Long logtime, Integer aggregationtype){
        this.dataId = dataId;
        this.logicalId = logicalId;
        this.logtime = logtime;
        this.aggregationtype = aggregationtype;
    }
}
