package com.cet.pqscheduleservice.model.waveinfo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @Date 2021/3/29 16:17
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PQVariationevent {
    private String monitoredlabel;
    private Long monitoredid;
    private Long eventtime;
    private Integer pqvariationeventtype;
    private Long duration;
    private Float magnitude;
    private Boolean v1trigger;
    private Boolean v2trigger;
    private Boolean v3trigger;
    private String pqvariationeventtype$text;
    private Float v1magnitude;
    private Float v2magnitude;
    private Float v3magnitude;
    private Long srcdeviceid;
    private String remark;

}
