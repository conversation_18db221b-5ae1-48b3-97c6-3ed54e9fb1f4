package com.cet.pqscheduleservice.model.waveinfo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/20 14:53
 * @description
 */
@Data
public class PqData {
    private MonitorData monitorData;
    private AlarmData alarmData;
    private List<OverData> overDatas;
    @Data
    public static class MonitorData {
        private Long monitorCount;
        private Long terminalCount;
        private Double monitorOnlineCount;
        private Double dataIntegrity;
    }
    @Data
    public static class AlarmData {
        private Long alarmMonitorCount;
        private Long alarmCount;
    }
    @Data
    public static class OverData {
        private Long lineId;
        private Long overTotalTime;
        private Long freqDeviationTime;
        private Long voltDeviationTime;
        private Long harmonicVoltTime;
        private Long threeImbalanceTime;
        private Long negativeCurrentTime;
    }
}