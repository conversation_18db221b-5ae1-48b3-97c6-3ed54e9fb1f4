package com.cet.pqscheduleservice.model.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 层次数据请求条件
 * 
 * <AUTHOR>
 */
@NoArgsConstructor
public class QueryCondition {
	/**
	 * 定义模型组，用于指定查询与根节点直接关联的模型实例，并以其定义的标签返回
	 */
	private List<TreeGroupModel> groups;
	/**
	 * 子层级模型及筛选条件
	 */
	private List<SingleModelConditionDTO> subLayerConditions;

	/**
	 * 顶层节点的查询条件
	 */
	private FlatQueryConditionDTO rootCondition;

	/**
	 * 根节点的模型Label
	 */
	private String rootLabel;
	/**
	 * 根节点id，0或空表示所有
	 */
	@JsonProperty("rootID")
	private Long rootId;
	/**
	 * 返回的形式，true-树:children，false-层次结构：_model
	 */
	private Boolean treeReturnEnable;

	/**
	 * 在查询子层级时是否可以使用间接关联关系. false-强制使用直接关联关系, true/null-可以使用间接关联关系.
	 */
	private Boolean allowInterRelation;

	public QueryCondition(String rootLabel) {
		this.rootLabel = rootLabel;
	}

	public QueryCondition(List<SingleModelConditionDTO> subLayerConditions, String rootLabel, Long rootId) {
		this.subLayerConditions = subLayerConditions;
		this.rootLabel = rootLabel;
		this.rootId = rootId;
	}

	public QueryCondition(Long rootId, String rootLabel, SingleModelConditionDTO... subLayerConditions) {
		List<SingleModelConditionDTO> subLayerConditionList = new ArrayList<>();
		for (SingleModelConditionDTO singleModelConditionDTO : subLayerConditions) {
			subLayerConditionList.add(singleModelConditionDTO);
		}
		this.subLayerConditions = subLayerConditionList;
		this.rootLabel = rootLabel;
		this.rootId = rootId;
	}

	public QueryCondition(String rootLabel, Long rootId) {
		this.rootLabel = rootLabel;
		this.rootId = rootId;
	}

	public QueryCondition(String rootLabel, FlatQueryConditionDTO blocks, SingleModelConditionDTO... conditionDTOs) {
		List<SingleModelConditionDTO> subLayerConditionsinList = new ArrayList<>();
		for (SingleModelConditionDTO singleModelConditionDTO : conditionDTOs) {
			subLayerConditionsinList.add(singleModelConditionDTO);
		}
		this.rootLabel = rootLabel;
		this.rootCondition = blocks;
		this.subLayerConditions = subLayerConditionsinList;
	}

	public QueryCondition(String rootLabel, FlatQueryConditionDTO blocks) {
		this.rootLabel = rootLabel;
		this.rootCondition = blocks;
	}

	/**
	 * 单模型查询，只查询根模型
	 * 
	 * @param rootLabel
	 * @param blocks
	 * @return
	 */
	public QueryCondition(String rootLabel, ConditionBlock... blocks) {
		this.rootLabel = rootLabel;
		FlatQueryConditionDTO rootCondition = new FlatQueryConditionDTO();
		ConditionBlockCompose filter = new ConditionBlockCompose();
		List<ConditionBlock> expressions = new ArrayList<>();
		for (ConditionBlock conditionBlock : blocks) {
			expressions.add(conditionBlock);
		}
		filter.setExpressions(expressions);
		rootCondition.setFilter(filter);
		this.rootCondition = rootCondition;
	}

	public List<TreeGroupModel> getGroups() {
		return groups;
	}

	public void setGroups(List<TreeGroupModel> groups) {
		this.groups = groups;
	}

	public List<SingleModelConditionDTO> getSubLayerConditions() {
		return subLayerConditions;
	}

	public void setSubLayerConditions(List<SingleModelConditionDTO> subLayerConditions) {
		this.subLayerConditions = subLayerConditions;
	}

	public FlatQueryConditionDTO getRootCondition() {
		return rootCondition;
	}

	public void setRootCondition(FlatQueryConditionDTO rootCondition) {
		this.rootCondition = rootCondition;
	}

	public String getRootLabel() {
		return rootLabel;
	}

	public void setRootLabel(String rootLabel) {
		this.rootLabel = rootLabel;
	}

	public Long getRootId() {
		return rootId;
	}

	public void setRootId(Long rootId) {
		this.rootId = rootId;
	}

	public Boolean getTreeReturnEnable() {
		return treeReturnEnable;
	}

	public void setTreeReturnEnable(Boolean treeReturnEnable) {
		this.treeReturnEnable = treeReturnEnable;
	}

	public Boolean getAllowInterRelation() {
		return allowInterRelation;
	}

	public void setAllowInterRelation(Boolean allowInterRelation) {
		this.allowInterRelation = allowInterRelation;
	}

	@Override
	public String toString() {
		return "QueryCondition{" +
				"groups=" + groups +
				", subLayerConditions=" + subLayerConditions +
				", rootCondition=" + rootCondition +
				", rootLabel='" + rootLabel + '\'' +
				", rootID=" + rootId +
				", treeReturnEnable=" + treeReturnEnable +
				'}';
	}

}
