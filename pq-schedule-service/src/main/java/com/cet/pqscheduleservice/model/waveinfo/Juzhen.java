package com.cet.pqscheduleservice.model.waveinfo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class Juzhen {
    /**
     * 持续时间
     */
    private double duration;
    /**
     * 持续时间--点位
     */
    private double duration_dw;
    /**
     *三相最大电压有效值
     */
    private double u_max;

    /**
     * 三相最小电压有效值
     */
    private double u_min;
    /**
     * A相最小电压有效值
     */
    private double ua_min;
    /**
     * B相最小电压有效值
     */
    private double ub_min;
    /**
     * C相最小电压有效值
     */
    private double uc_min;
    /**
     *A相最大电压有效值
     */
    private double ua_max;
    /**
     *B相最大电压有效值
     */
    private double ub_max;
    /**
     *C相最大电压有效值
     */
    private double uc_max;
    /**
     * 零序不平衡度
     */
    private double un_0v;
    /**
     * 负序不平衡度
     */
    private double un_nV;
    /**
     * 负序、正序不平衡度
     */
    private double un_np;
    /**
     * 2、4次谐波率
     */
    private double pu1;
    /**
     * 奇异值
     */
    private double svd;
    /**
     *  形状因子bi1
     */
    private double bi1;
    /**
     *  形状因子bi2
     */
    private double bi2;
    /**
     * 事件前2、4次谐波率
     */
    private double pu1_pre;
    /**
     * 事件前零序不平衡度
     */
    private double un_0v_pre;
    /**
     * 事件前负序不平衡度
     */
    private double un_nV_pre;
    /**
     * 事件前负序、正序不平衡度
     */
    private double un_np_pre;
    /**
     *A相小波变换识别雷击1
     */
    private double pa1;
    /**
     *A相小波变换识别雷击2
     */
    private double pa2;
    /**
     *B相小波变换识别雷击1
     */
    private double pb1;
    /**
     *B相小波变换识别雷击2
     */
    private double pb2;
    /**
     *C相小波变换识别雷击1
     */
    private double pc1;
    /**
     *C相小波变换识别雷击2
     */
    private double pc2;
    /**
     * 采样频率
     */
    private int sample;
    /**
     *故障原因类型 =1,2,3,4，5,6，7,8对应普通故障1-4(单相接地故障\两相接地短路\相间短路\三相故障)、雷击5、变压器激磁6、电机启动7和其他8
     */
    private String rdlx;
    /**
     * 故障类型 1:电压暂升 2:电压暂降 3:电压中断 0:暂无明显特征
     */
    private String gzlx;
    /**
     * 故障源定位 1：上游 2：下游
     */
    private String guydw;

    /**
     * 数据文件中第一个数据的时间
     */
    public Date startTime;

    /**
     * 波形故障触发时间
     */
    public Date faultStartTime;

    /**
     * 波形故障触起始点
     */
    public int ts;
    /**
     * 波形故障触结束点
     */
    public int te;

    /**
     * 是否成功
     */
    public int status ;

    public String faultType;






}
