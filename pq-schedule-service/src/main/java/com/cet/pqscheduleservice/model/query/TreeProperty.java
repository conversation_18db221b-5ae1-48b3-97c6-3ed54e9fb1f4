package com.cet.pqscheduleservice.model.query;

/**
 * <AUTHOR>
 * @date 2022/5/5 14:14
 * @description
 **/
public class TreeProperty {
    private Long provinceCompanyId;
    private String provinceCompanyName;
    private Long countyCompanyId;
    private String countyCompanyName;
    private Long cityCompanyId;
    private String cityCompanyName;
    private Long substationId;
    private String substationName;
    private Integer substationVoltClass;

    public Long getProvinceCompanyId() {
        return this.provinceCompanyId;
    }

    public String getProvinceCompanyName() {
        return this.provinceCompanyName;
    }

    public Long getCountyCompanyId() {
        return this.countyCompanyId;
    }

    public String getCountyCompanyName() {
        return this.countyCompanyName;
    }

    public Long getCityCompanyId() {
        return this.cityCompanyId;
    }

    public String getCityCompanyName() {
        return this.cityCompanyName;
    }

    public Long getSubstationId() {
        return this.substationId;
    }

    public String getSubstationName() {
        return this.substationName;
    }

    public Integer getSubstationVoltClass() {
        return this.substationVoltClass;
    }

    public void setProvinceCompanyId(final Long provinceCompanyId) {
        this.provinceCompanyId = provinceCompanyId;
    }

    public void setProvinceCompanyName(final String provinceCompanyName) {
        this.provinceCompanyName = provinceCompanyName;
    }

    public void setCountyCompanyId(final Long countyCompanyId) {
        this.countyCompanyId = countyCompanyId;
    }

    public void setCountyCompanyName(final String countyCompanyName) {
        this.countyCompanyName = countyCompanyName;
    }

    public void setCityCompanyId(final Long cityCompanyId) {
        this.cityCompanyId = cityCompanyId;
    }

    public void setCityCompanyName(final String cityCompanyName) {
        this.cityCompanyName = cityCompanyName;
    }

    public void setSubstationId(final Long substationId) {
        this.substationId = substationId;
    }

    public void setSubstationName(final String substationName) {
        this.substationName = substationName;
    }

    public void setSubstationVoltClass(final Integer substationVoltClass) {
        this.substationVoltClass = substationVoltClass;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof TreeProperty)) {
            return false;
        } else {
            TreeProperty other = (TreeProperty)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label119: {
                    Object this$provinceCompanyId = this.getProvinceCompanyId();
                    Object other$provinceCompanyId = other.getProvinceCompanyId();
                    if (this$provinceCompanyId == null) {
                        if (other$provinceCompanyId == null) {
                            break label119;
                        }
                    } else if (this$provinceCompanyId.equals(other$provinceCompanyId)) {
                        break label119;
                    }

                    return false;
                }

                Object this$provinceCompanyName = this.getProvinceCompanyName();
                Object other$provinceCompanyName = other.getProvinceCompanyName();
                if (this$provinceCompanyName == null) {
                    if (other$provinceCompanyName != null) {
                        return false;
                    }
                } else if (!this$provinceCompanyName.equals(other$provinceCompanyName)) {
                    return false;
                }

                label105: {
                    Object this$countyCompanyId = this.getCountyCompanyId();
                    Object other$countyCompanyId = other.getCountyCompanyId();
                    if (this$countyCompanyId == null) {
                        if (other$countyCompanyId == null) {
                            break label105;
                        }
                    } else if (this$countyCompanyId.equals(other$countyCompanyId)) {
                        break label105;
                    }

                    return false;
                }

                Object this$countyCompanyName = this.getCountyCompanyName();
                Object other$countyCompanyName = other.getCountyCompanyName();
                if (this$countyCompanyName == null) {
                    if (other$countyCompanyName != null) {
                        return false;
                    }
                } else if (!this$countyCompanyName.equals(other$countyCompanyName)) {
                    return false;
                }

                label91: {
                    Object this$cityCompanyId = this.getCityCompanyId();
                    Object other$cityCompanyId = other.getCityCompanyId();
                    if (this$cityCompanyId == null) {
                        if (other$cityCompanyId == null) {
                            break label91;
                        }
                    } else if (this$cityCompanyId.equals(other$cityCompanyId)) {
                        break label91;
                    }

                    return false;
                }

                Object this$cityCompanyName = this.getCityCompanyName();
                Object other$cityCompanyName = other.getCityCompanyName();
                if (this$cityCompanyName == null) {
                    if (other$cityCompanyName != null) {
                        return false;
                    }
                } else if (!this$cityCompanyName.equals(other$cityCompanyName)) {
                    return false;
                }

                label77: {
                    Object this$substationId = this.getSubstationId();
                    Object other$substationId = other.getSubstationId();
                    if (this$substationId == null) {
                        if (other$substationId == null) {
                            break label77;
                        }
                    } else if (this$substationId.equals(other$substationId)) {
                        break label77;
                    }

                    return false;
                }

                label70: {
                    Object this$substationName = this.getSubstationName();
                    Object other$substationName = other.getSubstationName();
                    if (this$substationName == null) {
                        if (other$substationName == null) {
                            break label70;
                        }
                    } else if (this$substationName.equals(other$substationName)) {
                        break label70;
                    }

                    return false;
                }

                Object this$substationVoltClass = this.getSubstationVoltClass();
                Object other$substationVoltClass = other.getSubstationVoltClass();
                if (this$substationVoltClass == null) {
                    if (other$substationVoltClass != null) {
                        return false;
                    }
                } else if (!this$substationVoltClass.equals(other$substationVoltClass)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof TreeProperty;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $provinceCompanyId = this.getProvinceCompanyId();
        result = result * 59 + ($provinceCompanyId == null ? 43 : $provinceCompanyId.hashCode());
        Object $provinceCompanyName = this.getProvinceCompanyName();
        result = result * 59 + ($provinceCompanyName == null ? 43 : $provinceCompanyName.hashCode());
        Object $countyCompanyId = this.getCountyCompanyId();
        result = result * 59 + ($countyCompanyId == null ? 43 : $countyCompanyId.hashCode());
        Object $countyCompanyName = this.getCountyCompanyName();
        result = result * 59 + ($countyCompanyName == null ? 43 : $countyCompanyName.hashCode());
        Object $cityCompanyId = this.getCityCompanyId();
        result = result * 59 + ($cityCompanyId == null ? 43 : $cityCompanyId.hashCode());
        Object $cityCompanyName = this.getCityCompanyName();
        result = result * 59 + ($cityCompanyName == null ? 43 : $cityCompanyName.hashCode());
        Object $substationId = this.getSubstationId();
        result = result * 59 + ($substationId == null ? 43 : $substationId.hashCode());
        Object $substationName = this.getSubstationName();
        result = result * 59 + ($substationName == null ? 43 : $substationName.hashCode());
        Object $substationVoltClass = this.getSubstationVoltClass();
        result = result * 59 + ($substationVoltClass == null ? 43 : $substationVoltClass.hashCode());
        return result;
    }

    public String toString() {
        return "TreeProperty(provinceCompanyId=" + this.getProvinceCompanyId() + ", provinceCompanyName=" + this.getProvinceCompanyName() + ", countyCompanyId=" + this.getCountyCompanyId() + ", countyCompanyName=" + this.getCountyCompanyName() + ", cityCompanyId=" + this.getCityCompanyId() + ", cityCompanyName=" + this.getCityCompanyName() + ", substationId=" + this.getSubstationId() + ", substationName=" + this.getSubstationName() + ", substationVoltClass=" + this.getSubstationVoltClass() + ")";
    }

    public TreeProperty(final Long provinceCompanyId, final String provinceCompanyName, final Long countyCompanyId, final String countyCompanyName, final Long cityCompanyId, final String cityCompanyName, final Long substationId, final String substationName, final Integer substationVoltClass) {
        this.provinceCompanyId = provinceCompanyId;
        this.provinceCompanyName = provinceCompanyName;
        this.countyCompanyId = countyCompanyId;
        this.countyCompanyName = countyCompanyName;
        this.cityCompanyId = cityCompanyId;
        this.cityCompanyName = cityCompanyName;
        this.substationId = substationId;
        this.substationName = substationName;
        this.substationVoltClass = substationVoltClass;
    }

    public TreeProperty() {
    }
}
