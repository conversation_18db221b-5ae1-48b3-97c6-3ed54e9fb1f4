package com.cet.pqscheduleservice.model.event;

import lombok.Data;


/**
 * <AUTHOR>
 * @date 2021/9/22 14:35
 * @description
 */
@Data
public class PqEventVariation {
    private String modelLabel = "pqvariationevent";

    private Long id;
    private Long eventid;
    private Long deviceid;
    private Long eventtime;
    private String description;
    private Long duration;
    private Double nominalvoltage;
    private Double magnitude;
    private Integer pqvariationeventtype;
    private Integer toleranceband;
    private Double v1magnitude;
    private Double v2magnitude;
    private Double v3magnitude;
    private Long v1duration;
    private Long v2duration;
    private Long v3duration;
    private Boolean v1trigger;
    private Boolean v2trigger;
    private Boolean v3trigger;
    private Integer transientfaultdirection;
    private Long eventendtime;
    private Integer confirmeventstatus;
    private Long monitoredid;
    private String monitoredlabel;
    private String operator;
    private String remark;
    private Boolean valid;
    private Long waveformlogtime;
    private Long updatetime;
    private Double lossenergy;
    private Boolean isminuteextreme;
    /**
     * 脱网状态0:未脱网;1:低压脱网;2:高压脱网;
     */
    private Integer escapestatus;
    /**
     * 原因分析类型
     */
    private Integer reason;
    /**
     * 故障类型
     */
    private Integer faultreason;
}
