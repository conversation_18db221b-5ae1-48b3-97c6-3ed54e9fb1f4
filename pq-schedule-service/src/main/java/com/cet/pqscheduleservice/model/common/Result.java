package com.cet.pqscheduleservice.model.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="Result",description="服务接口返回结果")
@SuppressWarnings({ "rawtypes", "unchecked" })
public class Result<T> implements Serializable {
	
	public static final int SUCCESS_CODE = 0;
	
	private static final long serialVersionUID = 1L;
	
    @ApiModelProperty(value="错误码",name="code",example="1")
    private Integer code;

    @ApiModelProperty(value="错误信息",name="msg",example="null")
    private String msg;

    @ApiModelProperty(value="数据",name="data",example="xxx")
    private T data;

    public Result() {
        code = 0;
        msg = "";
    }

    public Result(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

	public static Result success() {
		return new Result(0,"操作成功",null);
    }
    
    public static <T> Result<T> success(T t){
    	return new Result<T>(0, "success", t);
    }

    public static <T> Result<T> success(String msg){
        return new Result<T>(0, msg, null);
    }
    
    public static Result error(String msg) {
		return new Result(500,msg,null);
    }
    public static <T> Result<T> error(T t) {
		return new Result(500,"error",t);
    }
    public Integer getCode() {
        return this.code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
