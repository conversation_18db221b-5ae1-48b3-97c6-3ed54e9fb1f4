package com.cet.pqscheduleservice.model.fault;

/**
 * <AUTHOR>
 * @Date 2024/5/23 15:21
 * @Description
 */
public enum LineFaultEnum {
    AG("A相接地", 1, 21,FaultPhaseTypeEnum.A_PHASE.getType() ),
    BG("B相接地", 1, 22,FaultPhaseTypeEnum.B_PHASE.getType() ),
    CG("C相接地", 1, 23,FaultPhaseTypeEnum.C_PHASE.getType() ),
    ABG("AB相间接地", 3, 31,FaultPhaseTypeEnum.A_B_PHASE.getType() ),
    BCG("BC相间接地", 3, 32,FaultPhaseTypeEnum.B_C_PHASE.getType() ),
    CAG("CA相间接地", 3, 33,FaultPhaseTypeEnum.C_A_PHASE.getType() ),
    ABCG("ABC相间接地", 4, 34,FaultPhaseTypeEnum.A_B_C_PHASE.getType() ),
    AB("AB相间短路", 2, 35,FaultPhaseTypeEnum.A_B_PHASE.getType() ),
    BC("BC相间短路", 2, 36,FaultPhaseTypeEnum.B_C_PHASE.getType() ),
    CA("CA相间短路", 2, 37,FaultPhaseTypeEnum.C_A_PHASE.getType() ),
    ABC("ABC相间短路", 4, 38,FaultPhaseTypeEnum.A_B_C_PHASE.getType() ),
    ;

    private String name;

    private Integer type;

    private Integer faultType;

    private Integer faultPhaseType;

    LineFaultEnum(String name, Integer type, Integer faultType, Integer faultPhaseType) {
        this.name = name;
        this.type = type;
        this.faultType = faultType;
        this.faultPhaseType = faultPhaseType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getFaultType() {
        return faultType;
    }

    public void setFaultType(Integer faultType) {
        this.faultType = faultType;
    }

    public Integer getFaultPhaseType() {
        return faultPhaseType;
    }

    public void setFaultPhaseType(Integer faultPhaseType) {
        this.faultPhaseType = faultPhaseType;
    }

    /**
     * 单相、相间,根据故障小类获取相别信息
     */
    public static LineFaultEnum faultPhaseTypeGet(Integer faultType) {
        for (LineFaultEnum item : LineFaultEnum.values()) {
            if (item.getFaultType().equals(faultType)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据故障类型获取故障名称
     * @param faultType
     * @return
     */
    public static String getNameByFaultType(Integer faultType) {
        for (LineFaultEnum item : LineFaultEnum.values()) {
            if (item.getFaultType().equals(faultType)) {
                return item.getName();
            }
        }
        return null;
    }

}
