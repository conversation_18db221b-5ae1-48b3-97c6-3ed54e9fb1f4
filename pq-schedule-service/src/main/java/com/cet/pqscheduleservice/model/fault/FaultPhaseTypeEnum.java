package com.cet.pqscheduleservice.model.fault;


import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/23 15:21
 * @Description
 */
public enum FaultPhaseTypeEnum {
    A_PHASE("A相",1, Arrays.asList(1)),
    B_PHASE("B相",2, Arrays.asList(2)),
    C_PHASE("C相",3, Arrays.asList(3)),
    A_B_PHASE("AB相间",4, Arrays.asList(1,2)),
    B_C_PHASE("BC相间",5, Arrays.asList(2,3)),
    C_A_PHASE("CA相间",6, Arrays.asList(1,3)),
    A_B_C_PHASE("ABC相间",7, Arrays.asList(1,2,3)),
    A_AND_B_PHASE("A、B相",8, Arrays.asList(1,2)),
    B_AND_C_PHASE("B、C相",9, Arrays.asList(2,3)),
    C_AND_A_PHASE("C、A相",10, Arrays.asList(1,3)),
    A_AND_B_AND_C_PHASE("A、B、C相",11, Arrays.asList(1,2,3)),
    ;
    private String phase;
    private Integer type;
    private List<Integer> singleTypeList;

    FaultPhaseTypeEnum(String phase, Integer type, List<Integer> singleTypeList) {
        this.phase = phase;
        this.type = type;
        this.singleTypeList = singleTypeList;
    }

    public String getPhase() {
        return phase;
    }

    public void setPhase(String phase) {
        this.phase = phase;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<Integer> getSingleTypeList() {
        return singleTypeList;
    }

    public void setSingleTypeList(List<Integer> singleTypeList) {
        this.singleTypeList = singleTypeList;
    }

    /**
     * 根据type获取枚举
     *
     * @param
     * @return
     */
    public static FaultPhaseTypeEnum getEnumByType(Integer type) {
        for (FaultPhaseTypeEnum item : FaultPhaseTypeEnum.values()) {
            if (type.equals(item.getType())) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据type获取
     * @param type
     * @return
     */
    public static String getPhaseByType(Integer type) {
        for (FaultPhaseTypeEnum item : FaultPhaseTypeEnum.values()) {
            if (type.equals(item.getType())) {
                return item.getPhase();
            }
        }
        return null;
    }

    /**
     * 根据单相汇总多相
     *
     */
    public static Integer getTypeByList(List<Integer> singleTypeList) {
        if (CollectionUtils.isEmpty(singleTypeList)) {
            return null;
        }
        for (FaultPhaseTypeEnum item : FaultPhaseTypeEnum.values()) {
            if (item.getSingleTypeList().equals(singleTypeList)) {
                return item.getType();
            }
        }
        return null;
    }

}
