package com.cet.pqscheduleservice.model.query;

/**
 * 条件块
 *
 * <AUTHOR>
 */
public class ConditionBlock {
	/**
	 * 等于
	 */
	public static final String OPERATOR_EQ = "EQ";
	/**
	 * 小于
	 */
	public static final String OPERATOR_LT = "LT";
	/**
	 * 大于
	 */
	public static final String OPERATOR_GT = "GT";
	/**
	 * 小于等于
	 */
	public static final String OPERATOR_LE = "LE";
	/**
	 * 大于等于
	 */
	public static final String OPERATOR_GE = "GE";
	/**
	 * 不等于
	 */
	public static final String OPERATOR_NE = "NE";

	/**
	 * 介于期间
	 */
	public static final String OPERATOR_BETWEEN = "between";
	/**
	 * 模糊查询
	 */
	public static final String OPERATOR_LIKE = "LIKE";
	/**
	 * 在某个集合中
	 */
	public static final String OPERATOR_IN = "IN";

	/**
	 * 升序
	 */
	public static final String ASC = "asc";
	/**
	 * 降序
	 */
	public static final String DESC = "desc";

	private String prop;

	private String operator;

	/**
	 * 对参数进行分组，不同分组之间是并的关系，组内是或的关系
	 */
	private Integer tagid;
	/**
	 * 条件的限定范围，只支持两种数据类型，数值和字符串，其他不考虑
	 */
	private Object limit;

	public ConditionBlock() {
	}

	public ConditionBlock(String prop, String operator, Object limit) {
		this.prop = prop;
		this.operator = operator;
		this.limit = limit;
	}

	public ConditionBlock(String prop, String operator, Object limit, int tagid) {
		this.prop = prop;
		this.operator = operator;
		this.limit = limit;
		this.tagid = tagid;
	}

	public String getProp() {
		return prop;
	}

	public void setProp(String prop) {
		this.prop = prop;
	}

	public Integer getTagid() {
		return tagid;
	}

	public void setTagid(Integer tagid) {
		this.tagid = tagid;
	}
	
	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public Object getLimit() {
		return limit;
	}

	public void setLimit(Object limit) {
		this.limit = limit;
	}

	@Override
	public String toString() {
		return "ConditionBlock{" +
				"prop='" + prop + '\'' +
				", operator='" + operator + '\'' +
				", limit=" + limit +
				", tagid=" + tagid +
				'}';
	}
}
