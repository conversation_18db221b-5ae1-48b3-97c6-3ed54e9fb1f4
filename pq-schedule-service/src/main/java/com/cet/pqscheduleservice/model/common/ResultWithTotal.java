package com.cet.pqscheduleservice.model.common;

import lombok.NoArgsConstructor;

/**
 * 带有总数信息的结果
 * 
 * <AUTHOR>
 * @param <T> 类型
 */
@SuppressWarnings("serial")
@NoArgsConstructor
public class ResultWithTotal<T> extends Result<T> {
	private long total;

	public ResultWithTotal(Integer code, String msg, T data) {
		super(code, msg, data);
	}

	public ResultWithTotal(Integer code, String msg, T data, long total) {
		super(code, msg, data);
		this.total = total;
	}

	public long getTotal() {
		return total;
	}

	public void setTotal(long total) {
		this.total = total;
	}

	@Override
	public String toString() {
		return "ApiResultWithTotal{" + super.toString() +
				"total=" + total +
				'}';
	}

	public static <T> ResultWithTotal<T> success(T t, Integer total) {
		ResultWithTotal<T> resultWithTotal = new ResultWithTotal<T>(0, "success", t, total);
		return resultWithTotal;
	}

}
