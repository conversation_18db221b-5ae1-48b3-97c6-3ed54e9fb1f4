package com.cet.pqscheduleservice.model.query;

import lombok.Data;

/**
 * 排序
 * <AUTHOR>
 */
@Data
public class Order {
	
    private String propertyLabel;
    /**
     * orderType：asc、desc
     */
    private String orderType;
    /**
     *  优先级，数值越小优先级越高
     */
    private Integer priority;

    public Order(String propertyLabel, String orderType, Integer priority) {
        this.propertyLabel = propertyLabel;
        this.orderType = orderType;
        this.priority = priority;
    }

    @Override
    public String toString() {
        return "Order{" +
                "propertyLabel='" + propertyLabel + '\'' +
                ", orderType='" + orderType + '\'' +
                ", priority=" + priority +
                '}';
    }
}
