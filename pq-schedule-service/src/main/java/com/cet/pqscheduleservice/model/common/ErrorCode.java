package com.cet.pqscheduleservice.model.common;

/**
 * <AUTHOR>
 */
public class ErrorCode{
	
	public static final ErrorDesc SUCCESS = new ErrorDesc(0, "成功");
	
	public static final ErrorDesc INTERNAL_ERROR = new ErrorDesc(-1, "内部错误");
	
	public static final ErrorDesc SERVICE_API_ERROR = new ErrorDesc(-2, "调用服务接口错误");

	public static final ErrorDesc NAME_DUPLICATION = new ErrorDesc(-3, "名称重复");
	
	public static final ErrorDesc ACCOUNT_NOT_NULL = new ErrorDesc(-4, "存在账号不能删除");
	
	public static final ErrorDesc OTHER_WARN = new ErrorDesc(-5, "警告信息");
	
	public static final ErrorDesc ACCOUNT_DEPENDENCY = new ErrorDesc(-6, "账号被设备关联不能删除");

	public static final ErrorDesc MODEL_LABEL_ERROR = new ErrorDesc(-7, "模型名称错误");

	public static final int SUCCESS_CODE = 0;

	public static final String SUCCESS_DESC = "成功";

	public static final int INTERVAL_CODE = -1;

	public static final String INTERVAL_DESC = "内部错误";

	public static final int ILLEGAL_ARGUMENT_CODE = -2;

	public static final String ILLEGAL_ARGUMENT_DESC = "非法参数";

	public static final int FORMAT_ERROR_CODE = -3;

	public static final String FORMAT_ERROR_DESC = "格式错误";

	public static final int DUPLICATE_NAME_CODE = -4;

	public static final String DUPLICATE_NAME_DESC = "重名错误";

	public static final int MODEL_DUPLICATE_NODE_CODE = -2000001;

	public static final String MODEL_DUPLICATE_NODE_DESC = "名称重复";

	public static final int PEC_DUPLICATE_NAME_CODE = -100003;

	public static final String PEC_DUPLICATE_NAME_DESC = "监测点名称重复";
	
}
