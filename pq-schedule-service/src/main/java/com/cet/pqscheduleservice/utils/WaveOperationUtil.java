package com.cet.pqscheduleservice.utils;

import com.cet.pqscheduleservice.model.waveinfo.*;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 波形操作工具
 * @date 2021/6/15 10:32
 */
public class WaveOperationUtil {

    /**
     * 2001版波形数据标志
     */
    private static final Long FLAG_2001 = 0xFFFFFFFFL;

    private static final String ASCII = "ASCII";
    private static final String BINARY = "BINARY";
    /**
     * 周期信号
     */
    private static final float T = 0.02f;

    /**
     * 解析波形datafile字段
     *
     * @param dataFile dataFile字段
     */
    public static WaveFileObject loadWaveData(byte[] dataFile) {
        ByteArrayInputStream in = new ByteArrayInputStream(dataFile);
        DataInputStream din = new DataInputStream(in);
        byte[] hdrFile = null;
        byte[] cfgFile = null;
        byte[] datFile = null;
        try {
            Long flag1 = LibSerializeHelper.readUnsignedIntOrLong(din);
            if (flag1.equals(FLAG_2001)) {
                Long flag2 = LibSerializeHelper.readUnsignedIntOrLong(din);
                Long version = LibSerializeHelper.readUnsignedIntOrLong(din);
                Long nCompress = LibSerializeHelper.readUnsignedIntOrLong(din);
                Long hdrLen = LibSerializeHelper.readUnsignedIntOrLong(din);
                hdrFile = new byte[hdrLen.intValue()];
                din.readFully(hdrFile);
                Long cfgLen = LibSerializeHelper.readUnsignedIntOrLong(din);
                cfgFile = new byte[cfgLen.intValue()];
                din.readFully(cfgFile);
                Long datLen = LibSerializeHelper.readUnsignedIntOrLong(din);
                datFile = new byte[datLen.intValue()];
                din.readFully(datFile);
            } else {
                Integer cfgLen = flag1.intValue();
                cfgFile = new byte[cfgLen];
                din.readFully(cfgFile);
                Long datLen = LibSerializeHelper.readUnsignedIntOrLong(din);
                datFile = new byte[datLen.intValue()];
                din.readFully(datFile);
                hdrFile = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                in.close();
                din.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return new WaveFileObject(hdrFile, cfgFile, datFile);
    }

    /**
     * 解析波形数据
     *
     * @param cfgByte cfg byte 数组
     * @param datByte dat byte 数组
     * @return WaveDataInfo
     */
    public static WaveDataInfo parseWaveDataInfo(byte[] cfgByte, byte[] datByte) {
        List<String> cfgFileLinesArrayList = cgfByteToStringList(cfgByte);
        WaveDataInfo waveDataInfo = parseCfgStringList(cfgFileLinesArrayList);
        String datType = waveDataInfo.getDatType();
        if (ASCII.equalsIgnoreCase(datType)) {
            List<String> datFileLinesArrayList = datByteToStringByAscii(datByte);
            return parseDatStringListByAscii(datFileLinesArrayList, waveDataInfo);
        } else if (BINARY.equalsIgnoreCase(datType)) {
            List<String> datFileLinesArrayList = datByteToStringByBinary(datByte, waveDataInfo);
            return parseDatStringListByBinary(datFileLinesArrayList, waveDataInfo);
        } else {
            throw new RuntimeException(datType + "格式波形暂无解析规则");
        }
    }

    /**
     * 计算电流电压有效值序列
     *
     * @param dataList 电压或电流
     * @param samprate 采样率
     * @return 有效值
     */
    public static List<Float> caleEffectiveValue(List<Float> dataList, float samprate) {
        if (CollectionUtils.isEmpty(dataList)){
            return Collections.emptyList();
        }
        // 半周期样本点数
        int cNum = Math.round((float) (0.5 * T * samprate));
        List<Float> result = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            float value;
            if (i <= cNum-1) {
                value = (float) Math.sqrt(dataList.subList(0,cNum).stream().mapToDouble(d-> Math.pow(d,2)).sum()/cNum);
            } else {
                value = (float) Math.sqrt(dataList.subList(i-cNum,i).stream().mapToDouble(d-> Math.pow(d,2)).sum()/cNum);
            }
            result.add(value);
        }
        return result;
    }

    /**
     * 计算零序电压电流
     * @param aDataList A相电压或电流
     * @param bDataList B相电压或电流
     * @param cDataList C相电压或电流
     * @return 零序电压或电流
     */
    public static List<Float> caleZeroSequence(List<Float> aDataList, List<Float> bDataList, List<Float> cDataList) {
        if (CollectionUtils.isEmpty(aDataList)|| CollectionUtils.isEmpty(bDataList)|| CollectionUtils.isEmpty(cDataList)){
            return Collections.emptyList();
        }
        List<Float> result = new ArrayList<>();
        for (int i = 0; i < aDataList.size(); i++) {
            result.add((aDataList.get(i)+bDataList.get(i)+cDataList.get(i))/3);
        }
        return result;
    }

    /**
     * ASCII 波形字符串解析
     *
     * @param datFileLinesArrayList 字符串列表
     * @param waveDataInfo          波形基本信息
     * @return WaveDataInfo
     */
    private static WaveDataInfo parseDatStringListByAscii(List<String> datFileLinesArrayList, WaveDataInfo waveDataInfo) {
        int samplePerCycle;
        for (int rate = 0; rate < waveDataInfo.getRatesNum(); ++rate) {
            // 每周波的采样点数
            SampInfo sampInfo = waveDataInfo.getSampInfoList().get(rate);
            // 对于多采样率，数据是分段解析的，这样在解析下一个采样率的时候，需要更新数据的开始时间
            samplePerCycle = (int) Math.pow(2, Math.round(Math.log(sampInfo.getSamp() / waveDataInfo.getFrequency()) / Math.log(2)));
            for (int i = 0; i < waveDataInfo.getTotalChannelNum(); ++i) {
                ChannelDetailInfo channelInfoObj = waveDataInfo.getChannelDetailInfoList().get(i);
                // 计算通道的数据值
                if (waveDataInfo.getCfgRatesNum() != 0) {
                    // cfg文件中存在采样率
                    if (i < waveDataInfo.getAChannelNum()) {
                        calcSimulateChannelDataWithSample(channelInfoObj, sampInfo, waveDataInfo.getFrequency(), datFileLinesArrayList,
                                samplePerCycle);
                    } else {
                        calcSwitchChannelDataWithSample(waveDataInfo, channelInfoObj, sampInfo, datFileLinesArrayList, samplePerCycle);
                    }
                } else {
                    // cfg文件中不存在存在采样率
                    if (i < waveDataInfo.getAChannelNum()) {
                        calcSimulateChannelDataWithOutSample(channelInfoObj, sampInfo, datFileLinesArrayList, waveDataInfo.getTimeMult());
                    } else {
                        calcSwitchChannelDataWithOutSample(waveDataInfo, channelInfoObj, sampInfo, datFileLinesArrayList);
                    }
                }
            }
        }
        return waveDataInfo;
    }

    /**
     * ASCII data byte 解析为字符串
     *
     * @param datByte data byte 数组
     * @return List<String>
     */
    private static List<String> datByteToStringByAscii(byte[] datByte) {
        String datStr = new String(datByte);
        List<String> datFileLinesArrayList = new ArrayList<>();
        BufferedReader bufferedReader;
        try (StringReader stringReader = new StringReader(datStr)) {
            bufferedReader = new BufferedReader(stringReader);
            String valueString;
            while ((valueString = bufferedReader.readLine()) != null) {
                if (valueString.length() > 3) {
                    String dataFileStr = "," + valueString;
                    datFileLinesArrayList.add(dataFileStr);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return datFileLinesArrayList;
    }

    /**
     * 解析dat字符串数据
     *
     * @param datFileLinesArrayList dat字符串数据列表
     * @param waveDataInfo          波形基本信息
     * @return waveDataInfo
     */
    private static WaveDataInfo parseDatStringListByBinary(List<String> datFileLinesArrayList, WaveDataInfo waveDataInfo) {
        int samplePerCycle;
        for (int rate = 0; rate < waveDataInfo.getRatesNum(); ++rate) {
            // 每周波的采样点数
            SampInfo sampInfo = waveDataInfo.getSampInfoList().get(rate);
            // 对于多采样率，数据是分段解析的，这样在解析下一个采样率的时候，需要更新数据的开始时间
            Float frequency = waveDataInfo.getFrequency();
            samplePerCycle = (int) Math.pow(2, Math.round(Math.log(sampInfo.getSamp() / frequency) / Math.log(2)));
            for (int i = 0; i < waveDataInfo.getTotalChannelNum(); ++i) {
                ChannelDetailInfo channelInfoObj = waveDataInfo.getChannelDetailInfoList().get(i);
                // 计算通道的数据值
                if (waveDataInfo.getCfgRatesNum() != 0) {
                    // cfg文件中存在采样率
                    if (i < waveDataInfo.getAChannelNum()) {
                        //模拟量通道
                        calcSimulateChannelDataWithSample(channelInfoObj, sampInfo, frequency, datFileLinesArrayList, samplePerCycle);
                    } else {
                        //开关量通道
                        calcSwitchChannelDataWithSample(waveDataInfo, channelInfoObj, sampInfo, datFileLinesArrayList, samplePerCycle);
                    }
                } else {
                    // cfg文件中不存在采样率
                    if (i < waveDataInfo.getAChannelNum()) {
                        //模拟量通道
                        calcSimulateChannelDataWithOutSample(channelInfoObj, sampInfo, datFileLinesArrayList, waveDataInfo.getTimeMult());
                    } else {
                        //开关量通道
                        calcSwitchChannelDataWithOutSample(waveDataInfo, channelInfoObj, sampInfo, datFileLinesArrayList);
                    }
                }
            }
        }
        return waveDataInfo;
    }

    /**
     * 不存在采样率的开关量通道解析
     *
     * @param waveDataInfo          波形基本信息
     * @param channelInfoObj        通道信息
     * @param sampleInfo            采样率信息
     * @param datFileLinesArrayList data字符串
     */
    private static void calcSwitchChannelDataWithOutSample(WaveDataInfo waveDataInfo, ChannelDetailInfo channelInfoObj,
                                                           SampInfo sampleInfo, List<String> datFileLinesArrayList) {
        // 获取从通道字符串中解析出来的数据
        List<KeyValuePair> dataArray = new ArrayList<>();
        // 获取该通道是一侧值还是二侧值
        int index = sampleInfo.getBeginSamp();
        // 创建临时存储通道数据的数组
        while (index < sampleInfo.getEndSamp() && index < datFileLinesArrayList.size()) {
            // 解析*.dat文件中数据
            String[] dataInfoStrs = datFileLinesArrayList.get(index).split(",");
            // 获取数据产生的时间
            long time = Long.valueOf(dataInfoStrs[2]);
            // 如果switchValueIndex>=0表示是开关量
            int switchValueIndex = channelInfoObj.getChannelIndex() - waveDataInfo.getAChannelNum();
            int switchValue = getSwitchValue(waveDataInfo, channelInfoObj, dataInfoStrs, switchValueIndex);
            dataArray.add(new KeyValuePair(time, (float) switchValue));
            ++index;
        }
        // 计算通道数据
        for (KeyValuePair keyValue : dataArray) {
            keyValue.setXField(keyValue.getXField() * waveDataInfo.getTimeMult() / 1000);
            // 将通道数据保存起来
            channelInfoObj.getChannelData().add(keyValue);
        }
    }


    /**
     * 没有采样率的模拟通道解析
     *
     * @param channelInfoObj        通道信息
     * @param sampInfo              采样率信息
     * @param datFileLinesArrayList data字符串
     * @param timeMult              时间系数
     */
    private static void calcSimulateChannelDataWithOutSample(ChannelDetailInfo channelInfoObj, SampInfo sampInfo,
                                                             List<String> datFileLinesArrayList, Float timeMult) {
        // 获取从通道字符串中解析出来的数据
        List<KeyValuePair> dataArray = new ArrayList<>();
        // 获取该通道是一侧值还是二侧值
        boolean isSecondary = (channelInfoObj.getChannelPs() != null) && ("S".equalsIgnoreCase(channelInfoObj.getChannelPs()));
        int index = sampInfo.getBeginSamp();
        // 创建临时存储通道数据的数组
        while (index < sampInfo.getEndSamp() && index < datFileLinesArrayList.size()) {
            // 解析*.dat文件中数据
            String[] dataInfoStrs = datFileLinesArrayList.get(index).split(",");
            // 获取数据产生的时间
            float xField = Integer.valueOf(dataInfoStrs[2]);
            // 获取数值：（通道转换公式为ax+b， x取自.DAT文件， 单位就是uu）
            float yField = calcSimulateChannelValue(dataInfoStrs, channelInfoObj, isSecondary);
            // 保存获得值和时间
            KeyValuePair keyValue = new KeyValuePair(xField, yField);
            dataArray.add(keyValue);
            ++index;
        }
        // 计算通道数据
        for (KeyValuePair keyValue : dataArray) {
            keyValue.setXField(keyValue.getXField() * timeMult / 1000);
            // 将通道数据保存起来
            channelInfoObj.getChannelData().add(keyValue);
        }
    }

    /**
     * 有采样率的开关量通道解析
     *
     * @param waveDataInfo          波形基本信息
     * @param channelInfoObj        通道信息
     * @param sampInfo              采样率信息
     * @param datFileLinesArrayList data字符串
     * @param samplePerCycle        采样周期
     */
    private static void calcSwitchChannelDataWithSample(WaveDataInfo waveDataInfo, ChannelDetailInfo channelInfoObj, SampInfo sampInfo,
                                                        List<String> datFileLinesArrayList, int samplePerCycle) {
        int index = sampInfo.getBeginSamp();
        // 采样点之间的时间间隔=一周波时间（微秒）/每周波采样点数 1秒=1000毫秒=1000000微秒
        float dbinterval = 1000 * 1000 / (samplePerCycle * waveDataInfo.getFrequency());
        // 创建临时存储通道数据的数组
        while (index < sampInfo.getEndSamp() && index < datFileLinesArrayList.size()) {
            String[] dataInfoStrs = datFileLinesArrayList.get(index).split(",");
            // 如果switchValueIndex>=0表示是开关量
            int switchValueIndex = channelInfoObj.getChannelIndex() - waveDataInfo.getAChannelNum();
            float yField = getSwitchValue(waveDataInfo, channelInfoObj, dataInfoStrs, switchValueIndex);
            float xField = dbinterval * (index - sampInfo.getBeginSamp());
            channelInfoObj.getChannelData().add(new KeyValuePair(xField, yField));
            index++;
        }
    }

    /**
     * 计算开关量的值
     *
     * @param waveDataInfo     波形基本信息
     * @param channelInfoObj   通道信息
     * @param dataInfoStrs     数据信息
     * @param switchValueIndex 值下标
     * @return int
     */
    private static int getSwitchValue(WaveDataInfo waveDataInfo, ChannelDetailInfo channelInfoObj, String[] dataInfoStrs, int switchValueIndex) {
        int switchValue;
        if (ASCII.equalsIgnoreCase(waveDataInfo.getDatType())) {
            switchValue = Integer.valueOf(dataInfoStrs[channelInfoObj.getChannelIndex() + 3]);
        } else {
            // 2个字节表示16个开关量，判断
            int groupIndex = (int) Math.floor((double) switchValueIndex / 16);
            int switchValueIndexIn16 = switchValueIndex % 16;
            int groupValue = Integer.valueOf(dataInfoStrs[waveDataInfo.getAChannelNum() + groupIndex + 3]);
            switchValue = (groupValue >> switchValueIndexIn16) & 1;
        }
        return switchValue;
    }

    /**
     * 有采样率的模拟量通道解析
     *
     * @param channelInfoObj        通道信息
     * @param sampInfo              采样率信息
     * @param frequency             频率
     * @param datFileLinesArrayList data字符串
     * @param samplePerCycle        采样周期
     */
    private static void calcSimulateChannelDataWithSample(ChannelDetailInfo channelInfoObj, SampInfo sampInfo, Float frequency,
                                                          List<String> datFileLinesArrayList, int samplePerCycle) {
        int index = sampInfo.getBeginSamp();
        // 获取该通道是一侧值还是二侧值
        boolean isSecondary = (channelInfoObj.getChannelPs() != null) && ("S".equalsIgnoreCase(channelInfoObj.getChannelPs()));
        // 采样点之间的时间间隔=一周波时间（微秒）/每周波采样点数 1秒=1000毫秒=1000000微秒
        float dbinterval = 1000 * 1000 / (frequency * samplePerCycle);
        // 创建临时存储通道数据的数组
        while (index < sampInfo.getEndSamp() && index < datFileLinesArrayList.size()) {
            // 解析*.dat文件中数据
            String[] dataInfoStrs = datFileLinesArrayList.get(index).split(",");
            // 数值：（通道转换公式为ax+b， x取自.DAT文件， 单位就是uu）
            float yField = calcSimulateChannelValue(dataInfoStrs, channelInfoObj, isSecondary);
            // 时间
            float xField = dbinterval * (index - sampInfo.getBeginSamp());
            KeyValuePair keyValue = new KeyValuePair(xField, yField);
            channelInfoObj.getChannelData().add(keyValue);
            ++index;
        }
    }

    /**
     * 计算模拟量通道的值
     *
     * @param dataInfoStrs 数据
     * @param channelInfo  通道信息
     * @param isSecondary  是否二侧值
     * @return float
     */
    private static float calcSimulateChannelValue(String[] dataInfoStrs, ChannelDetailInfo channelInfo, boolean isSecondary) {
        // 数值：（通道转换公式为ax+b， x取自.DAT文件， 单位就是uu）
        float value = channelInfo.getChannelCof() * Float.valueOf(dataInfoStrs[channelInfo.getChannelIndex() + 3])
                + channelInfo.getChannelOffset();
        if (isSecondary) {
            value *= channelInfo.getChannelTransRatio();
        }
        return value;
    }

    /**
     * 解析dat数据解析为字符串
     *
     * @param datByte      data byte 数组
     * @param waveDataInfo 波形信息
     */
    private static List<String> datByteToStringByBinary(byte[] datByte, WaveDataInfo waveDataInfo) {
        List<String> datFileLinesArrayList = new ArrayList<>();
        //2字节表示16个开关量
        int dChannelGroupCount = (int) Math.ceil((double)waveDataInfo.getDChannelNum() / 16);
        int bytesAvailable = 8 + (waveDataInfo.getAChannelNum() + dChannelGroupCount) * 2;
        ByteArrayInputStream datFileInputStream = new ByteArrayInputStream(datByte);
        byte[] recBytes = new byte[bytesAvailable];
        List<byte[]> bytesArrayList = new ArrayList<>();
        try {
            int res = datFileInputStream.read(recBytes);
            while (res == bytesAvailable) {
                bytesArrayList.add(recBytes);
                recBytes = new byte[bytesAvailable];
                res = datFileInputStream.read(recBytes);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        for (byte[] byteArray : bytesArrayList) {
            ByteBuffer byteBuffer = ByteBuffer.allocate(byteArray.length);
            byteBuffer.put(byteArray);
            byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
            byteBuffer.position(0);
            int index = byteBuffer.getInt();
            int timeStamp = byteBuffer.getInt();
            String dataFileStr = String.format("%s, %d, %d", "", index, timeStamp);
            for (int j = 0; j < waveDataInfo.getAChannelNum() + dChannelGroupCount; ++j) {
                short value = byteBuffer.getShort();
                dataFileStr = String.format("%s, %d", dataFileStr, value);
            }
            datFileLinesArrayList.add(dataFileStr);
        }
        return datFileLinesArrayList;
    }

    /**
     * cfg byte数组解析成字符串
     *
     * @param cfgByte cfg byte数组
     * @return List<String>
     */
    private static List<String> cgfByteToStringList(byte[] cfgByte) {
        List<String> cfgFileLinesArrayList = new ArrayList<>();
        ByteArrayInputStream fileInputStream = new ByteArrayInputStream(cfgByte);
        InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream);
        BufferedReader cfgBufferedReader = new BufferedReader(inputStreamReader);
        try {
            String valueString;
            while ((valueString = cfgBufferedReader.readLine()) != null) {
                cfgFileLinesArrayList.add(valueString.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fileInputStream.close();
                inputStreamReader.close();
                cfgBufferedReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return cfgFileLinesArrayList;
    }

    /**
     * 解析cfg文件字符串
     *
     * @param cfgFileLinesArrayList cfg字符串
     * @return 波形基本信息
     */
    private static WaveDataInfo parseCfgStringList(List<String> cfgFileLinesArrayList) {
        WaveDataInfo waveDataInfo = new WaveDataInfo();
        int offset = 0;
        //解析厂站、设备、版本信息
        String headInfo = cfgFileLinesArrayList.get(offset++);
        String[] headInfoList = headInfo.split(",");
        waveDataInfo.setStationName(headInfoList[0].trim());
        waveDataInfo.setDeviceName(headInfoList[1].trim());
        waveDataInfo.setVersion(headInfoList[2].trim());
        //解析通道总数，模拟量通道个数，开关量通道个数
        String channelNumInfo = cfgFileLinesArrayList.get(offset++);
        String[] channelNumInfoList = channelNumInfo.split(",");
        String totalChannelNum = channelNumInfoList[0].trim();
        String aChannelNum = channelNumInfoList[1].trim();
        String dChannelNum = channelNumInfoList[2].trim();
        waveDataInfo.setTotalChannelNum(Integer.valueOf(totalChannelNum));
        waveDataInfo.setAChannelNum(Integer.valueOf(aChannelNum.substring(0, aChannelNum.length() - 1)));
        waveDataInfo.setDChannelNum(Integer.valueOf(dChannelNum.substring(0, aChannelNum.length() - 1)));
        //解析通道系数和通道偏移量
        List<ChannelDetailInfo> channelDetailInfoList = new ArrayList<>();
        for (int i = 0; i < waveDataInfo.getTotalChannelNum(); i++) {
            ChannelDetailInfo channelDetailInfo = new ChannelDetailInfo();
            String[] channelInfoList = cfgFileLinesArrayList.get(offset++).split(",");
            channelDetailInfo.setChannelIndex(i);
            int length = channelInfoList.length;
            String channelType = length == 13 ? "A" : (length == 5 ? "D" : null);
            channelDetailInfo.setChannelType(channelType);
            channelDetailInfo.setChannelName(channelInfoList[1].trim());
            channelDetailInfo.setPhaseType(channelInfoList[2].trim());
            channelDetailInfo.setCcbm(channelInfoList[3].trim());
            if (length == 13) {
                channelDetailInfo.setChannelUnit(channelInfoList[4].trim());
                channelDetailInfo.setChannelCof(Float.valueOf(channelInfoList[5].trim()));
                channelDetailInfo.setChannelOffset(Float.valueOf(channelInfoList[6].trim()));
                channelDetailInfo.setChannelTransRatio(Float.valueOf(channelInfoList[10].trim())
                        / Float.valueOf(channelInfoList[11].trim()));
                channelDetailInfo.setChannelPs(channelInfoList[12].trim());
            }
            channelDetailInfoList.add(channelDetailInfo);
        }
        waveDataInfo.setChannelDetailInfoList(channelDetailInfoList);
        //解析频率
        Float frequency = Float.valueOf(cfgFileLinesArrayList.get(offset++));
        waveDataInfo.setFrequency(frequency);
        //解析采样率个数
        Integer ratesNum = Integer.valueOf(cfgFileLinesArrayList.get(offset++));
        waveDataInfo.setCfgRatesNum(ratesNum);
        ratesNum = ratesNum < 1 ? 1 : ratesNum;
        waveDataInfo.setRatesNum(ratesNum);
        List<SampInfo> sampInfoList = new ArrayList<>();
        // 解析采样率
        for (int i = 0; i < ratesNum; i++) {
            SampInfo sampInfo = new SampInfo();
            String[] ratesInfoList = cfgFileLinesArrayList.get(offset++).split(",");
            sampInfo.setSamp(Float.valueOf(ratesInfoList[0].trim()));
            sampInfo.setEndSamp(Integer.valueOf(ratesInfoList[1].trim()));
            Integer beginSamp = 0;
            if (i >= 1) {
                beginSamp = sampInfoList.get(i - 1).getEndSamp();
            }
            sampInfo.setBeginSamp(beginSamp);
            sampInfoList.add(sampInfo);
        }
        waveDataInfo.setSampInfoList(sampInfoList);
        //解析开始时间
        String startTime = cfgFileLinesArrayList.get(offset++).trim();
        waveDataInfo.setStartTime(startTime);
        // 解析触发时间
        String triggerTime = cfgFileLinesArrayList.get(offset++).trim();
        waveDataInfo.setTriggerTime(triggerTime);
        String datType = cfgFileLinesArrayList.get(offset++).trim();
        waveDataInfo.setDatType(datType);
        //解析时间系数
        if (offset < cfgFileLinesArrayList.size()) {
            Float timeMult = Float.valueOf(cfgFileLinesArrayList.get(offset).trim());
            waveDataInfo.setTimeMult(timeMult);
        }
        return waveDataInfo;
    }

    /**
     * 提取通道数据
     *
     * @param waveDataInfo 波形解析结果
     * @param channelNames  通道名 兼容多个名
     * @return
     */
    public static List<Float> getChannelData(WaveDataInfo waveDataInfo, String... channelNames) {
        List<ChannelDetailInfo> channelDetailInfoList = waveDataInfo.getChannelDetailInfoList();
        List<KeyValuePair> channelData = channelDetailInfoList.stream()
                .filter(c -> {
                    for (String channelName : channelNames) {
                        if (channelName.equalsIgnoreCase(c.getChannelName())){
                            return Boolean.TRUE;
                        }
                    }
                    return Boolean.FALSE;
                })
                .findFirst().orElse(new ChannelDetailInfo()).getChannelData();
        if (channelData == null) {
            return Collections.emptyList();
        }
        return channelData.stream().map(KeyValuePair::getYField).collect(Collectors.toList());
    }

}
