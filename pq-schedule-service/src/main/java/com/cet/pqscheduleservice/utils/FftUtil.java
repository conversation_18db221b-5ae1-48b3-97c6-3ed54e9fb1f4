package com.cet.pqscheduleservice.utils;


import Jama.Matrix;
import Jama.SingularValueDecomposition;
import com.cet.pqscheduleservice.model.waveinfo.*;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/24 8:49
 * @description
 */
public class FftUtil {
    public static Specturm calcSpecturmData(List<KeyValuePair> channelData, Integer begin, Integer end) {
        List<Complex> fftData = calFFT(channelData, begin, end, true);
        if (CollectionUtils.isEmpty(fftData)) {
            return null;
        }
        Specturm specturm = new Specturm();
        Map<Integer, Double> specturmData = new HashMap<>();
        double dataRe = fftData.get(1).getRe();
        double dataIm = fftData.get(1).getIm();
        double H01RMS = Math.sqrt(Math.pow(dataRe, 2) + Math.pow(dataIm, 2));
        int fftLen = end - begin;
        // THD意思：Total Harmonic Distortion，总谐波失真
        double evenSumThdValue = 0;
        double oddSumThdValue = 0;
        double sumi = 0;
        double sumk = 0;
        for (int i = 2; i < fftLen / 2; i++) {
            double dataItemRe = fftData.get(i).getRe();
            double dataItemIm = fftData.get(i).getIm();
            double powSum = Math.pow(dataItemRe, 2) + Math.pow(dataItemIm, 2);

            if (i % 2 == 0) {
                evenSumThdValue += powSum;
            } else {
                oddSumThdValue = oddSumThdValue + powSum;
            }
            specturmData.put(i, Math.sqrt(powSum) / H01RMS);

            sumi += powSum;
            sumk += powSum * i * i;
        }
        //计算奇次畸变率
        specturm.setOddHarmonics(Math.sqrt(oddSumThdValue) / H01RMS);
        //计算偶次畸变率
        specturm.setEvenHarmonics(Math.sqrt(evenSumThdValue) / H01RMS);
        //计算总谐波畸变率
        specturm.setTotalHarmonics(Math.sqrt(oddSumThdValue + evenSumThdValue) / H01RMS);
        specturm.setH01RMS(H01RMS);
        double kFactor = 0d;
        if(sumi != 0.0){
            kFactor = sumk / sumi;
        }
        specturm.setKFactor(kFactor);
        specturm.setSpecturmData(specturmData);
        return specturm;
    }

    // 有采样数据的情况下计算通道的有效值数据。
    public static List<KeyValuePair> calChannelRMSData(WaveDataInfo waveDataInfo, int samplePerCycle, ChannelDetailInfo channelDetailInfo) {
        List<KeyValuePair> rMSData = new ArrayList<>();
        List<SampInfo> sampInfoList = waveDataInfo.getSampInfoList();
        SampInfo sampInfo = sampInfoList.get(0);
        int begin = sampInfo.getBeginSamp();
        int end = Math.min(sampInfo.getEndSamp(), channelDetailInfo.getChannelData().size());
        int halfSamplePerCycle = samplePerCycle / 2;
        // 限制最小半周波采点数
        halfSamplePerCycle = Math.max(halfSamplePerCycle, 1);
        List<KeyValuePair> channelData = channelDetailInfo.getChannelData();
        // 限制最小半周波采点数
        halfSamplePerCycle = Math.max(halfSamplePerCycle, 1);
        // 每半个周波计算一次
        for (int j = begin;j < end - halfSamplePerCycle;j += halfSamplePerCycle) {
            KeyValuePair rmsValue = calRMS(channelData, j, Math.min(j + halfSamplePerCycle, end));
            rMSData.add(rmsValue);
        }
        return rMSData;
    }

    public static int calSamplePerCycle(WaveDataInfo waveDataInfo) {
        Float frequency = waveDataInfo.getFrequency();
        List<SampInfo> sampInfoList = waveDataInfo.getSampInfoList();
        SampInfo sampInfo = sampInfoList.get(0);
        int begin = sampInfo.getBeginSamp();
        int end = Math.min(sampInfo.getEndSamp(), waveDataInfo.getChannelDetailInfoList().get(0).getChannelData().size());
        int samplePerCycle = (int) Math.pow(2, Math.round(Math.log(sampInfo.getSamp() / frequency) / Math.log(2)));
        return samplePerCycle;
    }

    public static long calTriggerInterval(WaveDataInfo waveDataInfo) {
        Long triggerTime = null;
        Long startTime = null;
        try {
            startTime = convertStartTimeToMillisecond(waveDataInfo.getStartTime());
            triggerTime = convertStartTimeToMillisecond(waveDataInfo.getTriggerTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        long interval = triggerTime - startTime;
        return interval;
    }

    private static Long convertStartTimeToMillisecond(String startTime) throws ParseException {
        // 01/05/2022,15:05:38.111
        String[] str = startTime.split("\\.");
        String DATE_PATTERN = "dd/MM/yyyy,HH:mm:ss";
        Long time = new SimpleDateFormat(DATE_PATTERN).parse(str[0]).getTime();
        String s = str[1].substring(0, 3);
        return time + Long.parseLong(s);
    }

    public static double calSingularValue(WaveDataInfo waveDataInfo, int samplePerCycle, ChannelDetailInfo channelDetailInfo) {
        //计算有效值
        List<KeyValuePair> valuePairs = FftUtil.calChannelRMSData(waveDataInfo,samplePerCycle,channelDetailInfo);
        //从计算的值里面找到最小值-》
        KeyValuePair keyValuePair = valuePairs.stream().min(Comparator.comparing(x -> x.getYField())).orElse(null);
        //取最小值到最后一个值范围内90%的数据
        List<KeyValuePair> filterKeyValuePairs = valuePairs.stream().filter(x -> keyValuePair.getXField() <= x.getXField()).collect(Collectors.toList());
        List<Float> yFieldList = filterKeyValuePairs.stream().map(KeyValuePair::getYField).collect(Collectors.toList());
        int len = (int)Math.floor(yFieldList.size() * 0.9);
        List<Float> matrixValueList = yFieldList.subList(0, len);
        //构造矩阵 转换为double类型二维数组
        double[] doubles = new double[matrixValueList.size()];
        Object[] objects = matrixValueList.toArray();
        for (int i = 0; i < objects.length; i++) {
            doubles[i]=Double.valueOf(String.valueOf(objects[i]));
        }
        double [][] matrix={doubles};
        Matrix b = new Matrix(matrix);
        //调用svd算法计算奇异值
        SingularValueDecomposition svd = new SingularValueDecomposition(b);
        Matrix s= svd.getS();
        double[][] array = s.getArray();
        return array[0][0];
    }



    // 计算有效值。
    // channelIndex 通道下标。
    // begin 可选，数据开始下标，默认为0。
    // end 可选， 数据结束下标， 默认为通道数据长度。
    private static KeyValuePair calRMS(List<KeyValuePair> channelData, int begin, int end) {
        // 截取需要的数据段
        List<KeyValuePair> data = channelData.subList(begin, end);
        int dataLen = data.size();
        Double sum = 0D;
        for (int i = 0; i < dataLen; i++) {
            KeyValuePair keyValuePair = data.get(i);
            sum += Math.pow(keyValuePair.getYField(), 2);
        }
        // 时间为最后一个点的时间
        float xField = data.get(dataLen - 1).getXField();
        Double value = Math.sqrt(sum / dataLen);

        return new KeyValuePair(xField,value.floatValue());
    }

    public static String getChannelType(String unit) {
        unit = unit.toUpperCase();

        if (unit.indexOf("NULL") != -1) {
            return "UNKNOWN";
        } else if (unit.indexOf("V") != -1 || unit.indexOf("U") != -1) {
            return "U";
        } else if (unit.indexOf("A") != -1 || unit.indexOf("I") != -1) {
            return "I";
        } else {
            return "UNKNOWN";
        }
    }

    // 计算正负序。
    // channelIndex 通道下标数组。
    // begin 可选，数据开始下标，默认为0。
    // end 可选， 数据结束下标， 默认为通道数据长度。
    public static SequenceValue calcSequenceValue(List<ChannelDetailInfo> channelIndexs, Integer begin, Integer end) {
        List<Complex> IDatas = new ArrayList<>();
        List<Complex> UDatas = new ArrayList<>();

        SequenceValue sequenceValue = new SequenceValue();
        for (int i = 0; i < channelIndexs.size(); i++) {
            ChannelDetailInfo channelDetailInfo = channelIndexs.get(i);
            List<KeyValuePair> channelData = channelDetailInfo.getChannelData();
            List<Complex> fftData = calFFT(channelData, begin, end, true);
            if(CollectionUtils.isEmpty(fftData)){
                return null;
            }
            String channelType = getChannelType(channelDetailInfo.getChannelUnit());
            if (channelType.equals("U")) {
                double dataRe = fftData.get(1).getRe();
                double dataIm = fftData.get(1).getIm();
                UDatas.add(new Complex(dataRe,dataIm));
            }
            if (channelType.equals("I")) {
                double dataRe = fftData.get(1).getRe();
                double dataIm = fftData.get(1).getIm();
                IDatas.add(new Complex(dataRe,dataIm));
            }
        }

        // 复数算子
        Double angle = (120 * Math.PI) / 180;
        Complex a = new Complex(Math.cos(angle), Math.sin(angle));
        Complex aa = complexMulti(a, a);

        Double uPositiveValue = 0D;
        Double uNegativeValue = 0D;
        Double uZeroValue = 0D;
        Double uNegativeUnbalance = 0D;
        Double uZeroUnbalance = 0D;

        Double iPositiveValue = 0D;
        Double iNegativeValue = 0D;
        Double iZeroValue = 0D;
        Double iNegativeUnbalance = 0D;
        Double iZeroUnbalance = 0D;

        if (UDatas.size() >= 3) {
            Complex a0 = UDatas.get(0);
            Complex a1 = complexMulti(a, UDatas.get(1));
            Complex a2 = complexMulti(aa, UDatas.get(2));

            // 计算正序电流
            Complex uPositiveSequence = new Complex(a0.getRe() + a1.getRe() + a2.getRe(), a0.getIm() + a1.getIm() + a2.getIm());
            uPositiveValue = Math.sqrt(
                    Math.pow(uPositiveSequence.getRe(), 2) + Math.pow(uPositiveSequence.getIm(), 2)
            );

            a1 = complexMulti(aa, UDatas.get(1));
            a2 = complexMulti(a, UDatas.get(2));

            // 计算负序电流
            Complex uNegativeSequence = new Complex(a0.getRe() + a1.getRe() + a2.getRe(), a0.getIm() + a1.getIm() + a2.getIm());
            uNegativeValue = Math.sqrt(
                    Math.pow(uNegativeSequence.getRe(), 2) + Math.pow(uNegativeSequence.getIm(), 2)
            );

            a1 = UDatas.get(1);
            a2 = UDatas.get(2);

            //计算零序电流
            Complex uZeroSequence = new Complex(a0.getRe() + a1.getRe() + a2.getRe(), a0.getIm() + a1.getIm() + a2.getIm());
            uZeroValue = Math.sqrt(
                    Math.pow(uZeroSequence.getRe(), 2) + Math.pow(uZeroSequence.getIm(), 2)
            );

            if (uPositiveValue > 0) {
                //计算电流零序不平衡度
                uZeroUnbalance = uZeroValue / uPositiveValue;
                //计算电流负序不平衡度
                uNegativeUnbalance = uNegativeValue / uPositiveValue;
            }
        }

        if (IDatas.size() >= 3) {
            Complex a0 = IDatas.get(0);
            Complex a1 = complexMulti(a, IDatas.get(1));
            Complex a2 = complexMulti(aa, IDatas.get(2));

            // 计算正序电流
            Complex iPositiveSequence = new Complex(a0.getRe() + a1.getRe() + a2.getRe(), a0.getIm() + a1.getIm() + a2.getIm());
            iPositiveValue = Math.sqrt(
                    Math.pow(iPositiveSequence.getRe(), 2) + Math.pow(iPositiveSequence.getIm(), 2)
            );

            a1 = complexMulti(aa, IDatas.get(1));
            a2 = complexMulti(a, IDatas.get(2));

            // 计算负序电流
            Complex iNegativeSequence = new Complex(a0.getRe() + a1.getRe() + a2.getRe(), a0.getIm() + a1.getIm() + a2.getIm());
            iNegativeValue = Math.sqrt(
                    Math.pow(iNegativeSequence.getRe(), 2) + Math.pow(iNegativeSequence.getIm(), 2)
            );

            a1 = IDatas.get(1);
            a2 = IDatas.get(2);

            //计算零序电流
            Complex iZeroSequence = new Complex(a0.getRe() + a1.getRe() + a2.getRe(), a0.getIm() + a1.getIm() + a2.getIm());
            iZeroValue = Math.sqrt(
                    Math.pow(iZeroSequence.getRe(), 2) + Math.pow(iZeroSequence.getIm(), 2)
            );

            if (iPositiveValue > 0) {
                //计算电流零序不平衡度
                iZeroUnbalance = iZeroValue / iPositiveValue;
                //计算电流负序不平衡度
                iNegativeUnbalance = iNegativeValue / iPositiveValue;
            }
        }

        sequenceValue.setUNegativeUnbalance(uNegativeUnbalance);
        sequenceValue.setUZeroUnbalance(uZeroUnbalance);
        sequenceValue.setINegativeUnbalance(iNegativeUnbalance);
        sequenceValue.setIZeroUnbalance(iZeroUnbalance);
        return sequenceValue;
    }

    // 模拟复数相乘
    private static Complex complexMulti(Complex a, Complex b) {
        return new Complex(a.getRe() * b.getRe() -a.getIm() * b.getIm(), a.getRe() * b.getIm() + a.getIm() * b.getRe());
    }

    // channelIndex 通道下标。
    // begin 可选，数据开始下标，默认为0。
    // end 可选， 数据结束下标， 默认为通道数据长度。
    // isInverse 是否是傅里叶逆变换。
    // 重要 end-begin 的值必须为2的倍数。
    private static List<Complex> calFFT(List<KeyValuePair> channelData, Integer begin, Integer end, Boolean isInverse) {
        List<Complex> fftData = new ArrayList<>();
        if (CollectionUtils.isEmpty(channelData)) {
            return fftData;
        }
        if (begin == null) {
            begin = 0;
        }
        if (end == null) {
            end = channelData.size();
        }
        // 截取需要的数据段
        List<KeyValuePair> data = channelData.subList(begin, end);
        int dataLen = data.size();
        // 长度小于2，或不是2的倍数时，则判定为条件不满足
        if (dataLen < 2 || dataLen % 2 != 0) {
            return fftData;
        }
        // 傅里叶变换中需要用到，具体原理没看明白，但算法就是将位进行逆序排列。
        Integer[] reverseTable = new Integer[dataLen];
        int limit = 1;
        int bit = dataLen >> 1;
        while (limit < dataLen) {
            for (int i = 0; i < limit; i++) {
                if (reverseTable[i] == null){
                    reverseTable[i] = 0;
                }
                reverseTable[i + limit] = reverseTable[i]  + bit;
            }

            limit = limit << 1;
            bit = bit >> 1;
        }
        // 获得最初的实部和虚部
        Double[] reals = new Double[dataLen];
        Double[] imaginary = new Double[dataLen];
        for (int i = 0; i < dataLen; i++) {
            int j = reverseTable[i];
            reals[j] = (double)data.get(i).getYField();
            imaginary[j] = 0.0;
        }
        // 真正的傅里叶计算，完全没搞懂，直接抄代码
        int blockEnd = 1;
        double PI2 = (isInverse ? -2 : 2) * Math.PI;
        for (int blockSize = 2; blockSize <= dataLen; blockSize <<= 1) {
            double detalAngle = PI2 / blockSize;
            double sm2 = Math.sin(-2 * detalAngle);
            double sm1 = Math.sin(-detalAngle);
            double cm2 = Math.cos(-2 * detalAngle);
            double cm1 = Math.cos(-detalAngle);
            double w = 2 * cm1;
            double ar0, ar1, ar2, ai0, ai1, ai2;
            for (int i = 0; i < dataLen; i += blockSize) {
                ar2 = cm2;
                ar1 = cm1;
                ai2 = sm2;
                ai1 = sm1;
                for (int j = i, n = 0; n < blockEnd; j++, n++) {
                    ar0 = w * ar1 - ar2;
                    ar2 = ar1;
                    ar1 = ar0;
                    ai0 = w * ai1 - ai2;
                    ai2 = ai1;
                    ai1 = ai0;
                    int k = j + blockEnd;
                    double tr = ar0 * reals[k] - ai0 * imaginary[k];
                    double ti = ar0 * imaginary[k] + ai0 * reals[k];
                    reals[k] = reals[j] - tr;
                    imaginary[k] = imaginary[j] - ti;
                    reals[j] += tr;
                    imaginary[j] += ti;
                }
            }

            blockEnd = blockSize;
        }
        if (isInverse) {
            for (int i = 0; i < dataLen; i++) {
                reals[i] /= dataLen;
                imaginary[i] /= dataLen;
                if (Math.abs(reals[i]) < 0.000001) {
                    reals[i] = 0.0;
                }
                if (Math.abs(imaginary[i]) < 0.000001) {
                    imaginary[i] = 0.0;
                }
            }
        }
        for (int i= 0; i< reals.length; i++ ) {
            Complex complex = new Complex(reals[i], imaginary[i]);
            fftData.add(complex);
        }
        return fftData;
    }


}
