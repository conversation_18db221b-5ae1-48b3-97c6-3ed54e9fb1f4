package com.cet.pqscheduleservice.utils;


import java.io.DataInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Random;

/**
 * <AUTHOR>
 * @ClassName ：LibSerializeHelper
 * @date ：Created in 2021/3/29 15:59
 * @description：
 */
public class LibSerializeHelper {
    private static final int SIZE_1K = 1024;
    private static final int SIZE_1B = 8;
    private static final int SIZE_HALF_BYTE = 4;
    private static final int VALUE_0XFF = 255;
    private static final int VALUE_0XFFFE = 65534;
    private static final int VALUE_0XFFFF = 65535;
    public static final int INT_LEN = 4;
    private static final String TAG = " NetSerialParse ";
    private static byte[] mShortAr = new byte[2];
    private static byte[] mIntAr = new byte[4];
    private static byte[] mLongAr = new byte[8];

    public LibSerializeHelper() {
    }

    public static byte[] toLH(short n) {
        byte[] b = new byte[]{(byte)(n & 255), (byte)(n >> 8 & 255)};
        return b;
    }

    public static byte[] toHH(short n) {
        byte[] b = new byte[]{(byte)(n >> 8 & 255), (byte)(n & 255)};
        return b;
    }

    public static byte[] toLH(int n) {
        byte[] b = new byte[]{(byte)(n & 255), (byte)(n >> 8 & 255), (byte)(n >> 16 & 255), (byte)(n >> 24 & 255)};
        return b;
    }

    public static byte[] toLH(long n) {
        byte[] b = new byte[]{(byte)((int)(n & 255L)), (byte)((int)(n >> 8 & 255L)), (byte)((int)(n >> 16 & 255L)), (byte)((int)(n >> 24 & 255L)), (byte)((int)(n >> 32 & 255L)), (byte)((int)(n >> 40 & 255L)), (byte)((int)(n >> 48 & 255L)), (byte)((int)(n >> 56 & 255L))};
        return b;
    }

    public static byte[] toHH(int n) {
        byte[] b = new byte[]{(byte)(n >> 24 & 255), (byte)(n >> 16 & 255), (byte)(n >> 8 & 255), (byte)(n & 255)};
        return b;
    }

    public static byte[] toHH(long n) {
        byte[] b = new byte[]{(byte)((int)(n >> 56 & 255L)), (byte)((int)(n >> 48 & 255L)), (byte)((int)(n >> 40 & 255L)), (byte)((int)(n >> 32 & 255L)), (byte)((int)(n >> 24 & 255L)), (byte)((int)(n >> 16 & 255L)), (byte)((int)(n >> 8 & 255L)), (byte)((int)(n & 255L))};
        return b;
    }

    public static int lBytesToInt(byte[] b) {
        int s = 0;

        for(int i = 0; i < 3; ++i) {
            if (b[3 - i] >= 0) {
                s += b[3 - i];
            } else {
                s = s + 256 + b[3 - i];
            }

            s *= 256;
        }

        if (b[0] >= 0) {
            s += b[0];
        } else {
            s = s + 256 + b[0];
        }

        return s;
    }

    public static byte[] longReverseOrder(long l) {
        byte[] bytes = new byte[8];
        ByteBuffer byteBuffer = ByteBuffer.allocate(8);
        byteBuffer.putLong(l);
        bytes = bytesReverseOrder(byteBuffer.array());
        return bytes;
    }

    public static byte[] bytesReverseOrder(byte[] b) {
        int length = b.length;
        byte[] result = new byte[length];

        for(int i = 0; i < length; ++i) {
            result[length - i - 1] = b[i];
        }

        return result;
    }

    public static short hBytesToShort(byte[] b) {
        int s = 0;
        if (b[0] >= 0) {
            s = s + b[0];
        } else {
            s = s + 256 + b[0];
        }

        s *= 256;
        if (b[1] >= 0) {
            s += b[1];
        } else {
            s = s + 256 + b[1];
        }

        short result = (short)s;
        return result;
    }

    public static short lBytesToShort(byte[] b) {
        int s = 0;
        if (b[1] >= 0) {
            s = s + b[1];
        } else {
            s = s + 256 + b[1];
        }

        s *= 256;
        if (b[0] >= 0) {
            s += b[0];
        } else {
            s = s + 256 + b[0];
        }

        short result = (short)s;
        return result;
    }

    public static synchronized byte readByte(DataInputStream din) throws IOException {
        return din.readByte();
    }

    public static synchronized byte[] readByteArray(int length, DataInputStream din) throws IOException {
        byte[] result = new byte[length];
        din.read(result);
        return result;
    }

    public static synchronized char[] readCharArray(int length, DataInputStream din) throws IOException {
        if (length > 0 && length < 1048576) {
            char[] result = new char[length];

            for(int i = 0; i < length; ++i) {
                result[i] = din.readChar();
            }

            return result;
        } else {
            return new char[0];
        }
    }

    public static synchronized int readInt(InputStream din) throws IOException {
        clearIntAr();
        din.read(mIntAr);
        mIntAr = arrayInversion(mIntAr);
        return reverseInt(mIntAr);
    }

    public static synchronized short readShort(DataInputStream din) throws IOException {
        clearShortAr();
        din.read(mShortAr);
        mShortAr = arrayInversion(mShortAr);
        return reverseShort(mShortAr);
    }

    public static synchronized long readLong(DataInputStream din) throws IOException {
        clearLongAr();
        din.read(mLongAr);
        mLongAr = arrayInversion(mLongAr);
        return reverseLong(mLongAr);
    }

    public static synchronized long readLongNoConvert(DataInputStream din) throws IOException {
        clearLongAr();
        din.read(mLongAr);
        return reverseLong(mLongAr);
    }

    public static synchronized int readUnsigdedShort(DataInputStream din) throws IOException {
        clearShortAr();
        clearIntAr();
        din.read(mShortAr);
        mIntAr[0] = 0;
        mIntAr[1] = 0;
        mIntAr[2] = mShortAr[1];
        mIntAr[3] = mShortAr[0];
        return reverseInt(mIntAr);
    }

    public static synchronized int readUnsigdedShort(DataInputStream din, ByteOrder order) throws IOException {
        if (ByteOrder.BIG_ENDIAN == order) {
            return readUnsigdedShort(din);
        } else {
            clearShortAr();
            clearIntAr();
            din.read(mShortAr);
            mIntAr[0] = 0;
            mIntAr[1] = 0;
            mIntAr[2] = mShortAr[0];
            mIntAr[3] = mShortAr[1];
            return reverseInt(mIntAr);
        }
    }

    public static synchronized long readUnsignedIntOrLong(DataInputStream din) throws IOException {
        clearIntAr();
        clearLongAr();
        din.read(mIntAr);
        int j = 3;

        for(int i = 4; i < 8; ++i) {
            mLongAr[i] = mIntAr[j--];
        }

        return reverseLong(mLongAr);
    }

    public static synchronized short readUnginedByte(DataInputStream din) throws IOException {
        clearShortAr();
        byte[] result = new byte[1];
        din.read(result);
        mShortAr[0] = 0;
        mShortAr[1] = result[0];
        return reverseShort(mShortAr);
    }

    public static synchronized String readString(DataInputStream din) throws IOException {
        int length = readStringLengFromMFC(din);
        if (length >= 0 && length < 1048576) {
            byte[] buf = new byte[length];
            din.read(buf);
            String result = (new String(buf, "GB2312")).trim();
            return result;
        } else {
            throw new IOException("");
        }
    }

    public static synchronized String readStringUTF8(DataInputStream din) throws IOException {
        int length = readInt(din);
        if (length >= 0 && length < 1048576) {
            byte[] result = new byte[length];
            din.read(result);
            return (new String(result, "UTF-8")).trim();
        } else {
            throw new IOException("读取StringUTF8方法的长度读取异常：" + length);
        }
    }

    public static synchronized String readStringByUTF8(DataInputStream din) throws IOException {
        int length = readStringLengFromMFC(din);
        if (length >= 0 && length < 1048576) {
            byte[] result = new byte[length];
            din.read(result);
            return new String(result, "UTF-8");
        } else {
            throw new IOException("");
        }
    }

    public static synchronized String readString(DataInputStream din, int length) throws IOException {
        if (length >= 0 && length < 1048576) {
            byte[] result = new byte[length];
            din.read(result);
            return new String(result, "UTF-8");
        } else {
            throw new IOException("");
        }
    }

    public static synchronized String readString(DataInputStream din, int length, String encode) throws IOException {
        if (length >= 0 && length < 1048576) {
            byte[] result = new byte[length];
            din.read(result);
            return new String(result, encode);
        } else {
            throw new IOException("");
        }
    }

    public static synchronized String readStringWithCharsetName(DataInputStream din, int length, String charsetName) throws IOException {
        if (length >= 0 && length < 1048576) {
            byte[] result = new byte[length];
            din.read(result);
            return new String(result, charsetName);
        } else {
            throw new IOException("");
        }
    }

    public static synchronized float readFloat(DataInputStream din) throws IOException {
        clearIntAr();
        din.read(mIntAr);
        mIntAr = arrayInversion(mIntAr);
        ByteBuffer bf = ByteBuffer.wrap(mIntAr);
        return bf.getFloat();
    }

    public static synchronized double readDouble(DataInputStream din) throws IOException {
        clearLongAr();
        din.read(mLongAr);
        mLongAr = arrayInversion(mLongAr);
        ByteBuffer bf = ByteBuffer.wrap(mLongAr);
        return bf.getDouble();
    }

    public static synchronized double readDoubleFromDoNet(DataInputStream din) throws IOException {
        byte[] b = new byte[8];
        din.read(b);
        long l = (long)b[0];
        l &= 255L;
        l |= (long)b[1] << 8;
        l &= 65535L;
        l |= (long)b[2] << 16;
        l &= 16777215L;
        l |= (long)b[3] << 24;
        l &= 4294967295L;
        l |= (long)b[4] << 32;
        l &= 1099511627775L;
        l |= (long)b[5] << 40;
        l &= 281474976710655L;
        l |= (long)b[6] << 48;
        l &= 72057594037927935L;
        l |= (long)b[7] << 56;
        return Double.longBitsToDouble(l);
    }

    public static synchronized int readStringLengFromMFC(DataInputStream din) throws IOException {
        short bLength = readUnginedByte(din);
        if (bLength < 255) {
            return bLength;
        } else {
            int wLength = readUnsigdedShort(din);
            if (wLength == 65534) {
                bLength = (short)readByte(din);
                if (bLength < 255) {
                    return bLength;
                }

                wLength = readUnsigdedShort(din);
            }

            if (wLength < 65535) {
                return wLength;
            } else {
                throw new IllegalArgumentException("The String'length is error!!!!");
            }
        }
    }

    public static synchronized byte[] arrayInversion(byte[] buf) {
        int length = buf.length;
        byte[] result = new byte[length];

        for(int i = 0; i < length; ++i) {
            result[i] = buf[length - 1 - i];
        }

        return result;
    }

    private static synchronized void clearIntAr() {
        mIntAr[0] = 0;
        mIntAr[1] = 0;
        mIntAr[2] = 0;
        mIntAr[3] = 0;
    }

    private static synchronized void clearShortAr() {
        mShortAr[0] = 0;
        mShortAr[1] = 0;
    }

    private static synchronized void clearLongAr() {
        for(int i = 0; i < 8; ++i) {
            mLongAr[i] = 0;
        }

    }

    public static int reverseInt(byte[] b) {
        return reverseInt(b, ByteOrder.BIG_ENDIAN);
    }

    public static int reverseInt(byte[] b, ByteOrder order) {
        legal(b, 4);
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        int value = 0;
        int i;
        if (ByteOrder.BIG_ENDIAN.equals(mOrder)) {
            for(i = 0; i < 4; ++i) {
                value |= (b[i] & 255) << (3 - i) * 8;
            }
        } else {
            for(i = 0; i < 4; ++i) {
                value |= (b[i] & 255) << i * 8;
            }
        }

        return value;
    }

    public static short reverseShort(byte[] b) {
        return reverseShort(b, ByteOrder.BIG_ENDIAN);
    }

    public static short reverseShort(byte[] b, ByteOrder order) {
        legal(b, 2);
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        short value = 0;
        if (ByteOrder.BIG_ENDIAN.equals(mOrder)) {
            value = (short)(value | (b[0] & 255) << 8);
            value = (short)(value | b[1] & 255);
        } else {
            value = (short)(value | b[0] & 255);
            value = (short)(value | (b[1] & 255) << 8);
        }

        return value;
    }

    public static long reverseLong(byte[] b) {
        return reverseLong(b, ByteOrder.BIG_ENDIAN);
    }

    public static long reverseLong(byte[] b, ByteOrder order) {
        legal(b, 8);
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        long value = 0L;
        int i;
        if (ByteOrder.BIG_ENDIAN.equals(mOrder)) {
            for(i = 0; i < 8; ++i) {
                value |= (long)(b[i] & 255) << (7 - i) * 8;
            }
        } else {
            for(i = 0; i < 8; ++i) {
                value |= (long)(b[i] & 255) << i * 8;
            }
        }

        return value;
    }

    public static byte[] convert(int number) {
        return convert(number, ByteOrder.BIG_ENDIAN);
    }

    public static byte[] convert(int number, ByteOrder order) {
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        return ByteOrder.BIG_ENDIAN.equals(mOrder) ? new byte[]{(byte)(number >> 24), (byte)(number >> 16 & 255), (byte)(number >> 8 & 255), (byte)(number & 255)} : new byte[]{(byte)(number & 255), (byte)(number >> 8 & 255), (byte)(number >> 16 & 255), (byte)(number >> 24)};
    }

    public static byte[] convert(short number) {
        return convert(number, ByteOrder.BIG_ENDIAN);
    }

    public static byte[] convert(short number, ByteOrder order) {
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        return ByteOrder.BIG_ENDIAN.equals(mOrder) ? new byte[]{(byte)(number >> 8), (byte)(number & 255)} : new byte[]{(byte)(number & 255), (byte)(number >> 8)};
    }

    public static byte[] convert(long number, ByteOrder order) {
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        return ByteOrder.BIG_ENDIAN.equals(mOrder) ? new byte[]{(byte)((int)(number >> 56)), (byte)((int)(number >> 48 & 255L)), (byte)((int)(number >> 40 & 255L)), (byte)((int)(number >> 32 & 255L)), (byte)((int)(number >> 24 & 255L)), (byte)((int)(number >> 16 & 255L)), (byte)((int)(number >> 8 & 255L)), (byte)((int)(number & 255L))} : new byte[]{(byte)((int)(number & 255L)), (byte)((int)(number >> 8 & 255L)), (byte)((int)(number >> 16 & 255L)), (byte)((int)(number >> 24 & 255L)), (byte)((int)(number >> 32 & 255L)), (byte)((int)(number >> 40 & 255L)), (byte)((int)(number >> 48 & 255L)), (byte)((int)(number >> 56 & 255L))};
    }

    private static void legal(byte[] b, int length) {
        if (b == null || b.length != length) {
            throw new IllegalArgumentException("the length of array should be " + length);
        }
    }

    public static int writeByte(byte b, OutputStream os) throws IOException {
        os.write(b);
        return 1;
    }

    public static int writeShort(short i, OutputStream os) throws IOException {
        os.write(toLH(i));
        return 2;
    }

    public static int writeInt(int i, OutputStream os) throws IOException {
        os.write(toLH(i));
        return 4;
    }

    public static int writeLong(long i, OutputStream os) throws IOException {
        os.write(toLH(i));
        return 8;
    }

    public static int writeBuffer(byte[] buf, OutputStream os) throws IOException {
        int length = writeInt(buf.length, os);
        os.write(buf);
        length += buf.length;
        return length;
    }

    public static long writeString(String msg, OutputStream os) throws IOException {
        return msg == null ? (long)writeInt(0, os) : (long)writeBuffer(msg.getBytes("UTF-8"), os);
    }

    public static long writeString(String msg, OutputStream os, String coding) throws IOException {
        return msg == null ? (long)writeInt(0, os) : (long)writeBuffer(msg.getBytes(coding), os);
    }

    public static long writeToken(OutputStream os, String token) throws IOException {
        Random random = new Random();
        byte tmp = (byte)(random.nextInt(8) + 1);
        byte[] buf = new byte[tmp];

        int length;
        for(length = 0; length < tmp; ++length) {
            buf[length] = (byte)random.nextInt(128);
        }

        os.write(tmp);
        length = length + 1;
        os.write(buf);
        length += buf.length;
        length = (int)((long)length + writeString(token, os));
        return (long)length;
    }
}
