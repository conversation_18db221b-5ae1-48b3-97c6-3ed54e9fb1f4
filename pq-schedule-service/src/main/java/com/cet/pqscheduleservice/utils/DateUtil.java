package com.cet.pqscheduleservice.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/9/9 9:59
 * @description
 */
public class DateUtil {

    /**
     * 年-月-日 时:分:秒 显示格式
     */
    public static final String DATE_TO_STRING_DETAIAL_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final Integer DAY1 = -24;

    /**
     * 年-月-日 显示格式
     */
    public static final String DATE_TO_STRING_SHORT_PATTERN = "yyyy-MM-dd";

    private final static SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");

    private static Logger logger = LoggerFactory.getLogger(DateUtil.class);

    public static Date getCurrentYearEndTime(Date oriTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        Date now = null;
        try {
            c.setTime(oriTime);
            c.add(Calendar.YEAR, 1);
            c.set(Calendar.DATE, 1);
            c.set(Calendar.MONTH, 0);
            String str = sdf.format(c.getTime());
            now = strToDate(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return now;
    }

    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(1);
    }

    /**
     * 获取昨天的开始时间 0时0分0秒0毫秒
     *
     * @return
     */
    public static Long getPreFirstTimeOfDay() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss");
        Calendar lastDate = Calendar.getInstance();
        lastDate.set(Calendar.HOUR_OF_DAY, DAY1);
        lastDate.set(Calendar.MINUTE, 0);
        lastDate.set(Calendar.SECOND, 0);
        lastDate.set(Calendar.MILLISECOND, 0);
        str = sdf.format(lastDate.getTime());
        Date date = strToDate(str, "yyyy-MM-dd HH:mm:ss.sss");
        if (date != null) {
            return date.getTime();
        }
        return 0L;
    }

    public static String formatDate(long source, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        Date date = new Date(source);
        return simpleDateFormat.format(date);
    }

    /**
     * 获取当天的开始时间 0时0分0秒0毫秒
     *
     * @return
     */
    public static Long getFirstTimeOfDay(Long time) {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss");
        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(new Date(time));
        lastDate.set(Calendar.HOUR_OF_DAY, 0);
        lastDate.set(Calendar.MINUTE, 0);
        lastDate.set(Calendar.SECOND, 0);
        lastDate.set(Calendar.MILLISECOND, 0);
        str = sdf.format(lastDate.getTime());
        Date date = strToDate(str, "yyyy-MM-dd HH:mm:ss.sss");
        if (date != null) {
            return date.getTime();
        }
        return 0L;
    }

    /**
     * 获取传入年的开始时间的时间戳
     *
     * @return
     */
    public static long setTime(short year, short month, short dayOfWeek, short day, short hour, short minute, short second, short milliseconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month-1);
        calendar.set(Calendar.WEEK_OF_MONTH, dayOfWeek);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, milliseconds);
        return calendar.getTimeInMillis();
    }

    public static String getLastDayOfMonth(Date oriTime) {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(oriTime);
        lastDate.set(Calendar.MONTH, lastDate.get(Calendar.MONTH) - 1);
        lastDate.set(Calendar.DAY_OF_MONTH, 1);
        str = sdf.format(lastDate.getTime());
        return str;
    }

    public static Date strToDate(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }

    public static Date getCurrentQuarterStartTime(Date oriTime) {
        Calendar c = Calendar.getInstance();
        c.setTime(oriTime);
        int currentMonth = c.get(Calendar.MONTH) + 1;
        Date now = null;
        try {
            if (currentMonth >= 1 && currentMonth <= 3) {
                c.set(Calendar.MONTH, 0);
            } else if (currentMonth >= 4 && currentMonth <= 6) {
                c.set(Calendar.MONTH, 3);
            } else if (currentMonth >= 7 && currentMonth <= 9) {
                c.set(Calendar.MONTH, 6);
            } else if (currentMonth >= 10 && currentMonth <= 12) {
                c.set(Calendar.MONTH, 9);
            }
            c.set(Calendar.DATE, 1);
            now = strToDate(shortSdf.format(c.getTime()) + " 00:00:00");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return now;
    }

    /**
     * 获取传入时间的开始时间的时间戳
     *
     * @return
     */
    public static long getFirstDayOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return getFirstDayOfYear(calendar.get(Calendar.YEAR));
    }

    /**
     * 获取传入年的开始时间的时间戳
     *
     * @return
     */
    public static long getFirstDayOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, 0);
        return calendar.getTimeInMillis();
    }

    public static Date getFirstDayOfMonth(Date oriTime) {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(oriTime);
        lastDate.set(Calendar.DATE, 1);
        str = sdf.format(lastDate.getTime());
        return strToDate(str, "yyyy-MM-dd");
    }


    /**
     * 获取当天的开始时间 0时0分0秒0毫秒
     *
     * @param oriTime
     * @return
     */
    public static Date getFirstTimeOfDay(Date oriTime) {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.sss");
        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(oriTime);
        lastDate.set(Calendar.HOUR_OF_DAY, 0);
        lastDate.set(Calendar.MINUTE, 0);
        lastDate.set(Calendar.SECOND, 0);
        lastDate.set(Calendar.MILLISECOND, 0);
        str = sdf.format(lastDate.getTime());
        return strToDate(str, "yyyy-MM-dd HH:mm:ss.sss");
    }

    public static Date strToDate(String timeStr, String formatStr) {

        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        Date date = null;
        try {
            date = format.parse(timeStr);
        } catch (ParseException e) {
            logger.debug("context", e);
        }
        return date;
    }

    /**
     * 当前季度的结束时间
     *
     * @return
     */
    public static Date getCurrentQuarterEndTime(Date oriTime) {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        c.setTime(oriTime);
        int currentMonth = c.get(Calendar.MONTH) + 1;
        Date now = null;
        try {
            if (currentMonth >= 1 && currentMonth <= 3) {
                c.set(Calendar.MONTH, 3);
                c.set(Calendar.DATE, 1);
            } else if (currentMonth >= 4 && currentMonth <= 6) {
                c.set(Calendar.MONTH, 6);
                c.set(Calendar.DATE, 1);
            } else if (currentMonth >= 7 && currentMonth <= 9) {
                c.set(Calendar.MONTH, 9);
                c.set(Calendar.DATE, 1);
            } else if (currentMonth >= 10 && currentMonth <= 12) {
                c.add(Calendar.YEAR, 1);
                c.set(Calendar.MONTH, 0);
                c.set(Calendar.DATE, 1);
            }
            now = strToDate(formatter.format(c.getTime()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return now;
    }

    public static int getFrontDayYear () {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();

        // 设置为昨天
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        // 设置为0点（午夜）
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 获取年份
       return calendar.get(Calendar.YEAR);

    }
}
