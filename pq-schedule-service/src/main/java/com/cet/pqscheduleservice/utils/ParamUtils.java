package com.cet.pqscheduleservice.utils;

import com.cet.pqscheduleservice.model.common.AggregationCycle;
import com.cet.pqscheduleservice.model.common.CommonManagerException;
import com.cet.pqscheduleservice.model.common.ErrorCode;
import com.cet.pqscheduleservice.model.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName ParamUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/3/2 16:04
 */
public class ParamUtils {
    private static final Logger logger = LoggerFactory.getLogger(ParamUtils.class);

    public static Integer getNextCycle(int cycle) {
        if (cycle == AggregationCycle.ONE_YEAR) {
            return AggregationCycle.ONE_MONTH;
        } else if (cycle == AggregationCycle.ONE_MONTH) {
            return AggregationCycle.ONE_DAY;
        } else if (cycle == AggregationCycle.ONE_DAY) {
            return AggregationCycle.ONE_HOUR;
        }

        return 0;
    }

    /**
     * 检查返回结果
     *
     * @param result
     * @throws RuntimeException
     */
    public static <T> void checkResultGeneric(Result<T> result) {
        if (ErrorCode.SUCCESS_CODE != result.getCode()) {
            logger.error(result.getMsg());
            throw new CommonManagerException("调用接口异常");
        }
    }

    /**
     * 检查返回结果
     *
     * @param result
     * @throws RuntimeException
     */
    public static <T, K> void checkResultGeneric(Result<T> result, K obj) {
        if (ErrorCode.SUCCESS_CODE != result.getCode()) {
            logger.error(result.getMsg());
            if (obj != null) {
                logger.error(JsonTransferUtils.toJsonString(obj));
            }
            throw new CommonManagerException("调用接口异常");
        } else {
            logger.debug(JsonTransferUtils.toJsonString(obj));
        }
    }
}
