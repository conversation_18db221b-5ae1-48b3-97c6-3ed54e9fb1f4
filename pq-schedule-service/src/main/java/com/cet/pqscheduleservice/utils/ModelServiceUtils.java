package com.cet.pqscheduleservice.utils;

import ch.qos.logback.classic.db.names.ColumnName;
import ch.qos.logback.classic.db.names.TableName;
import com.cet.pqscheduleservice.feign.ModelDataService;
import com.cet.pqscheduleservice.model.common.Result;
import com.cet.pqscheduleservice.model.common.ResultWithTotal;
import com.cet.pqscheduleservice.model.query.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Data Created in ${Date}
 */
@Component
public class ModelServiceUtils {

    public static final String ID = "id";

    /**
     * 单次写入数据量
     */
    private static final int WRITE_BATCH = 2000;

    //无效值
    private static final Double invalid = -2147483648.0;

    /**
     * 模型服务
     */
    @Autowired
    private ModelDataService modelServices;


    private static ModelDataService modelService;


    @PostConstruct
    private void init() {
        modelService = this.modelServices;
    }

    /**
     * 查询单个模型数据
     *
     * @param modelIds   模型id
     * @param modelLabel 模型label
     * @param filter     过滤条件
     * @param orders     排序条件
     * @param page       分页
     * @param clazz      结果类型
     * @param <T>
     * @return
     */
    public static <T> List<T> querySingleModel(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter,
                                               List<Order> orders, Page page, Class<T> clazz) {
        QueryCondition condition = getQueryCondition(modelIds, modelLabel, filter, null, orders, page, false, null);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(condition);
        ParamUtils.checkResultGeneric(result);
        return JsonTransferUtils.transferList(result.getData(), clazz);
    }


    /**
     * 查询单个模型数据
     *
     * @param modelIds   模型id
     * @param modelLabel 模型label
     * @param filter     过滤条件
     * @param orders     排序条件
     * @param page       分页
     * @param clazz      结果类型
     * @param <T>
     * @return
     */
    public static <T> List<T> querySingleModel(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter,
                                               List<Order> orders, Page page, boolean composemethod, Class<T> clazz) {
        QueryCondition condition = getQueryCondition(modelIds, modelLabel, filter, null, orders, page, composemethod, null);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(condition);
        ParamUtils.checkResultGeneric(result);
        return JsonTransferUtils.transferList(result.getData(), clazz);
    }


    /**
     * 查询单个模型数据-返回未处理的result
     *
     * @param modelIds   模型id
     * @param modelLabel 模型label
     * @param filter     过滤条件
     * @param orders     排序条件
     * @param page       分页
     * @return
     */
    public static ResultWithTotal<List<Map<String, Object>>> querySingleModel(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter,
                                                                              List<Order> orders, Page page) {
        QueryCondition condition = getQueryCondition(modelIds, modelLabel, filter, null, orders, page, false, null);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(condition);
        ParamUtils.checkResultGeneric(result);
        return result;
    }

    /**
     * 查询单个模型数据, 添加聚合值返回结果 -返回未处理的result
     */
    public static ResultWithTotal<List<Map<String, Object>>> querySingleModelGroupBy(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter,
                                                                              List<Order> orders, Page page, List<GroupBy> groups) {
        QueryCondition condition = getQueryConditionGroupBy(modelIds, modelLabel, filter, null, orders, page, false, null, groups);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(condition);
        ParamUtils.checkResultGeneric(result);
        return result;
    }

    public static QueryCondition getQueryConditionGroupBy(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter, ModelIdPairDTO treeNode, List<Order> orders,
                                                          Page page, boolean composemethod, List<SingleModelConditionDTO> subConditions, List<GroupBy> groups) {
        QueryCondition condition = new QueryCondition();
        condition.setRootId(0L);
        condition.setRootLabel(modelLabel);
        FlatQueryConditionDTO rootCondition = new FlatQueryConditionDTO();
        condition.setRootCondition(rootCondition);
        List<ConditionBlock> filters = new ArrayList<>();
        if (modelIds != null && !modelIds.isEmpty()) {
            filters.add(new ConditionBlock(ID, ConditionBlock.OPERATOR_IN, modelIds));
        }
        if (filter != null && !filter.isEmpty()) {
            filters.addAll(filter);
        }
        if (orders != null && !orders.isEmpty()) {
            rootCondition.setOrders(orders);
        }
        if (page != null) {
            rootCondition.setPage(page);
        }
        if (groups != null) {
            rootCondition.setGroupbys(groups);
        }
        if (null != treeNode) {
            rootCondition.setTreeNode(treeNode);
        }
        // 拼接子层级的过滤条件
        if (!CollectionUtils.isEmpty(subConditions)) {
            condition.setSubLayerConditions(subConditions);
        }
        ConditionBlockCompose filterCondition = new ConditionBlockCompose(filters);
        filterCondition.setComposemethod(composemethod);
        rootCondition.setFilter(filterCondition);
        return condition;
    }

    public static QueryCondition getQueryCondition(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter, ModelIdPairDTO treeNode, List<Order> orders,
                                                   Page page, boolean composemethod, List<SingleModelConditionDTO> subConditions) {
        return getQueryConditionGroupBy(modelIds, modelLabel, filter,treeNode, orders, page, composemethod, subConditions, null);
    }

    /**
     * 限制查询列
     *
     * @param modelIds
     * @param modelLabel
     * @param filter
     * @param treeNode
     * @param orders
     * @param page
     * @param composemethod
     * @param subConditions
     * @param props
     * @return
     */
    public static QueryCondition getQueryCondition(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter, ModelIdPairDTO treeNode, List<Order> orders,
                                                   Page page, boolean composemethod, List<SingleModelConditionDTO> subConditions, List<String> props) {
        QueryCondition condition = new QueryCondition();
        condition.setRootId(0L);
        condition.setRootLabel(modelLabel);
        FlatQueryConditionDTO rootCondition = new FlatQueryConditionDTO();
        condition.setRootCondition(rootCondition);
        List<ConditionBlock> filters = new ArrayList<>();
        if (modelIds != null && !modelIds.isEmpty()) {
            filters.add(new ConditionBlock(ID, ConditionBlock.OPERATOR_IN, modelIds));
        }
        if (filter != null && !filter.isEmpty()) {
            filters.addAll(filter);
        }
        if (orders != null && !orders.isEmpty()) {
            rootCondition.setOrders(orders);
        }
        if (props != null && !props.isEmpty()) {
            rootCondition.setProps(props);
        }
        if (page != null) {
            rootCondition.setPage(page);
        }
        if (null != treeNode) {
            rootCondition.setTreeNode(treeNode);
        }
        // 拼接子层级的过滤条件
        if (!CollectionUtils.isEmpty(subConditions)) {
            condition.setSubLayerConditions(subConditions);
        }
        ConditionBlockCompose filterCondition = new ConditionBlockCompose(filters);
        filterCondition.setComposemethod(composemethod);
        rootCondition.setFilter(filterCondition);
        return condition;
    }

    /**
     * 重写组装查询参数的方法，可以传是否查询间接关联
     *
     * @param modelIds
     * @param modelLabel
     * @param filter
     * @param treeNode
     * @param orders
     * @param page
     * @param composemethod
     * @param subConditions
     * @return
     */
    public static QueryCondition getQueryCondition(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter, ModelIdPairDTO treeNode, List<Order> orders,
                                                   Page page, boolean composemethod, List<SingleModelConditionDTO> subConditions, Boolean allowInterRelation) {
        QueryCondition queryCondition = getQueryCondition(modelIds, modelLabel, filter, treeNode, orders, page, composemethod, subConditions);
        queryCondition.setAllowInterRelation(allowInterRelation);
        return queryCondition;

    }

    /**
     * 查询单个模型数据-返回未处理的result
     *
     * @param modelIds   模型id
     * @param modelLabel 模型label
     * @param filter     过滤条件
     * @param orders     排序条件
     * @param page       分页
     * @return
     */
    public static ResultWithTotal<List<Map<String, Object>>> querySingleModelOr(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter,
                                                                                List<Order> orders, Page page, boolean composemethod) {
        QueryCondition condition = getQueryCondition(modelIds, modelLabel, filter, null, orders, page, composemethod, null);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(condition);
        ParamUtils.checkResultGeneric(result);
        return result;
    }

    /**
     * 查询单个模型数据-或查询（可选）
     *
     * @param modelIds   模型id
     * @param modelLabel 模型label
     * @param filter     过滤条件
     * @param orders     排序条件
     * @param page       分页
     * @param clazz      结果类型
     * @param <T>
     * @return
     */
    public static <T> List<T> querySingleModelOr(List<Long> modelIds, String modelLabel, List<ConditionBlock> filter,
                                                 List<Order> orders, Page page, boolean composemethod, Class<T> clazz) {
        QueryCondition condition = getQueryCondition(modelIds, modelLabel, filter, null, orders, page, composemethod, null);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(condition);
        ParamUtils.checkResultGeneric(result);
        return JsonTransferUtils.transferList(result.getData(), clazz);
    }

    /**
     * 返回_model树
     *
     * @param parentIds     父节点的id，可以为空
     * @param parentLabel   父节点的modelLabel，不可为空
     * @param filters       父层级的过滤条件
     * @param subConditions 自层级的过滤条件
     * @return 查询结果
     */
    public static List<Map<String, Object>> getRelations(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters, ModelIdPairDTO modelIdPairDTO,
                                                         List<SingleModelConditionDTO> subConditions) {
        return getInterRelations(parentIds, parentLabel, filters, modelIdPairDTO, subConditions, Boolean.FALSE);
    }

    /**
     * 返回_model树
     *
     * @param parentIds     父节点的id，可以为空
     * @param parentLabel   父节点的modelLabel，不可为空
     * @param filters       父层级的过滤条件
     * @param subConditions 自层级的过滤条件
     * @return 查询结果
     */
    public static List<Map<String, Object>> getInterRelations(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters, ModelIdPairDTO modelIdPairDTO,
                                                              List<SingleModelConditionDTO> subConditions, Boolean allowInterRelation) {
        QueryCondition queryCondition = getQueryCondition(parentIds, parentLabel, filters, modelIdPairDTO, null, null, false, subConditions);
        queryCondition.setAllowInterRelation(allowInterRelation);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(queryCondition);
        ParamUtils.checkResultGeneric(result, queryCondition);
        return result.getData();
    }

    /**
     * 返回_model树
     *
     * @param parentIds          父节点的id，可以为空
     * @param parentLabel        父节点的modelLabel，不可为空
     * @param filters            父层级的过滤条件
     * @param subConditions      自层级的过滤条件
     * @param allowInterRelation
     * @param orders             排序结果
     * @return 查询结果
     */
    public static List<Map<String, Object>> getInterRelations(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters, ModelIdPairDTO modelIdPairDTO,
                                                              List<SingleModelConditionDTO> subConditions, Boolean allowInterRelation, List<Order> orders) {
        QueryCondition queryCondition = getQueryCondition(parentIds, parentLabel, filters, modelIdPairDTO, orders, null, false, subConditions);
        queryCondition.setAllowInterRelation(allowInterRelation);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(queryCondition);
        ParamUtils.checkResultGeneric(result, queryCondition);
        return result.getData();
    }


    /**
     * 多节点关联查询--返回children树
     *
     * @param parentIds     父id
     * @param parentLabel   父label
     * @param filters       父条件
     * @param subConditions 子查询条件
     * @return
     */
    public static List<Map<String, Object>> getRelationTree(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters,
                                                            List<SingleModelConditionDTO> subConditions) {
        QueryCondition queryCondition = getQueryCondition(parentIds, parentLabel, filters, null, null, null, false, subConditions);
        queryCondition.setTreeReturnEnable(Boolean.TRUE);
        queryCondition.setAllowInterRelation(Boolean.FALSE);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(queryCondition);
        ParamUtils.checkResultGeneric(result);
        return result.getData();
    }

    /**
     * 多节点关联十项全能
     *
     * @param parentIds        父id
     * @param parentLabel      父label
     * @param filters          父条件
     * @param orders
     * @param page
     * @param composemethod
     * @param subConditions    子查询条件
     * @param treeReturnEnable
     * @return
     */
    public static List<Map<String, Object>> getRelationships(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters, List<Order> orders,
                                                             Page page, boolean composemethod, List<SingleModelConditionDTO> subConditions, boolean treeReturnEnable) {
        QueryCondition queryCondition = getQueryCondition(parentIds, parentLabel, filters, null, null, null, composemethod, subConditions);
        queryCondition.setTreeReturnEnable(treeReturnEnable);
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(queryCondition);
        ParamUtils.checkResultGeneric(result);
        return result.getData();
    }

    /**
     * 查询节点，并带上子层级节点的信息
     *
     * @param parentIds   父层级节点id列表，非必传
     * @param parentLabel 父层级的modelLabel，必传
     * @param filters     父层级的过滤条件
     * @param subNodes    子层级节点
     * @param clazz       需要转换的数据类型
     * @param <T>         返回值类型
     * @return 查询出来的节点列表
     */
    public static <T> List<T> queryWithChildren(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters, ModelIdPairDTO modelIdPairDTO,
                                                List<String> subNodes, Class<T> clazz) {
        List<Map<String, Object>> list = queryWithChildren(parentIds, parentLabel, filters, modelIdPairDTO, subNodes);
        return JsonTransferUtils.transferList(list, clazz);
    }

    /**
     * 查询节点，并带上子层级节点的信息
     *
     * @param parentIds   父层级节点id列表，非必传
     * @param parentLabel 父层级的modelLabel，必传
     * @param filters     父层级的过滤条件
     * @param subNodes    子层级节点
     * @return 查询出来的节点列表
     */
    public static List<Map<String, Object>> queryWithChildren(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters, ModelIdPairDTO modelIdPairDTO,
                                                              List<String> subNodes) {
        List<SingleModelConditionDTO> subConditions = getSingleModelConditionDTOS(subNodes);

        return getRelations(parentIds, parentLabel, filters, modelIdPairDTO, subConditions);
    }

    public static List<SingleModelConditionDTO> getSingleModelConditionDTOS(List<String> subNodes) {
        List<SingleModelConditionDTO> subConditions = null;
        if (!CollectionUtils.isEmpty(subNodes)) {
            subConditions = new ArrayList<>();
            for (String subNode : subNodes) {
                subConditions.add(new SingleModelConditionDTO(subNode));
            }
        }
        return subConditions;
    }

    /**
     * 查询节点，并带上子层级节点的信息-返回_model
     *
     * @param parentIds   父层级节点id列表，非必传
     * @param parentLabel 父层级的modelLabel，必传
     * @param filters     父层级的过滤条件
     * @param subLayers   子层级
     * @param clazz       需要转换的数据类型
     * @param <T>         返回值类型
     * @return 查询出来的节点列表
     */
    public static <T> List<T> queryWithChildrenCondition(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters, ModelIdPairDTO modelIdPairDTO,
                                                         List<SingleModelConditionDTO> subLayers, Class<T> clazz) {
        List<Map<String, Object>> list = getRelations(parentIds, parentLabel, filters, modelIdPairDTO, subLayers);
        return JsonTransferUtils.transferList(list, clazz);
    }

    /**
     * 查询节点，并带上子层级节点的信息-返回children
     *
     * @param parentIds   父层级节点id列表，非必传
     * @param parentLabel 父层级的modelLabel，必传
     * @param filters     父层级的过滤条件
     * @param subLayers   子层级
     * @param clazz       需要转换的数据类型
     * @param <T>         返回值类型
     * @return 查询出来的节点列表
     */
    public static <T> List<T> queryWithChildrenConditionTree(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters,
                                                             List<SingleModelConditionDTO> subLayers, Class<T> clazz) {
        List<Map<String, Object>> list = getRelationTree(parentIds, parentLabel, filters, subLayers);
        return JsonTransferUtils.transferList(list, clazz);
    }

    /**
     * 查询节点，并带上子层级节点的信息-返回children
     *
     * @param parentIds        父层级节点id列表，非必传
     * @param parentLabel      父层级的modelLabel，必传
     * @param filters          父层级的过滤条件
     * @param orders           排序条件
     * @param page             分页
     * @param composemethod
     * @param subConditions    子层级
     * @param treeReturnEnable
     * @param clazz            需要转换的数据类型
     * @param <T>              返回值类型
     * @return 查询出来的节点列表
     */
    public static <T> List<T> queryWithChildrenConditionOrderTree(List<Long> parentIds, String parentLabel, List<ConditionBlock> filters,
                                                                  List<Order> orders, Page page, boolean composemethod, List<SingleModelConditionDTO> subConditions, boolean treeReturnEnable, Class<T> clazz) {
        List<Map<String, Object>> list = getRelationships(parentIds, parentLabel, filters, orders, page, composemethod, subConditions, treeReturnEnable);
        return JsonTransferUtils.transferList(list, clazz);
    }

    /**
     * 写入数据
     *
     * @param data  需要写入的数据
     * @param clazz 需要转换的数据类型
     * @param <T>   数据类型
     * @return 写入成功后返回的数据
     */
    @SuppressWarnings("rawtypes")
    public static <T> List<T> writeData(List<T> data, Boolean ignoreDuplicate, Boolean returnData, Class<T> clazz) {
        Result<Object> result = modelService.write(data);
        ParamUtils.checkResultGeneric(result);
        return JsonTransferUtils.transferList((List) result.getData(), clazz);
    }

    /**
     * 批量写入数据
     *
     * @param data  需要写入的数据
     * @param clazz 需要转换的数据类型
     * @param <T>   数据类型
     */
    public static <T> void writeDataBatch(List<T> data, Boolean ignoreDuplicate, Boolean returnData, Class<T> clazz) {
        int count = data.size();

        int n = 0;
        int total = count / WRITE_BATCH;
        total = (count % WRITE_BATCH) == 0 ? total : (total + 1);
        while (n < total) {
            int end = Math.min((n + 1) * WRITE_BATCH, count);
            writeData(data.subList(n * WRITE_BATCH, end), ignoreDuplicate, returnData, clazz);

            n++;
        }
    }


    /**
     * 写入数据
     *
     * @param data 入库数据
     * @return 入库结果
     */
    public static Result<Object> write(@RequestBody Object data) {
        Result<Object> result = modelService.write(data);
        ParamUtils.checkResultGeneric(result);
        return result;
    }


    public static <T> List<T> queryModelData(QueryCondition queryCondition, Class<T> clazz) {
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(queryCondition);
        ParamUtils.checkResultGeneric(result);
        return JsonTransferUtils.transferList(result.getData(), clazz);
    }

    public static Optional<List<Map<String, Object>>> getModelData(QueryCondition queryCondition) {
        Result<List<Map<String, Object>>> result = modelService.query(queryCondition);
        Optional<Result<List<Map<String, Object>>>> resultOptional = Optional.ofNullable(result);
        return resultOptional.filter(u -> Result.SUCCESS_CODE == u.getCode() && !CollectionUtils.isEmpty(u.getData())).map(Result::getData);
    }

    public static List<SingleModelConditionDTO> createSubCondition(List<Long> id, String modelLabel) {
        SingleModelConditionDTO singleModelCondition = new SingleModelConditionDTO();
        singleModelCondition.setModelLabel(modelLabel);
        ConditionBlock idCondition = new ConditionBlock("id", ConditionBlock.OPERATOR_IN, id);
        List<ConditionBlock> conditionBlockList = Collections.singletonList(idCondition);
        singleModelCondition.setFilter(new ConditionBlockCompose(conditionBlockList));
        return Collections.singletonList(singleModelCondition);
    }

    public static QueryCondition createQueryCondition(Long modelId, String modelLabel) {
        QueryCondition queryCondition = new QueryCondition();
        queryCondition.setRootLabel(modelLabel);
        queryCondition.setRootId(modelId);
        queryCondition.setRootCondition(new FlatQueryConditionDTO());
        return queryCondition;
    }

    /**
     * @param modelId
     * @param modelLabel
     * @param parentLabel
     * @Description: 根据子层级查父层级
     * @Author: gongtong
     **/
    public static List<Map<String, Object>> getParentByChildren(Long modelId, String modelLabel, String parentLabel) {
        QueryCondition queryCondition = new QueryCondition(parentLabel);
        queryCondition.setSubLayerConditions(ModelServiceUtils.createSubCondition(Collections.singletonList(modelId), modelLabel));
        ResultWithTotal<List<Map<String, Object>>> result = modelService.query(queryCondition);
        return result.getData().stream().filter(t -> t.get(modelLabel + "_model") != null).collect(Collectors.toList());
    }





}
