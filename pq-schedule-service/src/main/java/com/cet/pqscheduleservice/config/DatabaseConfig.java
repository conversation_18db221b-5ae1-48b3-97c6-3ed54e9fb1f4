package com.cet.pqscheduleservice.config;

import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Description:
 * @date 2021/9/30 14:24
 */
@Configuration
public class DatabaseConfig {
    @Bean
    public DatabaseIdProvider databaseIdProvider() {
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties p = new Properties();
        p.setProperty("Oracle", "oracle");
        p.setProperty("PostgreSQL", "postgresql");
        p.setProperty("DM DBMS", "dm");
        databaseIdProvider.setProperties(p);
        return databaseIdProvider;
    }
}
