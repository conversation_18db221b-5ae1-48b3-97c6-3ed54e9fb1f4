package com.cet.pqscheduleservice.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/6/24 12:17
 * @description
 */
@Configuration
public class FeignInterceptorConfig {
    @Value("${spring.profiles.active}")
    private String active;

    @Bean
    public RequestInterceptor cloudContextInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                String url = template.url();
                //山东的feign调用定制化，需要带服务名
                if (!"sd".equals(active.toLowerCase())) {
                    url = url.replace("/model-service", "").replace("/pec-node-service", "")
                            .replace("/cloud-auth-service", "").replace("/device-data-service", "");
                }
                template.uri(url);
            }
        };
    }
}
