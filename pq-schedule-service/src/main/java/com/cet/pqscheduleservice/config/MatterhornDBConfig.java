package com.cet.pqscheduleservice.config;


import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.cet.pqscheduleservice.model.common.Constants;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @ClassName configDBConfig
 * @date ：Created in 2022/7/22 10:21
 * @description： config DB连接
 */
@Configuration
@MapperScan(basePackages = MatterhornDBConfig.PACKAGE, sqlSessionFactoryRef = "matterhornSqlSessionFactory")
@PropertySource(value = {
        Constants.LOAD_CONFIG_FILE_PATH + "application.yml"})
public class MatterhornDBConfig {

    static final String PACKAGE = "com.cet.pqscheduleservice.mapper.matterhorn";
    static final String MAPPER_LOCATION = "classpath:mapper/matterhorn/*.xml";

    @Bean(name = "matterhornDataSource")
    @ConfigurationProperties(prefix = "datasource.druid.matterhorn")
    @Primary
    public DataSource matterhornDB() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "matterhornTransactionManager")
    @Primary
    public DataSourceTransactionManager matterhornTransactionManager() {
        return new DataSourceTransactionManager(matterhornDB());
    }

    @Bean(name = "matterhornSqlSessionFactory")
    @Primary
    public SqlSessionFactory matterhornSqlSessionFactory(@Qualifier("matterhornDataSource") DataSource configDataSource, DatabaseIdProvider databaseIdProvider)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(configDataSource);
        sessionFactory.setDatabaseIdProvider(databaseIdProvider);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources(MatterhornDBConfig.MAPPER_LOCATION));
        SqlSessionFactory sqlSessionFactory = sessionFactory.getObject();
        sqlSessionFactory.getConfiguration().setJdbcTypeForNull(JdbcType.NULL);
        return sessionFactory.getObject();
    }

}
