package com.cet.pqscheduleservice.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.cet.pqscheduleservice.model.common.Constants;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @ClassName dataDBConfig
 * @date ：Created in 2021/1/22 10:21
 * @description： data DB连接
 */
@Configuration
@MapperScan(basePackages = DataDBConfig.PACKAGE, sqlSessionFactoryRef = "dataSqlSessionFactory")
@PropertySource(value = {
        Constants.LOAD_CONFIG_FILE_PATH + "application.yml"})
public class DataDBConfig {

    static final String PACKAGE = "com.cet.pqscheduleservice.mapper.data";
    static final String MAPPER_LOCATION = "classpath:mapper/data/*.xml";

    @Bean(name = "dataDataSource")
    @ConfigurationProperties(prefix = "datasource.druid.data")
    public DataSource dataDB() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "dataTransactionManager")
    public DataSourceTransactionManager dataTransactionManager() {
        return new DataSourceTransactionManager(dataDB());
    }

    @Bean(name = "dataSqlSessionFactory")
    public SqlSessionFactory dataSqlSessionFactory(@Qualifier("dataDataSource") DataSource dataDataSource,DatabaseIdProvider databaseIdProvider)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataDataSource);
        sessionFactory.setDatabaseIdProvider(databaseIdProvider);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources(DataDBConfig.MAPPER_LOCATION));
        SqlSessionFactory sqlSessionFactory = sessionFactory.getObject();
        sqlSessionFactory.getConfiguration().setJdbcTypeForNull(JdbcType.NULL);
        return sessionFactory.getObject();
    }

}
