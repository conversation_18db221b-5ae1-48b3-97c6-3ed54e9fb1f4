package com.cet.pqscheduleservice.annotation;



import com.cet.pqscheduleservice.model.enums.BatchType;

import java.lang.annotation.*;

/**
 * 	自定义注解
 * <AUTHOR>
 *
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface Batch {

    /**
     * 批量类型
     * @return
     */
    BatchType type();

    /**
     * 批量处理量
     * @return
     */
    int size() default 999;
}
