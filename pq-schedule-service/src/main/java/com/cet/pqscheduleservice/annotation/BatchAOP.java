package com.cet.pqscheduleservice.annotation;

import com.cet.pqscheduleservice.model.enums.BatchType;
import com.google.common.collect.Lists;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 	BatchAOP
 * <AUTHOR>
 *
 */
@Aspect
@Component
public class BatchAOP {

    @Pointcut("@annotation(com.cet.pqscheduleservice.annotation.Batch)")
    public void pointcut() {
    }

    @SuppressWarnings("unchecked")
    @Around(value = "pointcut()")
    public Object around(ProceedingJoinPoint thisJoinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) thisJoinPoint.getSignature();

        //实际调用的方法
        Method method = signature.getMethod();
        Batch annotation = method.getAnnotation(Batch.class);
        if (null == annotation) {
            return thisJoinPoint.proceed();
        }
        Object[] args = thisJoinPoint.getArgs();
        if (args == null || args.length == 0) {
            return thisJoinPoint.proceed();
        }
        Object arg = args[0];
        if (arg instanceof List) {

            if (CollectionUtils.isEmpty((List<?>) arg)) {
                //空参数不做任何处理交给用户处理
                return thisJoinPoint.proceed();
            }
            List list = (List) arg;
            int size = annotation.size();
            if (list.size() < size) {
                return thisJoinPoint.proceed();
            }
            //把list处理成多个list 分批执行
            List<? extends List<?>> split = split(list, size);
            //Object[] objects = new Object[args.length];

            //第一个参数数id后面参数
            //System.arraycopy(args, 0, objects, 1, args.length - 1);

            //实际调用方法的对象
            Object target = thisJoinPoint.getTarget();
            BatchType type = annotation.type();
            if (type == BatchType.QUERY) {
                return query(split, method, target, args);
            }
            return common(split, method, target, args);
        } else {
            return thisJoinPoint.proceed();
        }
    }

    @SuppressWarnings("unchecked")
    private Object query(List<? extends List<?>> split, Method method, Object target, Object[] objects) throws Exception {
        List list = new ArrayList();
        for (List<?> l : split) {
            objects[0] = l;
            List invoke = (List) method.invoke(target, objects);
            list.addAll(invoke);
        }
        return list;
    }

    @SuppressWarnings("unchecked")
    private Object common(List<? extends List<?>> split, Method method, Object target, Object[] objects) throws Exception {
        for (List<?> l : split) {
            objects[0] = l;
            method.invoke(target, objects);
        }
        return null;
    }

    private static <T> List<List<T>> split(List<T> resList, int subListLength) {
        if (CollectionUtils.isEmpty(resList) || subListLength <= 0) {
            return Lists.newArrayList();
        }
        List<List<T>> ret = Lists.newArrayList();
        int size = resList.size();
        if (size <= subListLength) {
            // 数据量不足 subListLength 指定的大小
            ret.add(resList);
        } else {
            int pre = size / subListLength;
            int last = size % subListLength;
            // 前面pre个集合，每个大小都是 subListLength 个元素
            for (int i = 0; i < pre; i++) {
                List<T> itemList = Lists.newArrayList();
                for (int j = 0; j < subListLength; j++) {
                    itemList.add(resList.get(i * subListLength + j));
                }
                ret.add(itemList);
            }
            // last的进行处理
            if (last > 0) {
                List<T> itemList = Lists.newArrayList();
                for (int i = 0; i < last; i++) {
                    itemList.add(resList.get(pre * subListLength + i));
                }
                ret.add(itemList);
            }
        }
        return ret;
    }
}
