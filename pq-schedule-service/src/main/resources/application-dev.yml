logging:
  file: logs/${spring.application.name}.log
  config: classpath:logback-boot.xml
cet:
  log:
    enabled: true

#每日5：0补招任务
procedure:
  daily:
    cron: 0 0 5,15 * * ?
  month:
    cron: 0 0 20 4 * ?
  status:
    cron: 0 0 */1 * * ?
  event:
    # 暂态事件转存  4.0及4.1需开启  4.2需关闭
    enabled: false
    cron: 0 30 */1 * * ?
  tolerance:
    cron: 0 50 */1 * * ?
  cause:
    enabled: false
    cron: 0 0 5 * * ?

deviceEvent:
  cron: 0 0 6 * * ?
  enabled: false
#注册中心
eureka:
  client:
    service-url:
      defaultZone: http://************:1001/eureka/
  instance:
    lease-renewal-interval-in-seconds: 15
    prefer-ip-address: true

#数据库连接，matterhorn数据库

#datasource:
#  # oracle 或 pg
#  druid:
#    matterhorn:
#      driver-class-name: oracle.jdbc.OracleDriver
#      url: ****************************************
#      username: MATTERHORN_HENAN
#      password: sA123456
#    data:
#      driver-class-name: oracle.jdbc.OracleDriver
#      url: ****************************************
#      username: PECSTAR_DATA_HENAN
#      password: sA123456

#线程池配置
task:
  queue:
    corePoolSize: 10
    maxPoolSize: 20
    queueCapacity: 200
    keepAlive: 60
    trigger: 10

datasource:
  # oracle 或 pg
  druid:
    matterhorn:
      driver-class-name: org.postgresql.Driver
      url: **************************************************
      username: postgres
      password: ENC(sajCBC1+pCu3CJAuUoLiNlFKkOppnkGR)
    data:
      driver-class-name: org.postgresql.Driver
      url: jdbc:postgresql://************:15433/PECSTAR_DATA_pq
      username: postgres
      password: ENC(sajCBC1+pCu3CJAuUoLiNlFKkOppnkGR)
    config:
      driver-class-name: org.postgresql.Driver
      url: ******************************************************
      username: postgres
      password: ENC(sajCBC1+pCu3CJAuUoLiNlFKkOppnkGR)