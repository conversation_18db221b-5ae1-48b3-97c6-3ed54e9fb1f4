server:
  port: 8156
spring:
  profiles:
    active: dev
  application:
    name: pq-schedule-service

pq:
  dataId: 2
  #监测点状态统计需要版本区分
  version: 4.0

ribbon:
  ReadTimeout: 32000
  ConnectTimeout: 35000
  eureka:
    enabled: true

device:
  data:
    name: device-data-service

event:
  aireasonenabled: 'true'

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    # 山东现场要求的密文：1234567890
    password: 1234567890

swagger:
  enabled: false
