<?xml version="1.0" encoding="UTF-8"?>
<assembly>
    <id>package</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <fileSets>
        <!--配置-->
        <fileSet>
            <directory>src/main/resources/install</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>*</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources/</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>application.yml</include>
                <include>application-dev.yml</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>