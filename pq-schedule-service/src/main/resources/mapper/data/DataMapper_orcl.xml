<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.data.DataMapper">

    <resultMap type="com.cet.pqscheduleservice.model.event.WaveData" id="waveMap_orcl">
        <result column="logTime" property="logTime" javaType="java.util.Date" />
        <result column="deviceId" property="deviceId" javaType="java.lang.Long"/>
        <result column="msec" property="msec"  javaType="java.lang.Integer"/>
    </resultMap>

    <select id="queryWave" resultMap="waveMap_orcl" databaseId="oracle">
         <![CDATA[
                select logTime,deviceId,msec from ${tableName}
        ]]>
        <where>
            <foreach collection="eventLogVos" item="item" open="(" separator="or" close=")">
                <![CDATA[ (deviceid = #{item.deviceId} and  (logtime - TO_DATE('1970-01-01 08', 'YYYY-MM-DD HH24')) * 86400000 + msec > (#{item.eventTime} + #{item.msec} - 500)
                 and (logtime - TO_DATE('1970-01-01 08', 'YYYY-MM-DD HH24')) * 86400000 + msec < (#{item.eventTime} + #{item.msec} + 500))
             ]]>
            </foreach>
        </where>
    </select>


    <select id="queryDeviceEventStartId" resultType="java.lang.Long" databaseId="oracle">
         <![CDATA[
        SELECT * FROM ( SELECT id FROM pd_tb_06_${year} where eventtype = 100
         ]]>
        <if test="starttimestamp != null">
            and eventtime &gt;= #{starttimestamp, jdbcType=TIMESTAMP}
        </if>
        <if test="endtimestamp != null">
            and eventtime &lt; #{endtimestamp, jdbcType=TIMESTAMP}
        </if>
        <![CDATA[
        ORDER BY eventtime ASC, id ASC ) WHERE ROWNUM = 1
        ]]>
    </select>

    <select id="queryDeviceEventCount" resultType="java.lang.Integer" databaseId="oracle">
         <![CDATA[
        SELECT count(1) FROM pd_tb_06_${year} where eventtype = 100
        ]]>
        <if test="starttimestamp != null">
            and eventtime &gt;= #{starttimestamp, jdbcType=TIMESTAMP}
        </if>
        <if test="endtimestamp != null">
            and eventtime &lt; #{endtimestamp, jdbcType=TIMESTAMP}
        </if>
    </select>

</mapper>
