<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.data.DataMapper">

    <resultMap type="com.cet.pqscheduleservice.model.event.WaveData" id="waveMap">
        <result column="logTime" property="logTime" javaType="java.util.Date" />
        <result column="deviceId" property="deviceId" javaType="java.lang.Long"/>
        <result column="msec" property="msec"  javaType="java.lang.Integer"/>
    </resultMap>

    <select id="queryWave" resultMap="waveMap" databaseId="postgresql">
         <![CDATA[
                select logTime,deviceId,msec from ${tableName}
        ]]>
        <where>
            <foreach collection="eventLogVos" item="item" open="(" separator="or" close=")">
                <![CDATA[(deviceid = #{item.deviceId} and  EXTRACT(epoch FROM logtime) * 1000 -28800000 + msec > (#{item.eventTime} + #{item.msec} - 500)
                 and EXTRACT(epoch FROM logtime) * 1000 -28800000 + msec < (#{item.eventTime} + #{item.msec} + 500))
                 ]]>
            </foreach>
        </where>
    </select>

    <select id="queryDeviceEventStartId" resultType="java.lang.Long" databaseId="postgresql">
         <![CDATA[
         SELECT id FROM pd_tb_06_${year} where eventtype = 100
         ]]>
        <if test="starttimestamp != null">
            and eventtime &gt;= #{starttimestamp, jdbcType=TIMESTAMP}
        </if>
        <if test="endtimestamp != null">
            and eventtime &lt; #{endtimestamp, jdbcType=TIMESTAMP}
        </if>
        <![CDATA[
        ORDER BY eventtime ASC, id ASC limit 1
        ]]>
    </select>

    <select id="queryDeviceEventCount" resultType="java.lang.Integer" databaseId="postgresql">
         <![CDATA[
        SELECT count(1) FROM pd_tb_06_${year} where eventtype = 100
        ]]>
        <if test="starttimestamp != null">
            and eventtime &gt;= #{starttimestamp, jdbcType=TIMESTAMP}
        </if>
        <if test="endtimestamp != null">
            and eventtime &lt; #{endtimestamp, jdbcType=TIMESTAMP}
        </if>
    </select>

</mapper>
