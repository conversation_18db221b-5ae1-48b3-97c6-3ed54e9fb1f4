<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.config.ConfigMapper">

    <resultMap id="pecConfigResultMap" type="com.cet.pqscheduleservice.model.config.PecConfig">
        <result property="nodeType" column="nodetype" />
        <result property="nodeId" column="nodeid" />
        <result property="nodeName" column="nodename" />
        <result property="data" column="data" />
        <result property="parentNodeId" column="parentnodeid" />
        <result property="parentNodeType" column="parentnodetype" />
    </resultMap>


    <select id="queryAllSystemNetwork" resultMap="pecConfigResultMap">
        <![CDATA[
                select * from PC_TB_05 where PARENTNODEID in (select NODEID from PC_TB_05 where NODENAME = '系统网络')
        ]]>
    </select>

    <select id="queryStationId" resultType="java.lang.Long">
        <![CDATA[
                select nodeid from PC_TB_05 where nodetype = 269615104
        ]]>
    </select>

    <select id="queryChannelId" resultMap="pecConfigResultMap">
        <![CDATA[
                select * from PC_TB_05
        ]]>
        <where>
            <foreach collection="stationIds" index="index" item="item"
                     separator="or" open="(" close=")">
                (PARENTNODETYPE = 269619200 and PARENTNODEID = ${item})
            </foreach>
        </where>
    </select>

</mapper>
