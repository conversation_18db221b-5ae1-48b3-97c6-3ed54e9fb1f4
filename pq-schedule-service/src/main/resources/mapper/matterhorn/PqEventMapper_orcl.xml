<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper">

    <resultMap type="com.cet.pqscheduleservice.model.event.Measuredby" id="measuredbyMap_orcl">
        <result column="measuredby" property="measuredBy" javaType="java.lang.Long"/>
        <result column="monitoredid" property="monitoredId" javaType="java.lang.Long"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.PqEventVariation" id="pqEventVariationMap_orcl">
        <result column="id" property="id" javaType="java.lang.Long"/>
        <result column="srcdeviceid" property="deviceid" javaType="java.lang.Long"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.SemiScheme" id="semiSchemeMap_orcl">
        <result column="semiScheme_id" property="semiScheme_id" javaType="java.lang.Integer"/>
        <result column="itemid" property="itemId" javaType="java.lang.Integer"/>
        <result column="idx" property="idx" javaType="java.lang.Integer"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.IticScheme" id="iticSchemeMap_orcl">
        <result column="iticScheme_id" property="iticScheme_id" javaType="java.lang.Integer"/>
        <result column="itemId" property="itemId" javaType="java.lang.Integer"/>
        <result column="idx" property="idx" javaType="java.lang.Integer"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
        <result column="isup" property="isup" javaType="java.lang.Boolean"/>
    </resultMap>

    <select id="queryEventStartId" resultType="java.lang.Long" databaseId="oracle">
         <![CDATA[
                select eventid from PDI_TRANSEVENTPOI where transtype = 'transient' and year = ${year}
        ]]>
    </select>

    <select id="queryMeasureby" resultMap="measuredbyMap_orcl" databaseId="oracle">
         <![CDATA[
                select measuredby,monitoredid  from Measuredby
        ]]>
    </select>

    <insert id="insertPqEventVariation" parameterType="com.cet.pqscheduleservice.model.event.PqEventVariation" databaseId="oracle">
        <trim prefix="begin" suffix="; end;">
        <foreach collection="pqEventTransients" index="index" item="item"
                 separator=";">
            <![CDATA[
             merge into pqvariationevent a
              using
              (
                select 1 from dual
              )b
              on (a.monitoredlabel = #{item.monitoredlabel} and a.monitoredid = #{item.monitoredid} and a.eventtime=#{item.eventtime} and a.pqvariationeventtype=#{item.pqvariationeventtype}  and a.v1trigger=#{item.v1trigger}
              and a.v2trigger=#{item.v2trigger} and a.v3trigger=#{item.v3trigger})
              when matched then update set
                a.magnitude = #{item.magnitude},
                a.duration = #{item.duration},
                a.faultreason = #{item.faultreason},
                a.escapestatus = #{item.escapestatus},
                a.reason = #{item.reason}
              when not matched then
                insert (id,monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,
                                    v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy, escapestatus, reason, faultreason)
                       values (pqvariationevent_SEQ.nextval,#{item.monitoredlabel},#{item.monitoredid},#{item.deviceid},#{item.eventtime},#{item.pqvariationeventtype},#{item.nominalvoltage},#{item.duration},#{item.v1duration},
                               #{item.v2duration},#{item.v3duration},#{item.magnitude},#{item.v1magnitude},#{item.v2magnitude},#{item.v3magnitude},#{item.v1trigger},#{item.v2trigger},#{item.v3trigger},#{item.waveformlogtime},#{item.valid},#{item.description},#{item.confirmeventstatus},#{item.remark},#{item.updatetime},#{item.transientfaultdirection},#{item.toleranceband},#{item.operator},#{item.isminuteextreme},#{item.lossenergy},#{item.escapestatus},#{item.reason},#{item.faultreason})
        ]]>
        </foreach>
        </trim>
    </insert>

    <update id="updatePqEventPoi" databaseId="oracle">
        <![CDATA[update PDI_TRANSEVENTPOI set eventid =${count} ,updatetime = date2utc(LOCALTIMESTAMP)  where transtype = 'transient' and year = ${year}
        ]]>
    </update>

    <select id="queryToleranceEvent" resultMap="pqEventVariationMap_orcl" databaseId="oracle">
         <![CDATA[
               select id,srcdeviceid,duration,magnitude from pqvariationevent  where id > (select max(pqvariationevent_id) from pqvariationeventsemiinfo)
        ]]>
    </select>

    <select id="querySemiScheme" resultMap="semiSchemeMap_orcl" databaseId="oracle">
         <![CDATA[
               select tb.a as semiScheme_id,id as itemId,idx,duration,magnitude from semischemeitem it, (select a, b from  model_ins_rel_search_multi_v2('semischeme',ID_LIST(),'{}','semischemeitem')) tb where it.id = tb.b
        ]]>
    </select>

    <select id="queryIticScheme" resultMap="iticSchemeMap_orcl" databaseId="oracle">
         <![CDATA[
               select tb.a as iticScheme_id,id as itemId,idx,duration,magnitude,isup from iticschemeitem it, (select a, b from  model_ins_rel_search_multi_v2('iticscheme',ID_LIST(),'{}','iticschemeitem')) tb where it.id = tb.b
        ]]>
    </select>

    <insert id="insertSemiScheme" parameterType="com.cet.pqscheduleservice.model.event.PqVariationEventSemiInfo" databaseId="oracle">
        <trim prefix="begin" suffix="; end;">
        <foreach collection="pqVariationEventSemiInfos" index="index" item="item"
                 separator=";">
            <![CDATA[
              merge into pqvariationeventsemiinfo a
                  using
                  (
                    select 1 from dual
                  )b
                  on (a.pqvariationevent_id = #{item.pqvariationeventId} and a.semischeme_id = #{item.semischemeId})
                  when matched then update set
                    a.toleranceband = 4
                  when not matched then
                    insert (id,pqvariationevent_id, semischeme_id, toleranceband)
                           values (pqvariationeventsemiinfo_SEQ.nextval,#{item.pqvariationeventId},#{item.semischemeId},#{item.toleranceband})
        ]]>
        </foreach>
        </trim>
    </insert>

    <insert id="insertIticScheme" parameterType="com.cet.pqscheduleservice.model.event.PqVariationEventItic" databaseId="oracle">
        <trim prefix="begin" suffix="; end;">
        <foreach collection="pqVariationEventItics" index="index" item="item"
                 separator=";">
            <![CDATA[
             merge into pqvariationeventitic a
                  using
                  (
                    select 1 from dual
                  )b
                  on (a.pqvariationevent_id = #{item.pqvariationeventId} and a.iticscheme_id = #{item.iticschemeId})
                  when matched then update set
                    a.toleranceband = 4
                  when not matched then
                    insert (id,pqvariationevent_id, iticscheme_id, toleranceband)
                           values (pqvariationeventitic_SEQ.nextval,#{item.pqvariationeventId},#{item.iticschemeId},#{item.toleranceband})
        ]]>
        </foreach>
        </trim>
    </insert>

    <update id="updateSchemePoi" databaseId="oracle">
        <![CDATA[update PDI_TRANSEVENTPOI set eventid =${count} ,updatetime = date2utc(LOCALTIMESTAMP)  where transtype = 'pqvariationevent'
        ]]>
    </update>

    <select id="queryEvent" resultMap="pqEventVariationMap" databaseId="oracle">
         <![CDATA[
        select id,srcdeviceid,duration,magnitude,v1magnitude,v2magnitude,v3magnitude,eventtime,monitoredid from pqvariationevent  where eventtime >= ${startTime} and eventtime < ${endTime}
        ]]>
    </select>

    <update id="updateEventCauseAnalysis" parameterType="com.cet.pqscheduleservice.model.event.EventAnalysisDTO" databaseId="oracle">
        <foreach collection="pqEventVariations" index="index" item="item" separator=";">
            <![CDATA[UPDATE pqvariationevent SET reason = #{item.reason} WHERE id = #{item.id}]]>
        </foreach>
    </update>

    <insert id="insertDeviceEventList" parameterType="com.cet.pqscheduleservice.model.event.DeviceEvent" databaseId="oracle">
        <trim prefix="begin" suffix="; end;">
            <foreach collection="deviceEventList" index="index" item="item"
                     separator=";">
                <![CDATA[
             merge into deviceevent a
              using
              (
                select 1 from dual
              )b
              on (
                  a.monitoredlabel = #{item.monitoredlabel} and
                  a.monitoredid = #{item.monitoredid} and
                  a.eventtime = #{item.eventtime} and
                  a.stationid = #{item.stationid} and
                  a.deviceid = #{item.deviceid}
              )
              when matched then update set
                a.magnitude = #{item.magnitude},
                a.duration = #{item.duration},
                a.maxa = #{item.maxa},
                a.mina = #{item.mina},
                a.maxb = #{item.maxb},
                a.minb = #{item.minb},
                a.maxc = #{item.maxc},
                a.minc = #{item.minc},
                a.toleranceband = #{item.toleranceband},
                a.phase = #{item.phase},
                a.description = #{item.description},
                a.updatetime = #{item.updatetime}
              when not matched then
                insert (id,,deviceid,monitoredlabel, monitoredid,stationid,eventtypename,eventtime,duration,magnitude,maxa,maxb,maxc,mina,minb,minc,description,phase,updatetime)
                       values (deviceevent_SEQ.nextval,#{item.deviceid},#{item.monitoredlabel},#{item.monitoredid},#{item.stationid},#{item.eventtypename},
                               #{item.eventtime},#{item.duration},#{item.magnitude},#{item.maxa},#{item.maxb},#{item.maxc},
                               #{item.mina},#{item.minb},#{item.minc},#{item.toleranceband},#{item.description},#{item.phase},#{item.updatetime})
        ]]>
            </foreach>
        </trim>
    </insert>
</mapper>
