<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.matterhorn.PqHistoryStatusMapper">

    <resultMap type="com.cet.pqscheduleservice.model.status.PqStatus" id="pqStatusMap_dm">
        <result column="logtime" property="logtime" javaType="java.lang.Long"/>
        <result column="deviceid" property="deviceId" javaType="java.lang.Long"/>
        <result column="monitoredid" property="monitoredId" javaType="java.lang.Long"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.status.PqMonitorStatus" id="pqMonitorStatus_dm">
        <result column="status" property="status" javaType="java.lang.Integer"/>
        <result column="id" property="monitoredId" javaType="java.lang.Long"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.status.PqHistoryStatus" id="pqHistoryStatusMap_dm">
        <result column="aggregationcycle" property="aggregationcycle" javaType="java.lang.Integer"/>
        <result column="pqmonitor_id" property="pqmonitor_id" javaType="java.lang.Long"/>
        <result column="monitorstatus" property="monitorstatus" javaType="java.lang.Integer"/>
        <result column="logtime" property="logtime" javaType="java.lang.Long"/>
    </resultMap>

    <select id="queryPqStatus" resultMap="pqStatusMap_dm" databaseId="dm">
        <![CDATA[
                    select distinct a.logtime, a.deviceid,a.monitoredid from (select distinct p.logtime,m.measuredby as deviceid,m.monitoredid from pqquantityaggdata p,measuredby m where p.dataid = ${dataId} and p.aggregationcycle=12
                    and p.line_id = m.monitoredid)a, (select max(logtime) logtime,pqmonitor_id,monitorstatus from pqhistorystatus where aggregationcycle=12 group by pqmonitor_id,monitorstatus)b where  a.monitoredid = b.pqmonitor_id
                     and (a.logtime > b.logtime or (a.logtime = b.logtime and b.monitorstatus=5))order by a.logtime desc]]>
    </select>

    <select id="queryHistoryStatusCount" resultType="java.lang.Long" databaseId="dm">
        <![CDATA[
                    select count(1) from pqhistorystatus
        ]]>
    </select>

    <select id="queryAllPqStatus" resultMap="pqStatusMap_dm" databaseId="dm">
        <![CDATA[
                    select max(p.logtime) as logtime,m.measuredby as deviceid,m.monitoredid from pqquantityaggdata p,measuredby m
                            where p.dataid = ${dataId} and p.aggregationcycle=12  and p.line_id = m.monitoredid group by measuredby,monitoredid
        ]]>
    </select>

    <select id="queryMonitorStatus" resultMap="pqMonitorStatus_dm" databaseId="dm">
        <![CDATA[select id,status from line ]]>
    </select>

    <select id="queryHistoryStatus" resultMap="pqHistoryStatusMap_dm" databaseId="dm">
        <![CDATA[select aggregationcycle,pqmonitor_id,monitorstatus,logtime from pqhistorystatus ]]>
        <where>
            <foreach collection="pqHistoryStatuses" index="index" item="item"
                     separator="or">
                aggregationcycle = #{item.aggregationcycle} and logtime = #{item.logtime} and pqmonitor_id = #{item.pqmonitor_id}
            </foreach>
        </where>
    </select>

    <insert id="insertPqHistoryStatus" parameterType="com.cet.pqscheduleservice.model.status.PqHistoryStatus" databaseId="dm">
        <trim prefix="begin" suffix="; end;">
            <foreach collection="pqHistoryStatuses" index="index" item="item"
                     separator=";">
                <![CDATA[
              merge into pqhistorystatus a using (select 1 from dual)b on (a.pqmonitor_id = #{item.pqmonitor_id} and a.aggregationcycle = #{item.aggregationcycle} and a.logtime=#{item.logtime})
              when matched then update set a.monitorstatus = #{item.monitorstatus}, a.updatetime = #{item.updatetime}, a.realtimestatus = #{item.realtimestatus} when not matched then
              insert (pqmonitor_id,aggregationcycle,logtime,monitorstatus,realtimestatus,updatetime) values (#{item.pqmonitor_id},#{item.aggregationcycle},#{item.logtime},#{item.monitorstatus},#{item.realtimestatus},#{item.updatetime})
        ]]>
            </foreach>
        </trim>
    </insert>

    <update id="updateLineStatus" parameterType="com.cet.pqscheduleservice.model.status.PqHistoryStatus" databaseId="dm">
        <trim prefix="begin" suffix="; end;">
            <foreach collection="pqHistoryStatuses" index="index" item="item"
                     separator=";">
                <![CDATA[update line set status = #{item.monitorstatus} where id = #{item.pqmonitor_id} and status not in (2,3,4)
        ]]>
            </foreach>
        </trim>
    </update>

    <resultMap type="com.cet.pqscheduleservice.model.line.LineVO" id="lineMap_dm">
        <result column="id" property="id" javaType="java.lang.Long"/>
        <result column="monitortype" property="monitortype" javaType="java.lang.Integer"/>
        <result column="lotconfig" property="lotconfig" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectOfflineMonitorsLast5Days" resultType="java.lang.Long" databaseId="dm">
        SELECT pqmonitor_id
        FROM pqhistorystatus
        WHERE logtime >= (CAST(SYSDATE - 5 AS TIMESTAMP) - TO_DATE('1970-01-01','YYYY-MM-DD')) * 86400000
        AND monitorstatus != 1
        AND pqmonitor_id IN
        <foreach collection="pqmonitorIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY pqmonitor_id
        HAVING COUNT(*) = 5
    </select>

    <update id="updatePqHistoryStatus" databaseId="dm">
        UPDATE line
        SET status = #{status}
        WHERE id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="queryMeterInfo" resultMap="lineMap_dm" databaseId="dm">
        <![CDATA[select id,monitortype,lotconfig from line where monitortype in(5,6)]]>
    </select>

</mapper>
