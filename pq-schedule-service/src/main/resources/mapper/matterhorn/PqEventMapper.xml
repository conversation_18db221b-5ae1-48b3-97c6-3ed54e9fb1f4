<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper">

    <resultMap type="com.cet.pqscheduleservice.model.event.Measuredby" id="measuredbyMap">
        <result column="measuredby" property="measuredBy" javaType="java.lang.Long"/>
        <result column="monitoredid" property="monitoredId" javaType="java.lang.Long"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.PqEventVariation" id="pqEventVariationMap">
        <result column="id" property="id" javaType="java.lang.Long"/>
        <result column="srcdeviceid" property="deviceid" javaType="java.lang.Long"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.SemiScheme" id="semiSchemeMap">
        <result column="semiScheme_id" property="semiScheme_id" javaType="java.lang.Integer"/>
        <result column="itemid" property="itemId" javaType="java.lang.Integer"/>
        <result column="idx" property="idx" javaType="java.lang.Integer"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.IticScheme" id="iticSchemeMap">
        <result column="iticScheme_id" property="iticScheme_id" javaType="java.lang.Integer"/>
        <result column="itemId" property="itemId" javaType="java.lang.Integer"/>
        <result column="idx" property="idx" javaType="java.lang.Integer"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
        <result column="isup" property="isup" javaType="java.lang.Boolean"/>
    </resultMap>

    <select id="queryEventStartId" resultType="java.lang.Long" databaseId="postgresql">
         <![CDATA[
                select eventid from PDI_TRANSEVENTPOI where transtype = 'transient' and year = ${year}
        ]]>
    </select>

    <select id="queryMeasureby" resultMap="measuredbyMap" databaseId="postgresql">
         <![CDATA[
                select measuredby,monitoredid  from Measuredby
        ]]>
    </select>

    <insert id="insertPqEventVariation" parameterType="com.cet.pqscheduleservice.model.event.PqEventVariation" databaseId="postgresql">
        <foreach collection="pqEventTransients" index="index" item="item"
                 separator="">
            <![CDATA[insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,
                        v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values
                        (#{item.monitoredlabel},#{item.monitoredid},#{item.deviceid},#{item.eventtime},#{item.pqvariationeventtype},#{item.nominalvoltage},#{item.duration},#{item.v1duration},#{item.v2duration},#{item.v3duration},#{item.magnitude},#{item.v1magnitude},#{item.v2magnitude},
                        #{item.v3magnitude},#{item.v1trigger},#{item.v2trigger},#{item.v3trigger},#{item.waveformlogtime},#{item.valid},#{item.description},#{item.confirmeventstatus},#{item.remark},#{item.updatetime},#{item.transientfaultdirection},#{item.toleranceband},#{item.operator},#{item.isminuteextreme},#{item.lossenergy},#{item.escapestatus},#{item.reason},#{item.faultreason})
            on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do
          UPDATE SET
             magnitude = excluded.magnitude,
             escapestatus = excluded.escapestatus,
             faultreason = excluded.faultreason,
             reason = excluded.reason,
             duration = excluded.duration;
        ]]>
        </foreach>
    </insert>

    <update id="updatePqEventPoi" databaseId="postgresql">
        <![CDATA[update PDI_TRANSEVENTPOI set eventid =${count} ,updatetime = date2utc(LOCALTIMESTAMP)  where transtype = 'transient' and year = ${year}
        ]]>
    </update>

    <select id="queryToleranceEvent" resultMap="pqEventVariationMap" databaseId="postgresql">
         <![CDATA[
               select id,srcdeviceid,duration,magnitude from pqvariationevent  where id > (select max(pqvariationevent_id) from pqvariationeventsemiinfo)
        ]]>
    </select>

    <select id="querySemiScheme" resultMap="semiSchemeMap" databaseId="postgresql">
         <![CDATA[
               select tb.a as semiScheme_id,id as itemId,idx,duration,magnitude from semischemeitem it, (select a, b from  model_instance_relationship_search_multi_v2('semischeme','{}','{}','{semischemeitem}')) tb where it.id = tb.b
        ]]>
    </select>

    <select id="queryIticScheme" resultMap="iticSchemeMap" databaseId="postgresql">
         <![CDATA[
               select tb.a as iticScheme_id,id as itemId,idx,duration,magnitude,isup from iticschemeitem it, (select a, b from  model_instance_relationship_search_multi_v2('iticscheme','{}','{}','{iticschemeitem}')) tb where it.id = tb.b
        ]]>
    </select>

    <insert id="insertSemiScheme" parameterType="com.cet.pqscheduleservice.model.event.PqVariationEventSemiInfo" databaseId="postgresql">
        <foreach collection="pqVariationEventSemiInfos" index="index" item="item"
                 separator="">
            <![CDATA[INSERT INTO pqvariationeventsemiinfo (pqvariationevent_id, semischeme_id, toleranceband) VALUES (#{item.pqvariationeventId},#{item.semischemeId},#{item.toleranceband})
                 on conflict(pqvariationevent_id,semischeme_id) do
          UPDATE SET
             toleranceband = excluded.toleranceband;
        ]]>
        </foreach>
    </insert>

    <insert id="insertIticScheme" parameterType="com.cet.pqscheduleservice.model.event.PqVariationEventItic" databaseId="postgresql">
        <foreach collection="pqVariationEventItics" index="index" item="item"
                 separator="">
            <![CDATA[INSERT INTO pqvariationeventitic (pqvariationevent_id, iticscheme_id, toleranceband) VALUES (#{item.pqvariationeventId},#{item.iticschemeId},#{item.toleranceband})
                 on conflict(pqvariationevent_id,iticscheme_id) do
          UPDATE SET
             toleranceband = excluded.toleranceband;
        ]]>
        </foreach>
    </insert>

    <update id="updateSchemePoi" databaseId="postgresql">
        <![CDATA[update PDI_TRANSEVENTPOI set eventid =${count} ,updatetime = date2utc(LOCALTIMESTAMP)  where transtype = 'pqvariationevent'
        ]]>
    </update>

    <select id="queryEvent" resultMap="pqEventVariationMap" databaseId="postgresql">
         <![CDATA[
        select id,srcdeviceid,duration,magnitude,v1magnitude,v2magnitude,v3magnitude,eventtime,monitoredid from pqvariationevent  where eventtime >= ${startTime} and eventtime < ${endTime}
        ]]>
    </select>

    <update id="updateEventCauseAnalysis" parameterType="com.cet.pqscheduleservice.model.event.EventAnalysisDTO" databaseId="postgresql">
        <foreach collection="pqEventVariations" index="index" item="item" separator=";">
            <![CDATA[UPDATE pqvariationevent SET reason = #{item.reason},updatetime = date2utc(LOCALTIMESTAMP) WHERE id = #{item.id}]]>
        </foreach>
    </update>

    <insert id="insertDeviceEventList" parameterType="com.cet.pqscheduleservice.model.event.DeviceEvent" databaseId="postgresql">
        <foreach collection="deviceEventList" index="index" item="item"
                 separator="">
            <![CDATA[insert into deviceevent (deviceid,monitoredlabel, monitoredid,stationid,eventtypename,eventtime,duration,magnitude,maxa,maxb,maxc,mina,minb,minc,toleranceband,description,phase,updatetime) values
                        (#{item.deviceid},#{item.monitoredlabel},#{item.monitoredid},#{item.stationid},#{item.eventtypename},
                               #{item.eventtime},#{item.duration},#{item.magnitude},#{item.maxa},#{item.maxb},#{item.maxc},
                               #{item.mina},#{item.minb},#{item.minc},#{item.toleranceband},#{item.description},#{item.phase},#{item.updatetime})
            on conflict(monitoredlabel,monitoredid,eventtime,stationid,deviceid) do
          UPDATE SET
             magnitude = excluded.magnitude ,
             duration = excluded.duration ,
             maxa = excluded.maxa,
             mina = excluded.mina,
             maxb = excluded.maxb,
             minb = excluded.minb,
             maxc = excluded.maxc,
             minc = excluded.minc,
             toleranceband = excluded.toleranceband,
             eventtypename = excluded.eventtypename,
             phase =  excluded.phase,
             description = excluded.description ,
             updatetime = excluded.updatetime ;
        ]]>
        </foreach>
    </insert>
</mapper>
