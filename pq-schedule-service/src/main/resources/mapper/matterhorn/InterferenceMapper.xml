<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.matterhorn.InterferenceMapper">

    <select id="getWindLines" resultType="java.lang.Long" databaseId="postgresql">
        <![CDATA[
        select bid from model_instance_relationship  where atype = 'windpowerstation' and btype = 'line'
        ]]>
    </select>

    <select id="getPhotoLines" resultType="java.lang.Long" databaseId="postgresql">
        <![CDATA[
        select bid from model_instance_relationship  where atype = 'photovoltaicstation' and btype = 'line' and aid in (select id from photovoltaicstation where voltagegrade in
        ]]>
        <foreach collection="voltagegrades" index="index" item="id" open="(" close=")" separator=",">
        #{id}
        </foreach>
        )
    </select>

</mapper>