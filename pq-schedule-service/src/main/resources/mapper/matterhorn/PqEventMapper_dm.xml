<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper">

    <resultMap type="com.cet.pqscheduleservice.model.event.Measuredby" id="measuredbyMap_dm">
        <result column="measuredby" property="measuredBy" javaType="java.lang.Long"/>
        <result column="monitoredid" property="monitoredId" javaType="java.lang.Long"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.PqEventVariation" id="pqEventVariationMap_dm">
        <result column="id" property="id" javaType="java.lang.Long"/>
        <result column="srcdeviceid" property="deviceid" javaType="java.lang.Long"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.SemiScheme" id="semiSchemeMap_dm">
        <result column="semiScheme_id" property="semiScheme_id" javaType="java.lang.Integer"/>
        <result column="itemid" property="itemId" javaType="java.lang.Integer"/>
        <result column="idx" property="idx" javaType="java.lang.Integer"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
    </resultMap>

    <resultMap type="com.cet.pqscheduleservice.model.event.IticScheme" id="iticSchemeMap_dm">
        <result column="iticScheme_id" property="iticScheme_id" javaType="java.lang.Integer"/>
        <result column="itemId" property="itemId" javaType="java.lang.Integer"/>
        <result column="idx" property="idx" javaType="java.lang.Integer"/>
        <result column="magnitude" property="magnitude" javaType="java.lang.Double"/>
        <result column="duration" property="duration" javaType="java.lang.Long"/>
        <result column="isup" property="isup" javaType="java.lang.Boolean"/>
    </resultMap>

    <select id="queryEventStartId" resultType="java.lang.Long" databaseId="dm">
         <![CDATA[
                select eventid from PDI_TRANSEVENTPOI where transtype = 'transient' and year = ${year}
        ]]>
    </select>

    <select id="queryMeasureby" resultMap="measuredbyMap_dm" databaseId="dm">
         <![CDATA[
                select measuredby,monitoredid  from Measuredby
        ]]>
    </select>

    <insert id="insertPqEventVariation" parameterType="com.cet.pqscheduleservice.model.event.PqEventVariation" databaseId="dm">
        <trim prefix="begin" suffix="; end;">
        <foreach collection="pqEventTransients" index="index" item="item"
                 separator=";">
            <![CDATA[
             merge into pqvariationevent a
              using
              (
                select 1 from dual
              )b
              on (a.monitoredlabel = #{item.monitoredlabel} and a.monitoredid = #{item.monitoredid} and a.eventtime=#{item.eventtime} and a.pqvariationeventtype=#{item.pqvariationeventtype}  and a.v1trigger=#{item.v1trigger}
              and a.v2trigger=#{item.v2trigger} and a.v3trigger=#{item.v3trigger})
              when matched then update set
                a.magnitude = #{item.magnitude},
                a.duration = #{item.duration},
                a.faultreason = #{item.faultreason}
              when not matched then
                insert (id,monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,
                                    v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy, escapestatus, reason, faultreason)
                       values ((SELECT NVL(MAX(id),0)+1 FROM pqvariationevent), #{item.monitoredlabel},#{item.monitoredid},#{item.deviceid},#{item.eventtime},#{item.pqvariationeventtype},#{item.nominalvoltage},#{item.duration},#{item.v1duration},
                               #{item.v2duration},#{item.v3duration},#{item.magnitude},#{item.v1magnitude},#{item.v2magnitude},#{item.v3magnitude},#{item.v1trigger},#{item.v2trigger},#{item.v3trigger},#{item.waveformlogtime},#{item.valid},#{item.description},#{item.confirmeventstatus},#{item.remark},#{item.updatetime},#{item.transientfaultdirection},#{item.toleranceband},#{item.operator},#{item.isminuteextreme},#{item.lossenergy},#{item.escapestatus},#{item.reason},#{item.faultreason})
        ]]>
        </foreach>
        </trim>
    </insert>

    <update id="updatePqEventPoi" databaseId="dm">
        <![CDATA[update PDI_TRANSEVENTPOI set eventid =${count} ,updatetime =  CAST(
            (SYSDATE - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')
                ) * 86400000 +
            TO_NUMBER(TO_CHAR(SYSTIMESTAMP, 'FF3')) AS BIGINT)  where transtype = 'transient' and year = ${year}
        ]]>
    </update>

    <select id="queryToleranceEvent" resultMap="pqEventVariationMap_dm" databaseId="dm">
         <![CDATA[
               select id,srcdeviceid,duration,magnitude from pqvariationevent  where id > (select max(pqvariationevent_id) from pqvariationeventsemiinfo)
        ]]>
    </select>

    <select id="querySemiScheme" resultMap="semiSchemeMap_dm" databaseId="dm">
         <![CDATA[
        select mr.aid as semiScheme_id,id as itemId,idx,duration,magnitude from semischemeitem it
        left join (select aid,bid from model_instance_relationship where  atype='semischeme' and btype= 'semischemeitem') mr
        on it.id = mr.bid;
        ]]>
    </select>

    <select id="queryIticScheme" resultMap="iticSchemeMap_dm" databaseId="dm">
         <![CDATA[
        select mr.aid as iticScheme_id,id as itemId,idx,duration,magnitude,isup from iticschemeitem it
        left join
        (select aid,bid from model_instance_relationship where  atype='iticscheme' and btype= 'iticschemeitem') mr
        on it.id = mr.bid;
        ]]>
    </select>

    <insert id="insertSemiScheme" parameterType="com.cet.pqscheduleservice.model.event.PqVariationEventSemiInfo" databaseId="dm">
        <trim prefix="begin" suffix="; end;">
        <foreach collection="pqVariationEventSemiInfos" index="index" item="item"
                 separator=";">
            <![CDATA[
              merge into pqvariationeventsemiinfo a
                  using
                  (
                    select 1 from dual
                  )b
                  on (a.pqvariationevent_id = #{item.pqvariationeventId} and a.semischeme_id = #{item.semischemeId})
                  when matched then update set
                    a.toleranceband = 4
                  when not matched then
                    insert (pqvariationevent_id, semischeme_id, toleranceband)
                           values (#{item.pqvariationeventId},#{item.semischemeId},#{item.toleranceband})
        ]]>
        </foreach>
        </trim>
    </insert>

    <insert id="insertIticScheme" parameterType="com.cet.pqscheduleservice.model.event.PqVariationEventItic" databaseId="dm">
        <trim prefix="begin" suffix="; end;">
        <foreach collection="pqVariationEventItics" index="index" item="item"
                 separator=";">
            <![CDATA[
             merge into pqvariationeventitic a
                  using
                  (
                    select 1 from dual
                  )b
                  on (a.pqvariationevent_id = #{item.pqvariationeventId} and a.iticscheme_id = #{item.iticschemeId})
                  when matched then update set
                    a.toleranceband = 4
                  when not matched then
                    insert (pqvariationevent_id, iticscheme_id, toleranceband)
                           values ( #{item.pqvariationeventId},#{item.iticschemeId},#{item.toleranceband})
        ]]>
        </foreach>
        </trim>
    </insert>

    <update id="updateSchemePoi" databaseId="dm">
        <![CDATA[update PDI_TRANSEVENTPOI set eventid =${count} ,updatetime = CAST(
            (SYSDATE - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')
                ) * 86400000 +
            TO_NUMBER(TO_CHAR(SYSTIMESTAMP, 'FF3')) AS BIGINT)  where transtype = 'pqvariationevent'
        ]]>
    </update>

    <select id="queryEvent" resultMap="pqEventVariationMap" databaseId="dm">
         <![CDATA[
        select id,srcdeviceid,duration,magnitude,v1magnitude,v2magnitude,v3magnitude,eventtime,monitoredid from pqvariationevent  where eventtime >= ${startTime} and eventtime < ${endTime}
        ]]>
    </select>

    <update id="updateEventCauseAnalysis" parameterType="com.cet.pqscheduleservice.model.event.EventAnalysisDTO" databaseId="dm">
        <foreach collection="pqEventVariations" index="index" item="item" separator=";">
            <![CDATA[UPDATE pqvariationevent SET reason = #{item.reason} WHERE id = #{item.id}]]>
        </foreach>
    </update>

    <insert id="insertDeviceEventList" parameterType="com.cet.pqscheduleservice.model.event.DeviceEvent" databaseId="dm">
        <foreach collection="deviceEventList" index="index" item="item" separator="">
            <![CDATA[
        MERGE INTO deviceevent t
        USING (SELECT
               #{item.deviceid} as deviceid,
               #{item.monitoredlabel} as monitoredlabel,
               #{item.monitoredid} as monitoredid,
               #{item.stationid} as stationid,
               #{item.eventtime} as eventtime,
               #{item.eventtypename} as eventtypename,
               #{item.duration} as duration,
               #{item.magnitude} as magnitude,
               #{item.maxa} as maxa,
               #{item.maxb} as maxb,
               #{item.maxc} as maxc,
               #{item.mina} as mina,
               #{item.minb} as minb,
               #{item.minc} as minc,
               #{item.toleranceband} as toleranceband,
               #{item.description} as description,
               #{item.phase} as phase,
               #{item.updatetime} as updatetime
              FROM dual) s
        ON (t.monitoredlabel = s.monitoredlabel
            AND t.monitoredid = s.monitoredid
            AND t.eventtime = s.eventtime
            AND t.stationid = s.stationid
            AND t.deviceid = s.deviceid)
        WHEN MATCHED THEN
            UPDATE SET
                t.magnitude = s.magnitude,
                t.duration = s.duration,
                t.maxa = s.maxa,
                t.mina = s.mina,
                t.maxb = s.maxb,
                t.minb = s.minb,
                t.maxc = s.maxc,
                t.minc = s.minc,
                t.toleranceband = s.toleranceband,
                t.eventtypename = s.eventtypename,
                t.phase = s.phase,
                t.description = s.description,
                t.updatetime = s.updatetime
        WHEN NOT MATCHED THEN
            INSERT (deviceid, monitoredlabel, monitoredid, stationid, eventtypename,
                    eventtime, duration, magnitude, maxa, maxb, maxc,
                    mina, minb, minc, toleranceband, description, phase, updatetime)
            VALUES (s.deviceid, s.monitoredlabel, s.monitoredid, s.stationid, s.eventtypename,
                    s.eventtime, s.duration, s.magnitude, s.maxa, s.maxb, s.maxc,
                    s.mina, s.minb, s.minc, s.toleranceband, s.description, s.phase, s.updatetime);
        ]]>
        </foreach>
    </insert>
</mapper>
