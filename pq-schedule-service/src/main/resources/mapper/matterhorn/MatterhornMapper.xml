<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cet.pqscheduleservice.mapper.matterhorn.MatterhornMapper">


    <select id="callCreat_pqoverlimitdata">
        <![CDATA[
                select creat_pqoverlimitdata(cast(extract(year from LOCALTIMESTAMP) as integer));
                select creat_pqoverlimitdata(cast(extract(year from LOCALTIMESTAMP) as integer)+1);
                select creat_pqoverlimitdata(cast(extract(year from LOCALTIMESTAMP) as integer)-1);
        ]]>
    </select>

    <select id="callCreat_pqquantityaggdata">
        <![CDATA[
                select creat_pqquantityaggdata(cast(extract(year from LOCALTIMESTAMP) as integer));
                select creat_pqquantityaggdata(cast(extract(year from LOCALTIMESTAMP) as integer)+1);
                select creat_pqquantityaggdata(cast(extract(year from LOCALTIMESTAMP) as integer)-1);
        ]]>
    </select>

    <select id="callS_PQSTABLE1_1">
        <![CDATA[
                call S_PQSTABLE1_1();
        ]]>
    </select>

    <select id="callS_PQSTABLE1_2">
        <![CDATA[
                call S_PQSTABLE1_2();
        ]]>
    </select>

    <select id="callS_PQSTABLE1_3">
        <![CDATA[
                call S_PQSTABLE1_3();
        ]]>
    </select>

    <select id="callS_PQSTABLE1_4">
        <![CDATA[
                call S_PQSTABLE1_4();
        ]]>
    </select>

    <select id="callS_PQSTABLE2">
        <![CDATA[
                call S_PQSTABLE2();
        ]]>
    </select>

    <select id="callS_PQRMS">
        <![CDATA[
                call S_PQRMS();
        ]]>
    </select>

    <select id="callS_PQSTABLE_LASTMONTH">
        <![CDATA[
                call S_PQSTABLE_LASTMONTH();
        ]]>
    </select>

    <select id="callS_PQSTABLE1_1_TIME">
        <![CDATA[
                call s_pqstable1_1_time();
        ]]>
    </select>

    <select id="callS_PQSTABLE1_2_TIME">
        <![CDATA[
                call s_pqstable1_2_time();
        ]]>
    </select>

    <select id="callS_PQSTABLE1_3_TIME">
        <![CDATA[
                call s_pqstable1_3_time();
        ]]>
    </select>

    <select id="callS_PQSTABLE1_4_TIME">
        <![CDATA[
                call s_pqstable1_4_time();
        ]]>
    </select>

    <select id="callS_PQSTABLE2_1_TIME">
        <![CDATA[
                call s_pqstable2_1_time();
        ]]>
    </select>

    <select id="callS_PQSTABLE2_2_TIME">
        <![CDATA[
                call s_pqstable2_2_time();
        ]]>
    </select>

    <select id="callS_PQSTABLE2_3_TIME">
        <![CDATA[
                call s_pqstable2_3_time();
        ]]>
    </select>

    <select id="callS_PQSTABLE2_4_TIME">
        <![CDATA[
                call s_pqstable2_4_time();
        ]]>
    </select>

    <select id="callADDPQPARTITIONS">
        <![CDATA[
                call c_addpqpartitions();
        ]]>
    </select>

    <delete id="deletePqStatLog">
        <![CDATA[
                delete from pqstatlog where logtime < to_date(#{date},'yyyy-mm-dd');
        ]]>
    </delete>

    <select id="queryLineDevice" resultType="com.cet.pqscheduleservice.model.event.Measuredby" >
        <![CDATA[
                select measuredBy,monitoredId from measuredby where monitoredlabel = 'line'
        ]]>
    </select>

</mapper>
