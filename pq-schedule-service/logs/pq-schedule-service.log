2025-08-01 00:00:00.003 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 00:00:00.039 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 01:00:00.009 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 01:00:00.010 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 02:00:00.012 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 02:00:00.012 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 03:00:00.008 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 03:00:00.020 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 04:00:00.002 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 04:00:00.002 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 05:00:00.004 - INFO - [ProcedureTask.java:50] : start exec procedure.
2025-08-01 05:00:00.004 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 05:00:00.004 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 05:00:00.004 - INFO - [ProcedureTask.java:52] : end exec procedure.
2025-08-01 05:00:00.005 - INFO - [ProcedureServiceImpl.java:28] : start call S_PQRMS
2025-08-01 05:00:17.746 - INFO - [ProcedureServiceImpl.java:30] : end call S_PQRMS
2025-08-01 05:00:17.746 - INFO - [ProcedureServiceImpl.java:36] : start call S_PQSTABLE2
2025-08-01 05:00:18.680 - INFO - [ProcedureServiceImpl.java:38] : end call S_PQSTABLE2
2025-08-01 05:00:18.680 - INFO - [ProcedureServiceImpl.java:44] : start call S_PQSTABLE1_1
2025-08-01 05:00:18.929 - INFO - [ProcedureServiceImpl.java:46] : end call S_PQSTABLE1_1
2025-08-01 05:00:18.929 - INFO - [ProcedureServiceImpl.java:53] : start call S_PQSTABLE1_2
2025-08-01 05:00:19.374 - INFO - [ProcedureServiceImpl.java:55] : end call S_PQSTABLE1_2
2025-08-01 05:00:19.375 - INFO - [ProcedureServiceImpl.java:61] : start call S_PQSTABLE1_3
2025-08-01 05:00:20.127 - INFO - [ProcedureServiceImpl.java:63] : end call S_PQSTABLE1_3
2025-08-01 05:00:20.128 - INFO - [ProcedureServiceImpl.java:69] : start call S_PQSTABLE1_4
2025-08-01 05:02:46.623 - INFO - [ProcedureServiceImpl.java:71] : end call S_PQSTABLE1_4
2025-08-01 06:00:00.014 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 06:00:00.014 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 07:00:00.006 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 07:00:00.006 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 08:00:00.015 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 08:00:00.015 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 09:00:00.004 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-08-01 09:00:00.005 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-08-01 09:06:04.141 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-08-01 09:06:15.083 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 13.343 seconds (JVM running for 14.888)
