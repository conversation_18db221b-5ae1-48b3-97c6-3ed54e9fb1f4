2025-07-31 00:00:00.012 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 00:00:00.051 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 00:30:00.014 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 00:30:00.015 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 00:30:00.015 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 00:30:19.771 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 00:30:25.222 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 00:30:25.223 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 00:50:00.014 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 00:50:00.014 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 01:00:00.012 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 01:00:00.012 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 01:30:00.004 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 01:30:00.005 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 01:30:00.005 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 01:30:00.349 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 01:30:05.336 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 01:30:05.337 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 01:50:00.013 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 01:50:00.013 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 02:00:00.012 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 02:00:00.012 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 02:30:00.010 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 02:30:00.010 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 02:30:00.010 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 02:30:00.355 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 02:30:05.205 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 02:30:05.205 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 02:50:00.007 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 02:50:00.007 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 03:00:00.004 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 03:00:00.004 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 03:30:00.008 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 03:30:00.008 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 03:30:00.008 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 03:30:00.398 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 03:30:05.139 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 03:30:05.139 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 03:50:00.005 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 03:50:00.006 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 04:00:00.011 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 04:00:00.011 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 04:30:00.015 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 04:30:00.015 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 04:30:00.015 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 04:30:19.720 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 04:30:24.374 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 04:30:24.375 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 04:50:00.009 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 04:50:00.009 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 05:00:00.001 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 05:00:00.001 - INFO - [ProcedureTask.java:44] : start exec procedure.
2025-07-31 05:00:00.001 - INFO - [ProcedureTask.java:106] : start deal event cause analysis.
2025-07-31 05:00:00.001 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 05:00:00.003 - INFO - [ProcedureTask.java:108] : end deal event cause analysis.
2025-07-31 05:00:00.003 - INFO - [ProcedureTask.java:46] : end exec procedure.
2025-07-31 05:00:00.003 - INFO - [ProcedureServiceImpl.java:28] : start call S_PQRMS
2025-07-31 05:00:00.218 - INFO - [PqEventServiceImpl.java:258] : event cause analysis add 0.
2025-07-31 05:00:17.708 - INFO - [ProcedureServiceImpl.java:30] : end call S_PQRMS
2025-07-31 05:00:17.708 - INFO - [ProcedureServiceImpl.java:36] : start call S_PQSTABLE2
2025-07-31 05:00:18.611 - INFO - [ProcedureServiceImpl.java:38] : end call S_PQSTABLE2
2025-07-31 05:00:18.611 - INFO - [ProcedureServiceImpl.java:44] : start call S_PQSTABLE1_1
2025-07-31 05:00:18.782 - INFO - [ProcedureServiceImpl.java:46] : end call S_PQSTABLE1_1
2025-07-31 05:00:18.782 - INFO - [ProcedureServiceImpl.java:53] : start call S_PQSTABLE1_2
2025-07-31 05:00:19.049 - INFO - [ProcedureServiceImpl.java:55] : end call S_PQSTABLE1_2
2025-07-31 05:00:19.049 - INFO - [ProcedureServiceImpl.java:61] : start call S_PQSTABLE1_3
2025-07-31 05:00:20.925 - INFO - [ProcedureServiceImpl.java:63] : end call S_PQSTABLE1_3
2025-07-31 05:00:20.925 - INFO - [ProcedureServiceImpl.java:69] : start call S_PQSTABLE1_4
2025-07-31 05:02:37.513 - INFO - [ProcedureServiceImpl.java:71] : end call S_PQSTABLE1_4
2025-07-31 05:30:00.005 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 05:30:00.005 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 05:30:00.005 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 05:30:00.311 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 05:30:04.977 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 05:30:04.977 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 05:50:00.012 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 05:50:00.012 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 06:00:00.013 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 06:00:00.017 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 06:30:00.004 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 06:30:00.004 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 06:30:00.004 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 06:30:19.708 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 06:30:24.435 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 06:30:24.435 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 06:50:00.006 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 06:50:00.007 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 07:00:00.008 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 07:00:00.008 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 07:30:00.005 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 07:30:00.005 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 07:30:00.005 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 07:30:19.755 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 07:30:24.658 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 07:30:24.658 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 07:50:00.002 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 07:50:00.002 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 08:00:00.003 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 08:00:00.003 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 08:30:00.011 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 08:30:00.011 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 08:30:00.011 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 08:30:19.708 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 08:30:25.102 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 08:30:25.103 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 08:50:00.009 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 08:50:00.009 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 09:00:00.011 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 09:00:00.011 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 09:30:00.011 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 09:30:00.012 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 09:30:00.013 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 09:30:19.747 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 09:30:26.132 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 09:30:26.132 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 09:50:00.003 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 09:50:00.004 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 10:00:00.012 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 10:00:00.013 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 10:30:00.008 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 10:30:00.008 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 10:30:00.008 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 10:30:19.720 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 10:30:25.516 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 10:30:25.516 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 10:50:00.002 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-31 10:50:00.003 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
2025-07-31 11:00:00.013 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-31 11:00:00.014 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-31 11:30:00.004 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-31 11:30:00.005 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-31 11:30:00.005 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-31 11:30:00.387 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 11:30:06.406 - INFO - [PqEventServiceImpl.java:2601] : read event and match wave count 0.
2025-07-31 11:30:06.406 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-31 11:47:58.227 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-31 11:48:09.411 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 13.409 seconds (JVM running for 14.781)
2025-07-31 11:50:00.011 - INFO - [ProcedureTask.java:87] : start deal pq tolerance.
2025-07-31 11:50:00.012 - INFO - [ProcedureTask.java:89] : end deal pqEvent.
2025-07-31 11:55:05.023 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-31 11:55:15.220 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 12.206 seconds (JVM running for 13.529)
2025-07-31 12:00:00.009 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 12:00:00.010 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 13:00:00.014 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 13:00:00.014 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 14:00:00.011 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 14:00:00.012 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 15:00:00.001 - INFO - [ProcedureTask.java:50] : start exec procedure.
2025-07-31 15:00:00.001 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 15:00:00.006 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 15:00:00.010 - INFO - [ProcedureTask.java:52] : end exec procedure.
2025-07-31 15:00:00.011 - INFO - [ProcedureServiceImpl.java:28] : start call S_PQRMS
2025-07-31 15:00:18.094 - INFO - [ProcedureServiceImpl.java:30] : end call S_PQRMS
2025-07-31 15:00:18.095 - INFO - [ProcedureServiceImpl.java:36] : start call S_PQSTABLE2
2025-07-31 15:00:19.033 - INFO - [ProcedureServiceImpl.java:38] : end call S_PQSTABLE2
2025-07-31 15:00:19.034 - INFO - [ProcedureServiceImpl.java:44] : start call S_PQSTABLE1_1
2025-07-31 15:00:19.228 - INFO - [ProcedureServiceImpl.java:46] : end call S_PQSTABLE1_1
2025-07-31 15:00:19.228 - INFO - [ProcedureServiceImpl.java:53] : start call S_PQSTABLE1_2
2025-07-31 15:00:19.510 - INFO - [ProcedureServiceImpl.java:55] : end call S_PQSTABLE1_2
2025-07-31 15:00:19.511 - INFO - [ProcedureServiceImpl.java:61] : start call S_PQSTABLE1_3
2025-07-31 15:00:21.321 - INFO - [ProcedureServiceImpl.java:63] : end call S_PQSTABLE1_3
2025-07-31 15:00:21.322 - INFO - [ProcedureServiceImpl.java:69] : start call S_PQSTABLE1_4
2025-07-31 15:02:49.323 - INFO - [ProcedureServiceImpl.java:71] : end call S_PQSTABLE1_4
2025-07-31 16:00:00.015 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 16:00:00.019 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 17:00:00.003 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 17:00:00.009 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 18:32:58.336 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 18:32:58.343 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 19:00:00.015 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 19:00:00.016 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 20:00:00.015 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 20:00:00.017 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 21:00:00.011 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 21:00:00.014 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 22:00:00.007 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 22:00:00.007 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
2025-07-31 23:00:00.007 - INFO - [ProcedureTask.java:65] : start deal pqhistory status.
2025-07-31 23:00:00.014 - INFO - [ProcedureTask.java:67] : end deal pqhistory status.
