2025-07-08 13:46:57.135 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-08 13:47:02.568 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 6.841 seconds (JV<PERSON> running for 7.634)
2025-07-08 18:39:04.321 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-08 18:39:14.872 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 20.43 seconds (JVM running for 23.073)
2025-07-08 18:52:15.317 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-07-08 18:52:15.321 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-07-08 18:52:16.279 - INFO - [PqEventServiceImpl.java:988] : 监测点7在2025-07-01 10:50:57发生事件波形数据为空
2025-07-08 18:52:18.148 - INFO - [PqEventServiceImpl.java:988] : 监测点4在2025-07-01 00:01:07发生事件波形数据为空
2025-07-08 18:52:19.731 - INFO - [PqEventServiceImpl.java:988] : 监测点6在2025-07-01 00:25:12发生事件波形数据为空
2025-07-08 18:52:20.981 - INFO - [PqEventServiceImpl.java:988] : 监测点2在2025-07-01 21:22:13发生事件波形数据为空
2025-07-08 18:52:21.975 - INFO - [PqEventServiceImpl.java:988] : 监测点5在2025-07-01 00:12:43发生事件波形数据为空
2025-07-08 18:52:22.916 - INFO - [PqEventServiceImpl.java:988] : 监测点15在2025-07-01 02:46:45发生事件波形数据为空
2025-07-08 18:52:29.258 - INFO - [PqEventServiceImpl.java:988] : 监测点6在2025-07-01 00:15:12发生事件波形数据为空
2025-07-08 18:52:36.240 - INFO - [PqEventServiceImpl.java:988] : 监测点9在2025-07-01 02:20:00发生事件波形数据为空
2025-07-08 18:52:45.019 - INFO - [PqEventServiceImpl.java:988] : 监测点5在2025-07-01 01:47:49发生事件波形数据为空
2025-07-08 18:52:46.979 - INFO - [PqEventServiceImpl.java:988] : 监测点6在2025-07-01 01:50:18发生事件波形数据为空
2025-07-08 18:52:47.713 - INFO - [PqEventServiceImpl.java:988] : 监测点9在2025-07-01 04:55:10发生事件波形数据为空
2025-07-08 18:56:41.135 - INFO - [PqEventServiceImpl.java:988] : 监测点4在2025-07-01 05:16:26发生事件波形数据为空
2025-07-08 18:56:41.197 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:35:24发生事件原因分析计算失败，原因SpringClientFactory-device-data-service has been closed already
2025-07-08 18:56:41.226 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 03:05:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.251 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:11:14发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.266 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 05:16:14发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.281 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:09:59发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.295 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:40:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.309 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:10:19发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.322 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 05:23:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.335 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:16:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.348 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 03:07:54发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.361 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:12:51发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.374 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 05:28:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.388 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:05:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.402 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:36:04发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.415 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:10:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.428 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 05:33:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.447 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:17:51发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.460 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 03:10:03发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.474 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:20:00发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.488 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 05:20:11发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.505 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:06:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.517 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:41:05发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.530 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:06:14发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.542 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 05:25:11发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.558 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:20:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.570 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 03:10:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.583 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:20:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.595 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 05:30:11发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.608 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:14:59发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.620 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:41:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.635 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:15:19发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.649 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 05:35:12发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.662 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:25:00发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.674 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 03:10:27发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.686 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:25:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.698 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 05:20:32发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.710 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:15:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.723 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:37:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.735 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:11:03发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.747 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 05:25:32发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.759 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:25:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.773 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 03:11:06发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.785 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:21:03发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.797 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 05:30:32发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.809 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:16:03发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.821 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:42:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.838 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:11:14发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.853 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 05:35:33发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.868 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:26:03发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.881 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 03:11:18发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.894 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:21:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.910 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 05:20:35发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.928 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:16:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.944 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:40:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.962 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:12:51发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.975 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 05:25:35发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:41.988 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:26:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.002 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 03:12:54发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.015 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:22:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.031 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 05:30:35发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.046 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:17:51发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.061 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:45:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.075 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:20:00发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.087 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 05:35:36发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.101 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:31:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.123 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 03:15:03发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.139 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:27:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.158 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 05:21:14发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.173 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:20:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.190 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:46:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.205 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:20:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.220 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 05:26:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.233 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:32:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.246 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 03:20:04发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.259 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:30:01发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.273 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 05:31:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.285 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:25:00发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.298 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:47:53发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.311 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:25:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.324 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 05:36:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.340 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:35:01发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.353 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 03:15:24发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.368 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:30:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.381 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 05:21:27发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.394 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:25:23发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.408 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:50:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.421 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:21:03发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.433 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 05:26:27发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.445 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:30:24发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.459 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 03:15:27发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.473 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:31:04发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.487 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 05:31:27发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.499 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:26:03发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.512 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:50:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.525 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:21:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.539 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 05:36:27发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.552 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:36:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.564 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 03:16:07发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.579 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:35:21发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.595 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 05:40:12发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.612 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:26:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.630 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:45:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.649 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:22:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.673 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 05:45:12发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.688 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:40:21发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.702 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 03:16:18发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.723 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:45:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.736 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 05:50:12发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.752 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:31:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.765 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:46:05发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.780 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:27:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.792 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 05:55:13发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.806 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:35:24发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.819 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 03:17:55发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.832 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:40:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.845 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:00:13发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.857 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:32:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.869 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:52:53发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.881 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:30:01发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.894 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:05:13发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.907 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:36:04发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.919 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 03:21:07发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.932 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:41:05发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.946 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:10:14发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.960 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:35:01发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.973 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:55:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.985 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:30:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:42.996 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:15:14发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.008 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:41:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.020 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 03:21:19发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.034 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:37:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.045 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:20:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.057 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:30:24发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.071 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:55:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.087 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:31:04发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.104 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:25:15发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.121 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:42:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.139 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 03:22:55发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.151 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:40:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.164 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:30:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.177 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:36:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.191 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:50:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.206 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:35:21发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.220 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:35:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.233 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:45:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.246 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 03:25:04发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.258 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:46:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.272 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:40:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.286 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:40:21发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.303 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:55:26发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.318 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:45:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.332 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:45:17发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.347 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:47:53发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.361 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 03:20:24发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.374 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:50:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.387 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:50:17发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.399 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:35:24发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.413 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:51:05发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.427 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:40:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.440 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 06:55:17发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.452 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:50:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.464 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 03:25:24发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.478 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:45:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.490 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:00:18发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.503 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:36:04发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.515 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:56:06发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.527 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:41:05发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.539 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:05:18发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.552 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:46:05发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.564 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 03:20:27发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.577 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:52:53发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.589 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:10:18发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.601 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:41:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.615 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:51:17发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.628 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:37:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.640 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:15:18发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.652 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:55:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.664 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 03:30:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.676 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:55:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.687 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:20:19发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.699 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:42:52发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.711 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:56:17发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.723 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:40:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.734 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:25:19发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.746 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:50:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.763 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 03:25:28发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.776 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:55:26发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.789 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:30:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.801 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:45:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.813 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:57:53发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.825 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:46:16发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.837 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:35:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.849 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:51:05发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.862 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 03:26:08发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.874 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:56:06发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.887 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:40:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.900 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:47:53发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.913 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 03:00:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.926 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:50:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.938 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:45:20发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.950 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:51:17发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.963 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 03:26:19发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.975 - INFO - [PqEventServiceImpl.java:155] : 监测点4在2025-07-01 02:56:17发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:43.991 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:50:21发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.003 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:50:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.016 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 03:00:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.028 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 02:45:25发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.040 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 07:55:21发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.052 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:57:53发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.065 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 03:27:55发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.077 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 03:00:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.089 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 08:00:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.102 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 02:46:05发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.114 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 03:00:26发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.126 - INFO - [PqEventServiceImpl.java:155] : 监测点5在2025-07-01 02:52:53发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.138 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 08:05:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.153 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 03:00:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.167 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 03:30:04发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.179 - INFO - [PqEventServiceImpl.java:155] : 监测点7在2025-07-01 03:00:26发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.192 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 08:10:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.205 - INFO - [PqEventServiceImpl.java:155] : 监测点9在2025-07-01 02:55:02发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.217 - INFO - [PqEventServiceImpl.java:155] : 监测点2在2025-07-01 03:01:06发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:44.234 - INFO - [PqEventServiceImpl.java:155] : 监测点6在2025-07-01 02:55:22发生事件原因分析计算失败，原因Error creating bean with name 'eurekaRibbonClientConfiguration': Unsatisfied dependency expressed through field 'eurekaConfig'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'eurekaInstanceConfigBean': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-07-08 18:56:48.274 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-08 18:56:53.612 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 6.905 seconds (JVM running for 7.846)
2025-07-08 19:03:14.672 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-07-08 19:03:14.673 - INFO - [PqEventServiceImpl.java:988] : 监测点6在2025-07-01 10:50:54发生事件波形数据为空
2025-07-08 19:03:14.676 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
