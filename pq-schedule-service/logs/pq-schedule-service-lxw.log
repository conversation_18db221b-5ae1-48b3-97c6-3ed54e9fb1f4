2025-07-30 16:59:05.968 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-30 16:59:11.208 -ERROR - [LoggingFailureAnalysisReporter.java:40] : 

***************************
AP<PERSON><PERSON>ATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).

2025-07-30 17:00:15.565 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-30 17:00:19.264 -ERROR - [LoggingFailureAnalysisReporter.java:40] : 

***************************
APPLIC<PERSON>ION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).

2025-07-30 17:01:21.492 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-30 17:01:25.354 -ERROR - [LoggingFailureAnalysisReporter.java:40] : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).

2025-07-30 17:02:36.907 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-30 17:02:46.197 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 11.001 seconds (JVM running for 12.057)
2025-07-30 17:10:11.934 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-30 17:10:20.663 - INFO - [PqEventServiceImpl.java:2619] : read event and match wave count 1258.
2025-07-30 17:21:19.390 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-30 17:21:22.356 - INFO - [PqEventServiceImpl.java:2619] : read event and match wave count 1270.
2025-07-30 17:23:40.164 - INFO - [PqEventServiceImpl.java:126] : insert pqevent count 1255.
2025-07-30 17:23:40.309 - INFO - [PqEventServiceImpl.java:2619] : read event and match wave count 0.
2025-07-30 17:23:40.309 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-30 17:30:00.013 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-30 17:30:00.014 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-30 17:30:00.014 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-30 17:30:00.161 - INFO - [PqEventServiceImpl.java:2619] : read event and match wave count 6.
2025-07-30 17:30:00.865 - INFO - [PqEventServiceImpl.java:126] : insert pqevent count 6.
2025-07-30 17:30:00.980 - INFO - [PqEventServiceImpl.java:2619] : read event and match wave count 0.
2025-07-30 17:30:00.980 - INFO - [PqEventServiceImpl.java:108] : 2024 year no event update.
2025-07-30 19:27:27.044 - INFO - [ProcedureTask.java:81] : start deal pq tolerance.
2025-07-30 19:27:27.044 - INFO - [ProcedureTask.java:59] : start deal pqhistory status.
2025-07-30 19:27:27.042 - INFO - [ProcedureTask.java:70] : start deal pqEvent.
2025-07-30 19:27:27.053 - INFO - [ProcedureTask.java:72] : end deal pqEvent.
2025-07-30 19:27:27.087 - INFO - [ProcedureTask.java:61] : end deal pqhistory status.
2025-07-30 19:27:27.087 - INFO - [PqEventServiceImpl.java:100] : start deal pq event.
2025-07-30 19:27:27.087 - INFO - [ProcedureTask.java:83] : end deal pqEvent.
