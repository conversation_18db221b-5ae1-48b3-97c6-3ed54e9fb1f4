2025-03-17 13:54:01.190 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-03-17 13:54:09.528 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 10.161 seconds (JVM running for 11.175)
2025-03-17 14:00:00.015 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 14:00:00.015 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 14:00:03.687 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 14:00:10.973 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 14:00:11.485 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 14:00:11.626 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 14:30:00.005 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 14:30:00.006 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 14:30:00.006 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 14:30:00.935 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 14:30:01.432 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 14:30:01.492 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 14:50:00.003 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 14:50:00.004 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 15:00:00.013 - INFO - [ProcedureTask.java:39] : start exec procedure.
2025-03-17 15:00:00.013 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 15:00:00.014 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 15:00:00.014 - INFO - [ProcedureTask.java:41] : end exec procedure.
2025-03-17 15:00:00.014 - INFO - [ProcedureServiceImpl.java:28] : start call S_PQRMS
2025-03-17 15:00:00.716 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 15:00:04.990 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 15:00:05.337 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 15:00:05.469 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 15:00:05.480 - INFO - [ProcedureServiceImpl.java:30] : end call S_PQRMS
2025-03-17 15:00:05.480 - INFO - [ProcedureServiceImpl.java:36] : start call S_PQSTABLE2
2025-03-17 15:00:05.829 - INFO - [ProcedureServiceImpl.java:38] : end call S_PQSTABLE2
2025-03-17 15:00:05.829 - INFO - [ProcedureServiceImpl.java:44] : start call S_PQSTABLE1_1
2025-03-17 15:00:05.922 - INFO - [ProcedureServiceImpl.java:46] : end call S_PQSTABLE1_1
2025-03-17 15:00:05.922 - INFO - [ProcedureServiceImpl.java:53] : start call S_PQSTABLE1_2
2025-03-17 15:00:06.572 - INFO - [ProcedureServiceImpl.java:55] : end call S_PQSTABLE1_2
2025-03-17 15:00:06.572 - INFO - [ProcedureServiceImpl.java:61] : start call S_PQSTABLE1_3
2025-03-17 15:00:08.956 - INFO - [ProcedureServiceImpl.java:63] : end call S_PQSTABLE1_3
2025-03-17 15:00:08.956 - INFO - [ProcedureServiceImpl.java:69] : start call S_PQSTABLE1_4
2025-03-17 15:00:57.092 - INFO - [ProcedureServiceImpl.java:71] : end call S_PQSTABLE1_4
2025-03-17 15:30:00.010 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 15:30:00.012 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 15:30:00.013 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 15:30:00.430 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 15:30:00.761 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 15:30:00.935 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 15:50:00.015 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 15:50:00.015 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 16:00:00.009 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 16:00:00.010 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 16:00:00.584 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 16:00:04.955 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 16:00:05.259 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 16:00:05.374 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 16:30:00.007 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 16:30:00.008 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 16:30:00.032 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 16:30:00.339 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 16:30:00.551 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 16:30:00.603 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 16:50:00.013 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 16:50:00.013 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 17:00:00.014 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 17:00:00.015 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 17:00:00.586 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 17:00:06.361 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 17:00:06.672 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 17:00:06.781 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 17:30:00.004 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 17:30:00.004 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 17:30:00.005 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 17:30:00.318 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 17:30:00.568 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 17:30:00.617 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 17:50:00.004 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 17:50:00.004 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 18:00:00.014 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 18:00:00.014 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 18:00:00.581 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 18:00:04.521 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 18:00:04.824 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 18:00:04.932 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 18:30:00.001 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 18:30:00.001 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 18:30:00.002 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 18:30:00.333 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 18:30:00.560 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 18:30:00.609 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 18:50:00.015 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 18:50:00.015 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 19:00:00.012 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 19:00:00.013 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 19:00:00.574 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 19:00:04.613 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 19:00:04.932 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 19:00:05.041 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 19:30:00.006 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 19:30:00.006 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 19:30:00.007 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 19:30:00.333 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 19:30:00.543 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 19:30:00.598 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 19:50:00.006 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 19:50:00.006 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 20:00:00.014 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 20:00:00.015 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 20:00:00.577 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 20:00:05.000 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 20:00:05.320 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 20:00:05.462 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 20:30:00.015 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 20:30:00.017 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 20:30:00.018 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 20:30:00.325 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 20:30:00.517 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 20:30:00.567 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 20:50:00.004 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 20:50:00.005 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 21:00:00.005 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 21:00:00.005 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 21:00:00.580 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 21:00:04.415 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 21:00:04.720 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 21:00:04.820 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 21:30:00.012 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 21:30:00.012 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 21:30:00.012 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 21:30:00.331 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 21:30:00.554 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 21:30:00.603 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 21:50:00.004 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 21:50:00.004 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 22:00:00.003 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 22:00:00.003 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 22:00:00.533 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 22:00:04.055 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 22:00:04.343 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 22:00:04.442 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 22:30:00.006 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 22:30:00.007 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 22:30:00.007 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 22:30:00.315 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 22:30:00.501 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 22:30:00.550 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 22:50:00.009 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 22:50:00.009 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-17 23:00:00.007 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-17 23:00:00.007 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-17 23:00:00.551 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-17 23:00:04.376 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-17 23:00:04.678 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-17 23:00:04.802 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-17 23:30:00.003 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-17 23:30:00.003 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-17 23:30:00.003 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-17 23:30:00.299 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 23:30:00.510 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-17 23:30:00.558 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-17 23:50:00.003 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-17 23:50:00.003 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
