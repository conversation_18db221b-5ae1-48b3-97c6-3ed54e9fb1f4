2025-07-09 11:02:27.940 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-09 11:02:34.417 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 8.049 seconds (JVM running for 9.018)
2025-07-09 11:06:06.712 - INFO - [PqEventServiceImpl.java:165] : event cause analysis add 2591.
2025-07-09 11:30:00.008 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-07-09 11:30:00.009 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-07-09 11:30:00.010 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-07-09 11:30:00.361 - INFO - [PqEventServiceImpl.java:2418] : read event and match wave count 108.
2025-07-09 11:30:00.904 -ERROR - [PqEventServiceImpl.java:119] : insert event error.
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
### The error may exist in file [E:\code\dev1\PQSysModule\pq-schedule-service\target\classes\mapper\matterhorn\PqEventMapper.xml]
### The error may involve com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper.insertPqEventVariation-Inline
### The error occurred while setting parameters
### SQL: insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;
### Cause: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy111.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy118.insertPqEventVariation(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.cet.pqscheduleservice.annotation.BatchAOP.around(BatchAOP.java:55)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy119.insertPqEventVariation(Unknown Source)
	at com.cet.pqscheduleservice.service.impl.PqEventServiceImpl.dealPqEvent(PqEventServiceImpl.java:116)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqEvent$3(ProcedureTask.java:66)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2468)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2211)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:309)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:446)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:370)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:149)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:138)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3409)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3407)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 32 common frames omitted
2025-07-09 11:30:01.053 - INFO - [PqEventServiceImpl.java:2418] : read event and match wave count 0.
2025-07-09 11:30:01.054 - INFO - [PqEventServiceImpl.java:103] : 2024 year no event update.
2025-07-09 11:45:01.668 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-07-09 11:45:07.474 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 7.436 seconds (JVM running for 8.306)
2025-07-09 11:48:44.463 - INFO - [PqEventServiceImpl.java:990] : 监测点6在2025-07-01 15:36:09发生事件波形数据为空
2025-07-09 11:49:35.829 - INFO - [PqEventServiceImpl.java:165] : event cause analysis add 2590.
2025-07-09 11:50:00.010 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-07-09 11:50:00.019 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-07-09 11:50:45.132 - INFO - [PqEventServiceImpl.java:165] : event cause analysis add 2591.
2025-07-09 12:00:00.006 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-07-09 12:00:00.007 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-07-09 12:00:00.113 - INFO - [PqHistoryStatusServiceImpl.java:85] : update pqhistorystatus count 36.
2025-07-09 12:00:00.121 - INFO - [PqHistoryStatusServiceImpl.java:89] : update line count 9.
2025-07-09 12:00:00.123 - INFO - [PqHistoryStatusServiceImpl.java:108] : no hidden line.
2025-07-09 12:30:00.010 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-07-09 12:30:00.011 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-07-09 12:30:00.011 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-07-09 12:30:00.299 - INFO - [PqEventServiceImpl.java:2432] : read event and match wave count 108.
2025-07-09 12:30:00.380 -ERROR - [PqEventServiceImpl.java:119] : insert event error.
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
### The error may exist in file [E:\code\dev1\PQSysModule\pq-schedule-service\target\classes\mapper\matterhorn\PqEventMapper.xml]
### The error may involve com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper.insertPqEventVariation-Inline
### The error occurred while setting parameters
### SQL: insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;
### Cause: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy111.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy118.insertPqEventVariation(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.cet.pqscheduleservice.annotation.BatchAOP.around(BatchAOP.java:55)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy119.insertPqEventVariation(Unknown Source)
	at com.cet.pqscheduleservice.service.impl.PqEventServiceImpl.dealPqEvent(PqEventServiceImpl.java:116)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqEvent$3(ProcedureTask.java:66)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2468)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2211)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:309)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:446)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:370)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:149)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:138)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3409)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3407)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 32 common frames omitted
2025-07-09 12:30:00.416 - INFO - [PqEventServiceImpl.java:2432] : read event and match wave count 0.
2025-07-09 12:30:00.416 - INFO - [PqEventServiceImpl.java:103] : 2024 year no event update.
2025-07-09 12:50:00.002 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-07-09 12:50:00.002 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-07-09 13:00:00.008 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-07-09 13:00:00.008 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-07-09 13:00:00.044 - INFO - [PqHistoryStatusServiceImpl.java:85] : update pqhistorystatus count 36.
2025-07-09 13:00:00.053 - INFO - [PqHistoryStatusServiceImpl.java:89] : update line count 9.
2025-07-09 13:00:00.053 - INFO - [PqHistoryStatusServiceImpl.java:108] : no hidden line.
2025-07-09 13:30:00.014 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-07-09 13:30:00.015 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-07-09 13:30:00.015 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-07-09 13:30:00.292 - INFO - [PqEventServiceImpl.java:2432] : read event and match wave count 108.
2025-07-09 13:30:00.366 -ERROR - [PqEventServiceImpl.java:119] : insert event error.
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
### The error may exist in file [E:\code\dev1\PQSysModule\pq-schedule-service\target\classes\mapper\matterhorn\PqEventMapper.xml]
### The error may involve com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper.insertPqEventVariation-Inline
### The error occurred while setting parameters
### SQL: insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;
### Cause: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy111.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy118.insertPqEventVariation(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.cet.pqscheduleservice.annotation.BatchAOP.around(BatchAOP.java:55)
	at sun.reflect.GeneratedMethodAccessor171.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy119.insertPqEventVariation(Unknown Source)
	at com.cet.pqscheduleservice.service.impl.PqEventServiceImpl.dealPqEvent(PqEventServiceImpl.java:116)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqEvent$3(ProcedureTask.java:66)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2468)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2211)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:309)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:446)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:370)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:149)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:138)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3409)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3407)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 31 common frames omitted
2025-07-09 13:30:00.397 - INFO - [PqEventServiceImpl.java:2432] : read event and match wave count 0.
2025-07-09 13:30:00.397 - INFO - [PqEventServiceImpl.java:103] : 2024 year no event update.
2025-07-09 13:50:00.004 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-07-09 13:50:00.005 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-07-09 14:00:00.015 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-07-09 14:00:00.015 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-07-09 14:00:00.068 - INFO - [PqHistoryStatusServiceImpl.java:85] : update pqhistorystatus count 36.
2025-07-09 14:00:00.076 - INFO - [PqHistoryStatusServiceImpl.java:89] : update line count 9.
2025-07-09 14:00:00.077 - INFO - [PqHistoryStatusServiceImpl.java:108] : no hidden line.
2025-07-09 14:30:00.012 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-07-09 14:30:00.013 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-07-09 14:30:00.013 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-07-09 14:30:00.373 - INFO - [PqEventServiceImpl.java:2432] : read event and match wave count 108.
2025-07-09 14:30:00.472 -ERROR - [PqEventServiceImpl.java:119] : insert event error.
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
### The error may exist in file [E:\code\dev1\PQSysModule\pq-schedule-service\target\classes\mapper\matterhorn\PqEventMapper.xml]
### The error may involve com.cet.pqscheduleservice.mapper.matterhorn.PqEventMapper.insertPqEventVariation-Inline
### The error occurred while setting parameters
### SQL: insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;                                   insert into pqvariationevent (monitoredlabel, monitoredid, srcdeviceid, eventtime, pqvariationeventtype, nominalvoltage, duration, v1duration,                         v2duration, v3duration, magnitude, v1magnitude, v2magnitude, v3magnitude, v1trigger, v2trigger, v3trigger, waveformlogtime, valid, description, confirmeventstatus, remark, updatetime, transientfaultdirection, toleranceband, operator, isminuteextreme, lossenergy,escapestatus,reason, faultreason) values                         (?,?,?,?,?,?,?,?,?,?,?,?,?,                         ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)             on conflict(monitoredlabel,monitoredid,eventtime,pqvariationeventtype,v1trigger,v2trigger,v3trigger) do           UPDATE SET              magnitude = excluded.magnitude,              escapestatus = excluded.escapestatus,              faultreason = excluded.faultreason,              duration = excluded.duration;
### Cause: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy111.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:271)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy118.insertPqEventVariation(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.cet.pqscheduleservice.annotation.BatchAOP.around(BatchAOP.java:55)
	at sun.reflect.GeneratedMethodAccessor171.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy119.insertPqEventVariation(Unknown Source)
	at com.cet.pqscheduleservice.service.impl.PqEventServiceImpl.dealPqEvent(PqEventServiceImpl.java:116)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqEvent$3(ProcedureTask.java:66)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: 错误: 没有匹配ON CONFLICT说明的唯一或者排除约束
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2468)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2211)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:309)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:446)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:370)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:149)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:138)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3409)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3407)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 31 common frames omitted
2025-07-09 14:30:00.519 - INFO - [PqEventServiceImpl.java:2432] : read event and match wave count 0.
2025-07-09 14:30:00.519 - INFO - [PqEventServiceImpl.java:103] : 2024 year no event update.
