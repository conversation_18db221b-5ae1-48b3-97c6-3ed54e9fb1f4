2025-03-18 00:00:00.015 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 00:00:00.057 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 00:00:00.602 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 00:00:04.261 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 00:00:04.603 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 00:00:04.731 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 00:30:00.002 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 00:30:00.002 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 00:30:00.002 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 00:30:00.314 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 00:30:00.523 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 00:30:00.576 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 00:50:00.014 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 00:50:00.014 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 01:00:00.005 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 01:00:00.005 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 01:00:00.555 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 01:00:04.101 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 01:00:04.412 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 01:00:04.521 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 01:30:00.015 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 01:30:00.015 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 01:30:00.015 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 01:30:00.327 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 01:30:00.528 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 01:30:00.577 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 01:50:00.014 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 01:50:00.015 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 02:00:00.006 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 02:00:00.006 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 02:00:00.623 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 02:00:04.679 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 02:00:04.974 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 02:00:05.334 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 02:30:00.001 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 02:30:00.001 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 02:30:00.001 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 02:30:00.304 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 02:30:00.675 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 02:30:00.999 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 02:50:00.015 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 02:50:00.015 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 03:00:00.015 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 03:00:00.015 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 03:00:01.300 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 03:00:06.093 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 03:00:06.796 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 03:00:07.305 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 03:30:00.009 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 03:30:00.023 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 03:30:00.023 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 03:30:00.333 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 03:30:00.757 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 03:30:00.940 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 03:50:00.024 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 03:50:00.024 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 04:00:00.007 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 04:00:00.007 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 04:00:01.078 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 04:00:04.956 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 04:00:05.260 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 04:00:05.544 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 04:30:00.015 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 04:30:00.015 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 04:30:00.015 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 04:30:00.324 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 04:30:00.665 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 04:30:00.859 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 04:50:00.007 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 04:50:00.007 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 05:00:00.009 - INFO - [ProcedureTask.java:39] : start exec procedure.
2025-03-18 05:00:00.009 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 05:00:00.009 - INFO - [ProcedureTask.java:41] : end exec procedure.
2025-03-18 05:00:00.009 - INFO - [ProcedureServiceImpl.java:28] : start call S_PQRMS
2025-03-18 05:00:00.009 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 05:00:01.859 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 05:00:06.384 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 05:00:06.671 - INFO - [ProcedureServiceImpl.java:30] : end call S_PQRMS
2025-03-18 05:00:06.672 - INFO - [ProcedureServiceImpl.java:36] : start call S_PQSTABLE2
2025-03-18 05:00:06.959 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 05:00:06.976 - INFO - [ProcedureServiceImpl.java:38] : end call S_PQSTABLE2
2025-03-18 05:00:06.976 - INFO - [ProcedureServiceImpl.java:44] : start call S_PQSTABLE1_1
2025-03-18 05:00:07.062 - INFO - [ProcedureServiceImpl.java:46] : end call S_PQSTABLE1_1
2025-03-18 05:00:07.062 - INFO - [ProcedureServiceImpl.java:53] : start call S_PQSTABLE1_2
2025-03-18 05:00:07.492 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 05:00:07.603 - INFO - [ProcedureServiceImpl.java:55] : end call S_PQSTABLE1_2
2025-03-18 05:00:07.603 - INFO - [ProcedureServiceImpl.java:61] : start call S_PQSTABLE1_3
2025-03-18 05:00:10.456 - INFO - [ProcedureServiceImpl.java:63] : end call S_PQSTABLE1_3
2025-03-18 05:00:10.457 - INFO - [ProcedureServiceImpl.java:69] : start call S_PQSTABLE1_4
2025-03-18 05:00:57.532 - INFO - [ProcedureServiceImpl.java:71] : end call S_PQSTABLE1_4
2025-03-18 05:30:00.007 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 05:30:00.007 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 05:30:00.007 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 05:30:00.308 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 05:30:00.683 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 05:30:00.896 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 05:50:00.007 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 05:50:00.007 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 06:00:00.014 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 06:00:00.014 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 06:00:00.811 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 06:00:05.226 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 06:00:05.521 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 06:00:05.812 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 06:30:00.007 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 06:30:00.007 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 06:30:00.008 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 06:30:00.321 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 06:30:00.643 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 06:30:00.838 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 06:50:00.001 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 06:50:00.001 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 07:00:00.007 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 07:00:00.007 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 07:00:01.182 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 07:00:05.017 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 07:00:05.328 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 07:00:05.654 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 07:30:00.006 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 07:30:00.006 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 07:30:00.006 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 07:30:00.305 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 07:30:00.742 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 07:30:00.912 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 07:50:00.010 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 07:50:00.010 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 08:00:00.013 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 08:00:00.013 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 08:00:00.993 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 08:00:04.804 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 08:00:05.064 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 08:00:05.348 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 08:30:00.007 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 08:30:00.007 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 08:30:00.007 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 08:30:00.331 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 08:30:00.605 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 08:30:00.735 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 08:50:00.003 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 08:50:00.003 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 09:00:00.015 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 09:00:00.015 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 09:00:00.741 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 09:00:04.406 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 09:00:04.728 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 09:00:04.950 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 09:30:00.005 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 09:30:00.006 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 09:30:00.006 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 09:30:00.352 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 09:30:00.658 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 09:30:00.865 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 09:50:00.009 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 09:50:00.009 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 10:00:00.009 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 10:00:00.009 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 10:00:00.796 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 10:00:05.746 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 10:00:06.100 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 10:00:06.666 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 10:30:00.013 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 10:30:00.013 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 10:30:00.014 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 10:30:01.564 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 10:30:01.974 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 10:30:02.199 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 10:50:00.000 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 10:50:00.001 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 11:00:00.006 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 11:00:00.006 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 11:00:00.809 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 11:00:04.567 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 11:00:04.850 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 11:00:04.999 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 11:30:00.014 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 11:30:00.015 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 11:30:00.015 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 11:30:00.316 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 11:30:00.574 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 11:30:00.664 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 11:50:00.004 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 11:50:00.004 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 12:00:00.004 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 12:00:00.005 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 12:00:00.600 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 12:00:04.469 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 12:00:04.764 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 12:00:04.888 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 12:30:00.013 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 12:30:00.014 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 12:30:00.014 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 12:30:00.318 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 12:30:00.552 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 12:30:00.623 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 12:50:00.006 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 12:50:00.006 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 13:00:00.001 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 13:00:00.001 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 13:00:00.562 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 13:00:04.109 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 13:00:04.445 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 13:00:04.570 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 13:30:00.004 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 13:30:00.004 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 13:30:00.004 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 13:30:00.534 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 13:30:00.750 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 13:30:00.825 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 13:50:00.012 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 13:50:00.013 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 14:00:00.006 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 14:00:00.007 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 14:00:01.600 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 14:00:06.216 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 14:00:06.535 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 14:00:06.659 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 14:30:00.011 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-18 14:30:00.012 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-18 14:30:00.012 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-18 14:30:00.323 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 14:30:00.536 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-18 14:30:00.589 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-18 14:50:00.011 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-18 14:50:00.011 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-18 15:00:00.013 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-18 15:00:00.013 - INFO - [ProcedureTask.java:39] : start exec procedure.
2025-03-18 15:00:00.013 - INFO - [ProcedureTask.java:41] : end exec procedure.
2025-03-18 15:00:00.013 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-18 15:00:00.022 - INFO - [ProcedureServiceImpl.java:28] : start call S_PQRMS
2025-03-18 15:00:00.737 - INFO - [PqHistoryStatusServiceImpl.java:326] : query monitor count 1392.
2025-03-18 15:00:05.521 - INFO - [ProcedureServiceImpl.java:30] : end call S_PQRMS
2025-03-18 15:00:05.522 - INFO - [ProcedureServiceImpl.java:36] : start call S_PQSTABLE2
2025-03-18 15:00:05.639 - INFO - [PqHistoryStatusServiceImpl.java:86] : update pqhistorystatus count 5550.
2025-03-18 15:00:05.818 - INFO - [ProcedureServiceImpl.java:38] : end call S_PQSTABLE2
2025-03-18 15:00:05.818 - INFO - [ProcedureServiceImpl.java:44] : start call S_PQSTABLE1_1
2025-03-18 15:00:05.903 - INFO - [ProcedureServiceImpl.java:46] : end call S_PQSTABLE1_1
2025-03-18 15:00:05.903 - INFO - [ProcedureServiceImpl.java:53] : start call S_PQSTABLE1_2
2025-03-18 15:00:05.992 - INFO - [PqHistoryStatusServiceImpl.java:90] : update line count 1392.
2025-03-18 15:00:06.303 - INFO - [PqHistoryStatusServiceImpl.java:115] : update hidden line count 236.
2025-03-18 15:00:06.418 - INFO - [ProcedureServiceImpl.java:55] : end call S_PQSTABLE1_2
2025-03-18 15:00:06.418 - INFO - [ProcedureServiceImpl.java:61] : start call S_PQSTABLE1_3
2025-03-18 15:00:09.056 - INFO - [ProcedureServiceImpl.java:63] : end call S_PQSTABLE1_3
2025-03-18 15:00:09.056 - INFO - [ProcedureServiceImpl.java:69] : start call S_PQSTABLE1_4
2025-03-18 15:00:53.586 - INFO - [ProcedureServiceImpl.java:71] : end call S_PQSTABLE1_4
