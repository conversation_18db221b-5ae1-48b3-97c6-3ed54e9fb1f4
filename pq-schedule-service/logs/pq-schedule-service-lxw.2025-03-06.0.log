2025-03-06 00:00:00.005 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 00:00:00.033 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 00:30:00.004 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 00:30:00.022 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 00:30:00.022 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 00:30:19.677 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 00:30:19.903 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 00:30:20.008 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 00:50:00.008 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 00:50:00.008 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 01:00:00.005 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 01:00:00.006 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 01:30:00.011 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 01:30:00.012 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 01:30:00.012 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 01:30:00.325 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 01:30:00.597 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 01:30:00.670 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 01:50:00.014 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 01:50:00.014 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 02:00:00.002 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 02:00:00.002 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 02:30:00.002 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 02:30:00.003 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 02:30:00.003 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 02:30:19.654 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 02:30:19.874 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 02:30:20.000 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 02:50:00.001 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 02:50:00.001 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 03:00:00.007 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 03:00:00.008 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 03:30:00.010 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 03:30:00.010 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 03:30:00.010 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 03:30:19.656 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 03:30:19.865 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 03:30:19.923 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 03:50:00.001 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 03:50:00.002 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 04:00:00.008 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 04:00:00.008 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 04:30:00.005 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 04:30:00.005 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 04:30:00.005 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 04:30:19.647 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 04:30:19.903 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 04:30:19.963 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 04:50:00.003 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 04:50:00.003 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 05:00:00.011 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 05:00:00.011 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 05:00:00.031 - INFO - [ProcedureTask.java:39] : start exec procedure.
2025-03-06 05:00:00.032 - INFO - [ProcedureTask.java:41] : end exec procedure.
2025-03-06 05:00:00.032 - INFO - [ProcedureServiceImpl.java:28] : start call S_PQRMS
2025-03-06 05:00:05.625 - INFO - [ProcedureServiceImpl.java:30] : end call S_PQRMS
2025-03-06 05:00:05.625 - INFO - [ProcedureServiceImpl.java:36] : start call S_PQSTABLE2
2025-03-06 05:00:05.972 - INFO - [ProcedureServiceImpl.java:38] : end call S_PQSTABLE2
2025-03-06 05:00:05.972 - INFO - [ProcedureServiceImpl.java:44] : start call S_PQSTABLE1_1
2025-03-06 05:00:06.076 - INFO - [ProcedureServiceImpl.java:46] : end call S_PQSTABLE1_1
2025-03-06 05:00:06.076 - INFO - [ProcedureServiceImpl.java:53] : start call S_PQSTABLE1_2
2025-03-06 05:00:06.583 - INFO - [ProcedureServiceImpl.java:55] : end call S_PQSTABLE1_2
2025-03-06 05:00:06.583 - INFO - [ProcedureServiceImpl.java:61] : start call S_PQSTABLE1_3
2025-03-06 05:00:09.003 - INFO - [ProcedureServiceImpl.java:63] : end call S_PQSTABLE1_3
2025-03-06 05:00:09.004 - INFO - [ProcedureServiceImpl.java:69] : start call S_PQSTABLE1_4
2025-03-06 05:00:15.280 - INFO - [ProcedureServiceImpl.java:71] : end call S_PQSTABLE1_4
2025-03-06 05:30:00.005 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 05:30:00.005 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 05:30:00.005 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 05:30:00.317 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 05:30:00.553 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 05:30:00.613 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 05:50:00.015 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 05:50:00.015 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 06:00:00.004 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 06:00:00.005 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 06:30:00.004 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 06:30:00.004 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 06:30:00.004 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 06:30:19.660 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 06:30:19.886 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 06:30:19.940 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 06:50:00.003 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 06:50:00.003 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 07:00:00.009 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 07:00:00.009 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 07:30:00.002 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 07:30:00.002 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 07:30:00.023 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 07:30:19.705 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 07:30:19.930 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 07:30:19.987 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 07:50:00.010 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 07:50:00.010 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 08:00:00.002 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 08:00:00.002 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 08:30:00.003 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 08:30:00.003 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 08:30:00.003 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 08:30:19.657 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 08:30:19.872 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 08:30:19.925 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 08:50:00.004 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 08:50:00.004 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 09:00:00.006 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 09:00:00.007 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 09:30:00.014 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 09:30:00.015 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 09:30:00.015 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 09:30:19.688 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 09:30:19.908 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 09:30:19.966 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 09:50:00.020 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 09:50:00.021 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 10:00:00.008 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 10:00:00.008 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 10:30:00.009 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 10:30:00.010 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 10:30:00.010 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 10:30:00.331 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 10:30:00.556 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 10:30:00.863 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 10:43:29.503 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-03-06 10:43:36.544 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 8.523 seconds (JVM running for 9.43)
2025-03-06 10:50:00.002 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 10:50:00.003 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 11:00:00.001 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 11:00:00.002 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 11:07:37.639 -ERROR - [RedirectingEurekaHttpClient.java:91] : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://************:1001/eureka/}
javax.ws.rs.WebApplicationException: com.fasterxml.jackson.core.JsonParseException: processing aborted
 at [Source: (GZIPInputStream); line: 1, column: 18]
	at com.netflix.discovery.provider.DiscoveryJerseyProvider.readFrom(DiscoveryJerseyProvider.java:110)
	at com.sun.jersey.api.client.ClientResponse.getEntity(ClientResponse.java:634)
	at com.sun.jersey.api.client.ClientResponse.getEntity(ClientResponse.java:586)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getApplicationsInternal(AbstractJerseyEurekaHttpClient.java:198)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getDelta(AbstractJerseyEurekaHttpClient.java:170)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1085)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:967)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1479)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1446)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.fasterxml.jackson.core.JsonParseException: processing aborted
 at [Source: (GZIPInputStream); line: 1, column: 18]
	at com.netflix.discovery.converters.EurekaJacksonCodec$ApplicationsDeserializer.deserialize(EurekaJacksonCodec.java:789)
	at com.netflix.discovery.converters.EurekaJacksonCodec$ApplicationsDeserializer.deserialize(EurekaJacksonCodec.java:775)
	at com.fasterxml.jackson.databind.ObjectReader._unwrapAndDeserialize(ObjectReader.java:1703)
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:1608)
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1188)
	at com.netflix.discovery.converters.EurekaJacksonCodec.readValue(EurekaJacksonCodec.java:197)
	at com.netflix.discovery.converters.wrappers.CodecWrappers$LegacyJacksonJson.decode(CodecWrappers.java:314)
	at com.netflix.discovery.provider.DiscoveryJerseyProvider.readFrom(DiscoveryJerseyProvider.java:103)
	... 26 common frames omitted
2025-03-06 11:07:37.687 -ERROR - [RedirectingEurekaHttpClient.java:83] : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://************:1001/eureka/}
javax.ws.rs.WebApplicationException: com.fasterxml.jackson.core.JsonParseException: processing aborted
 at [Source: (GZIPInputStream); line: 1, column: 18]
	at com.netflix.discovery.provider.DiscoveryJerseyProvider.readFrom(DiscoveryJerseyProvider.java:110)
	at com.sun.jersey.api.client.ClientResponse.getEntity(ClientResponse.java:634)
	at com.sun.jersey.api.client.ClientResponse.getEntity(ClientResponse.java:586)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getApplicationsInternal(AbstractJerseyEurekaHttpClient.java:198)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getDelta(AbstractJerseyEurekaHttpClient.java:170)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1085)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:967)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1479)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1446)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.fasterxml.jackson.core.JsonParseException: processing aborted
 at [Source: (GZIPInputStream); line: 1, column: 18]
	at com.netflix.discovery.converters.EurekaJacksonCodec$ApplicationsDeserializer.deserialize(EurekaJacksonCodec.java:789)
	at com.netflix.discovery.converters.EurekaJacksonCodec$ApplicationsDeserializer.deserialize(EurekaJacksonCodec.java:775)
	at com.fasterxml.jackson.databind.ObjectReader._unwrapAndDeserialize(ObjectReader.java:1703)
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:1608)
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1188)
	at com.netflix.discovery.converters.EurekaJacksonCodec.readValue(EurekaJacksonCodec.java:197)
	at com.netflix.discovery.converters.wrappers.CodecWrappers$LegacyJacksonJson.decode(CodecWrappers.java:314)
	at com.netflix.discovery.provider.DiscoveryJerseyProvider.readFrom(DiscoveryJerseyProvider.java:103)
	... 27 common frames omitted
2025-03-06 11:07:37.723 -ERROR - [RedirectingEurekaHttpClient.java:83] : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://************:1001/eureka/}
javax.ws.rs.WebApplicationException: com.fasterxml.jackson.core.JsonParseException: processing aborted
 at [Source: (GZIPInputStream); line: 1, column: 18]
	at com.netflix.discovery.provider.DiscoveryJerseyProvider.readFrom(DiscoveryJerseyProvider.java:110)
	at com.sun.jersey.api.client.ClientResponse.getEntity(ClientResponse.java:634)
	at com.sun.jersey.api.client.ClientResponse.getEntity(ClientResponse.java:586)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getApplicationsInternal(AbstractJerseyEurekaHttpClient.java:198)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getDelta(AbstractJerseyEurekaHttpClient.java:170)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1085)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:967)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1479)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1446)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.fasterxml.jackson.core.JsonParseException: processing aborted
 at [Source: (GZIPInputStream); line: 1, column: 18]
	at com.netflix.discovery.converters.EurekaJacksonCodec$ApplicationsDeserializer.deserialize(EurekaJacksonCodec.java:789)
	at com.netflix.discovery.converters.EurekaJacksonCodec$ApplicationsDeserializer.deserialize(EurekaJacksonCodec.java:775)
	at com.fasterxml.jackson.databind.ObjectReader._unwrapAndDeserialize(ObjectReader.java:1703)
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:1608)
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1188)
	at com.netflix.discovery.converters.EurekaJacksonCodec.readValue(EurekaJacksonCodec.java:197)
	at com.netflix.discovery.converters.wrappers.CodecWrappers$LegacyJacksonJson.decode(CodecWrappers.java:314)
	at com.netflix.discovery.provider.DiscoveryJerseyProvider.readFrom(DiscoveryJerseyProvider.java:103)
	... 27 common frames omitted
2025-03-06 11:07:37.724 -ERROR - [DiscoveryClient.java:972] : DiscoveryClient_PQ-SCHEDULE-SERVICE-LXW/Shelven:pq-schedule-service-lxw:8156 - was unable to refresh its cache! status = Retry limit reached; giving up on completing the request
com.netflix.discovery.shared.transport.TransportException: Retry limit reached; giving up on completing the request
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:139)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1085)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:967)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1479)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1446)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-03-06 11:12:51.917 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-03-06 11:12:58.699 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 8.273 seconds (JVM running for 9.195)
2025-03-06 11:30:00.004 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 11:30:00.005 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 11:30:00.006 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 11:30:00.426 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 11:30:00.728 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 11:30:00.851 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 11:50:00.011 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 11:50:00.012 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 12:00:00.005 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 12:00:00.006 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 12:03:43.461 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-03-06 12:03:50.035 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 9.238 seconds (JVM running for 10.297)
2025-03-06 12:30:00.002 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 12:30:00.003 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 12:30:00.003 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 12:30:00.443 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 12:30:00.665 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 12:30:00.734 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 12:50:00.008 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 12:50:00.008 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 13:00:00.011 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 13:00:00.012 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 13:00:00.256 - INFO - [PqHistoryStatusServiceImpl.java:110] : no hidden line.
2025-03-06 13:30:00.009 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 13:30:00.009 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 13:30:00.010 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 13:30:19.644 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 13:30:19.865 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 13:30:19.926 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 13:50:00.005 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 13:50:00.005 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 14:00:00.008 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 14:00:00.008 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 14:00:00.270 - INFO - [PqHistoryStatusServiceImpl.java:110] : no hidden line.
2025-03-06 14:30:00.012 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 14:30:00.013 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 14:30:00.014 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 14:30:19.722 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 14:30:19.941 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 14:30:19.991 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 14:50:00.006 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 14:50:00.007 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 15:00:00.009 - INFO - [ProcedureTask.java:39] : start exec procedure.
2025-03-06 15:00:00.009 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 15:00:00.010 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 15:00:00.012 - INFO - [ProcedureTask.java:41] : end exec procedure.
2025-03-06 15:00:00.013 - INFO - [ProcedureServiceImpl.java:28] : start call S_PQRMS
2025-03-06 15:11:40.709 - INFO - [ProcedureServiceImpl.java:30] : end call S_PQRMS
2025-03-06 15:11:40.709 - INFO - [ProcedureServiceImpl.java:36] : start call S_PQSTABLE2
2025-03-06 15:24:09.603 - INFO - [PqHistoryStatusServiceImpl.java:110] : no hidden line.
2025-03-06 15:24:09.604 - INFO - [ProcedureServiceImpl.java:38] : end call S_PQSTABLE2
2025-03-06 15:24:09.604 - INFO - [ProcedureServiceImpl.java:44] : start call S_PQSTABLE1_1
2025-03-06 15:24:09.708 - INFO - [ProcedureServiceImpl.java:46] : end call S_PQSTABLE1_1
2025-03-06 15:24:09.708 - INFO - [ProcedureServiceImpl.java:53] : start call S_PQSTABLE1_2
2025-03-06 15:24:10.080 - INFO - [ProcedureServiceImpl.java:55] : end call S_PQSTABLE1_2
2025-03-06 15:24:10.080 - INFO - [ProcedureServiceImpl.java:61] : start call S_PQSTABLE1_3
2025-03-06 15:24:11.369 - INFO - [ProcedureServiceImpl.java:63] : end call S_PQSTABLE1_3
2025-03-06 15:24:11.369 - INFO - [ProcedureServiceImpl.java:69] : start call S_PQSTABLE1_4
2025-03-06 15:24:20.457 - INFO - [ProcedureServiceImpl.java:71] : end call S_PQSTABLE1_4
2025-03-06 15:30:00.008 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 15:30:00.009 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 15:30:00.009 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 15:30:00.325 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 15:30:00.537 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 15:30:00.593 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 15:34:27.902 - INFO - [PqHistoryStatusServiceImpl.java:113] : update hidden line count 1.
2025-03-06 15:34:33.745 - INFO - [PqHistoryStatusServiceImpl.java:300] : query monitor count 1376.
2025-03-06 15:36:21.633 - INFO - [PqHistoryStatusServiceImpl.java:110] : no hidden line.
2025-03-06 15:36:48.570 - INFO - [PqHistoryStatusServiceImpl.java:110] : no hidden line.
2025-03-06 15:37:13.331 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-03-06 15:37:20.052 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 8.331 seconds (JVM running for 9.429)
2025-03-06 15:38:55.555 - INFO - [PqHistoryStatusServiceImpl.java:110] : no hidden line.
2025-03-06 15:39:04.447 - INFO - [PqHistoryStatusServiceImpl.java:301] : query monitor count 1376.
2025-03-06 15:40:32.806 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-03-06 15:40:38.908 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 7.514 seconds (JVM running for 8.392)
2025-03-06 15:41:03.274 - INFO - [PqHistoryStatusServiceImpl.java:300] : query monitor count 1376.
2025-03-06 15:41:27.019 - INFO - [PqHistoryStatusServiceImpl.java:300] : query monitor count 1376.
2025-03-06 15:44:37.744 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-03-06 15:44:43.800 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 7.512 seconds (JVM running for 8.311)
2025-03-06 15:46:14.651 - INFO - [PqHistoryStatusServiceImpl.java:297] : query monitor count 1376.
2025-03-06 15:46:46.864 - INFO - [PqHistoryStatusServiceImpl.java:297] : query monitor count 1376.
2025-03-06 15:51:38.209 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 15:51:38.211 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 15:51:38.217 - INFO - [PqHistoryStatusServiceImpl.java:297] : query monitor count 1376.
2025-03-06 15:51:38.241 -ERROR - [DruidDataSource.java:2471] : create connection SQLException, url: ****************************************terhorn_pq, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:292)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:195)
	at org.postgresql.Driver.makeConnection(Driver.java:458)
	at org.postgresql.Driver.connect(Driver.java:260)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:156)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:218)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:150)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1560)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1623)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2468)
Caused by: java.io.EOFException: null
	at org.postgresql.core.PGStream.receiveChar(PGStream.java:323)
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:505)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:141)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:192)
	... 10 common frames omitted
2025-03-06 15:52:46.159 - INFO - [SpringApplication.java:651] : The following profiles are active: dev
2025-03-06 15:52:51.931 - INFO - [StartupInfoLogger.java:59] : Started PqScheduleServiceApplication in 7.266 seconds (JVM running for 8.328)
2025-03-06 15:54:05.152 - INFO - [PqHistoryStatusServiceImpl.java:297] : query monitor count 1376.
2025-03-06 15:54:08.712 - INFO - [PqHistoryStatusServiceImpl.java:80] : update pqhistorystatus count 5504.
2025-03-06 15:54:09.049 - INFO - [PqHistoryStatusServiceImpl.java:84] : update line count 1376.
2025-03-06 15:54:09.206 - INFO - [PqHistoryStatusServiceImpl.java:106] : no hidden line.
2025-03-06 15:54:34.379 - INFO - [PqHistoryStatusServiceImpl.java:297] : query monitor count 1376.
2025-03-06 15:54:38.503 - INFO - [PqHistoryStatusServiceImpl.java:80] : update pqhistorystatus count 5504.
2025-03-06 15:54:39.147 - INFO - [PqHistoryStatusServiceImpl.java:84] : update line count 1376.
2025-03-06 15:54:39.303 - INFO - [PqHistoryStatusServiceImpl.java:106] : no hidden line.
2025-03-06 16:00:00.004 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 16:00:00.005 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 16:00:04.440 - INFO - [PqHistoryStatusServiceImpl.java:297] : query monitor count 1376.
2025-03-06 16:00:10.333 - INFO - [PqHistoryStatusServiceImpl.java:80] : update pqhistorystatus count 5504.
2025-03-06 16:00:10.669 - INFO - [PqHistoryStatusServiceImpl.java:84] : update line count 1376.
2025-03-06 16:00:10.827 - INFO - [PqHistoryStatusServiceImpl.java:106] : no hidden line.
2025-03-06 16:30:00.006 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 16:30:00.007 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 16:30:00.007 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 16:30:00.600 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 16:30:00.814 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 16:30:00.869 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
2025-03-06 16:50:00.004 - INFO - [ProcedureTask.java:76] : start deal pq tolerance.
2025-03-06 16:50:00.005 - INFO - [ProcedureTask.java:78] : end deal pqEvent.
2025-03-06 17:00:00.016 - INFO - [ProcedureTask.java:54] : start deal pqhistory status.
2025-03-06 17:00:00.016 - INFO - [ProcedureTask.java:56] : end deal pqhistory status.
2025-03-06 17:00:07.181 - INFO - [PqHistoryStatusServiceImpl.java:297] : query monitor count 1377.
2025-03-06 17:00:11.263 - INFO - [PqHistoryStatusServiceImpl.java:80] : update pqhistorystatus count 5508.
2025-03-06 17:00:11.633 - INFO - [PqHistoryStatusServiceImpl.java:84] : update line count 1377.
2025-03-06 17:00:11.806 - INFO - [PqHistoryStatusServiceImpl.java:106] : no hidden line.
2025-03-06 17:30:00.002 - INFO - [ProcedureTask.java:65] : start deal pqEvent.
2025-03-06 17:30:00.003 - INFO - [ProcedureTask.java:67] : end deal pqEvent.
2025-03-06 17:30:00.034 - INFO - [PqEventServiceImpl.java:95] : start deal pq event.
2025-03-06 17:30:00.356 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 17:30:00.557 - INFO - [PqEventServiceImpl.java:2309] : read event and match wave count 0.
2025-03-06 17:30:00.607 - INFO - [PqEventServiceImpl.java:155] : event not related wave,event id null.
