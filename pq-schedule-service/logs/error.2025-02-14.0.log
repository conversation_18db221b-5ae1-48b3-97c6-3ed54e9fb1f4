2025-02-14 18:07:30.406 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 18:07:38.983 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 18:07:47.592 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 19:00:08.947 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 19:00:17.516 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 19:00:26.112 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 20:00:09.027 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 20:00:17.607 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 20:00:26.149 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 21:00:14.981 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 21:00:23.526 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 21:00:32.084 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 22:00:08.978 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 22:00:17.552 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 22:00:29.621 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 23:00:08.999 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 23:00:23.558 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-14 23:00:32.151 -ERROR - [HttpUtils.java:189] : http post error, url is http://************:4500/Cet/DataCache/QueryDataLogUpdateTime
java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at sun.reflect.GeneratedMethodAccessor105.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.commons.httpclient.protocol.ReflectionSocketFactory.createSocket(ReflectionSocketFactory.java:140)
	at org.apache.commons.httpclient.protocol.DefaultProtocolSocketFactory.createSocket(DefaultProtocolSocketFactory.java:125)
	at org.apache.commons.httpclient.HttpConnection.open(HttpConnection.java:707)
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:387)
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397)
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323)
	at com.cet.pqscheduleservice.utils.HttpUtils.post(HttpUtils.java:178)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.lambda$readDataLogCacheTime$3(PqHistoryStatusServiceImpl.java:151)
	at java.util.HashMap.forEach(HashMap.java:1289)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.readDataLogCacheTime(PqHistoryStatusServiceImpl.java:135)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealDataLogCache(PqHistoryStatusServiceImpl.java:126)
	at com.cet.pqscheduleservice.service.impl.PqHistoryStatusServiceImpl.dealHistoryStatus(PqHistoryStatusServiceImpl.java:69)
	at com.cet.pqscheduleservice.task.ProcedureTask.lambda$procedurePqHistoryStatus$2(ProcedureTask.java:55)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
