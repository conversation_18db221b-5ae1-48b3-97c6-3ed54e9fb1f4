FROM 10.12.135.233/base/java:1.8-alpine-withfont
#COPY --from=***********/base/arthas:v3.2.0 /opt/arthas /opt/arthas
VOLUME /tmp
ARG artifactId
ARG VERSION
ADD ./target/pq-schedule-service.jar /pq-schedule-service.jar
ENV JAVA_OPTS=""
ENV JAR_FILE_NAME pq-schedule-service
#Djava.security.egd  这个是用来防止springboot项目tomcat启动慢的问题（具体可搜索：随机数数与熵池策略）
ENTRYPOINT [ "sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 /$JAR_FILE_NAME.jar" ]